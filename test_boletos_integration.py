#!/usr/bin/env python3
"""
Teste simples para verificar se a integração de boletos está funcionando
"""

import sys
import os

# Adiciona o diretório raiz ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Testa se todas as importações necessárias estão funcionando"""
    try:
        from dependencies import buscar_boleto_por_codigo_barras
        print("✓ Função buscar_boleto_por_codigo_barras importada com sucesso")
        return True
    except ImportError as e:
        print(f"✗ Erro ao importar função: {e}")
        return False

def test_function_signature():
    """Testa se a função tem a assinatura correta"""
    try:
        from dependencies import buscar_boleto_por_codigo_barras
        import inspect
        
        sig = inspect.signature(buscar_boleto_por_codigo_barras)
        params = list(sig.parameters.keys())
        
        if 'codigo_barras' in params:
            print("✓ Função tem o parâmetro 'codigo_barras' correto")
            return True
        else:
            print(f"✗ Função não tem o parâmetro esperado. Parâmetros encontrados: {params}")
            return False
    except Exception as e:
        print(f"✗ Erro ao verificar assinatura da função: {e}")
        return False

def test_html_structure():
    """Verifica se o HTML foi modificado corretamente"""
    try:
        with open('templates/contas_pagamentos.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '<th>Boletos</th>' in content:
            print("✓ Coluna 'Boletos' adicionada ao HTML")
            return True
        else:
            print("✗ Coluna 'Boletos' não encontrada no HTML")
            return False
    except Exception as e:
        print(f"✗ Erro ao verificar HTML: {e}")
        return False

def test_js_functions():
    """Verifica se as funções JavaScript foram adicionadas"""
    try:
        with open('static/js/contas_home.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('renderBoletoButton', 'Função renderBoletoButton'),
            ('addBuscarBoletoEventListeners', 'Função addBuscarBoletoEventListeners'),
            ('buscarBoletoPorCodigoBarras', 'Função buscarBoletoPorCodigoBarras'),
            ('btn-buscar-boleto', 'Classe CSS btn-buscar-boleto')
        ]
        
        all_passed = True
        for check, description in checks:
            if check in content:
                print(f"✓ {description} encontrada no JavaScript")
            else:
                print(f"✗ {description} não encontrada no JavaScript")
                all_passed = False
        
        return all_passed
    except Exception as e:
        print(f"✗ Erro ao verificar JavaScript: {e}")
        return False

def main():
    """Executa todos os testes"""
    print("=== Teste de Integração - Funcionalidade de Boletos ===\n")
    
    tests = [
        ("Importações", test_imports),
        ("Assinatura da Função", test_function_signature),
        ("Estrutura HTML", test_html_structure),
        ("Funções JavaScript", test_js_functions)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Executando teste: {test_name}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    print("=== Resumo dos Testes ===")
    passed = 0
    for test_name, result in results:
        status = "PASSOU" if result else "FALHOU"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTestes passaram: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("\n🎉 Todos os testes passaram! A integração está funcionando corretamente.")
    else:
        print(f"\n⚠️  {len(tests) - passed} teste(s) falharam. Verifique os erros acima.")

if __name__ == "__main__":
    main()
