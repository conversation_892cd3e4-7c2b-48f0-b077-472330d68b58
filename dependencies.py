import datetime
from datetime import datetime, timedelta
import pandas as pd
import time
import psycopg2
from dotenv import load_dotenv
import os
from contextlib import contextmanager
import redis
import logging
import pytz
import numpy as np
import math
from dateutil.relativedelta import relativedelta

# Carrega variáveis de ambiente
load_dotenv()

# Configuração do Redis usando a URL fornecida pelo Heroku
redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
redis_client = redis.StrictRedis.from_url(redis_url, decode_responses=True)

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Configurações do banco de dados
DATABASE = os.getenv("DATABASE")
HOST = os.getenv("HOST")
USERSERVER = os.getenv("USERSERVER")
PASSWORD = os.getenv("PASSWORD")
PORT = 5432

# Função de log
def log(message):
    logging.info(f"[{datetime.now()}] {message}")


@contextmanager
def instance_cursor(max_retries=5, wait_time=5):
    retries = 0
    while retries < max_retries:
        try:
            connection = psycopg2.connect(
                database=DATABASE,
                host=HOST,
                user=USERSERVER,
                password=PASSWORD,
                port=PORT,
                connect_timeout=35,
            )
            cursor = connection.cursor()
            logging.info("Conexão com PostgreSQL estabelecida.")
            try:
                yield cursor
                connection.commit()
            finally:
                if connection:
                    cursor.close()
                    connection.close()
                    logging.info("Conexão com PostgreSQL encerrada.")
            break  # Sai do loop se a conexão for bem-sucedida
        except psycopg2.OperationalError as e:
            logging.error(f"Erro ao conectar ao PostgreSQL: {e}")
            retries += 1
            if retries < max_retries:
                logging.info(f"Tentando reconectar... ({retries}/{max_retries})")
                time.sleep(wait_time)
            else:
                logging.error("Número máximo de tentativas de conexão atingido.")
                raise
        except Exception as e:
            logging.error(f"Erro inesperado: {e}")
            raise


def insert_log(user_id, route, action, ip_address=None, windows_username=None):
    try:
        query = """
            INSERT INTO logs (user_id, route, action, ip_address, windows_username, created_at)
            VALUES (%s, %s, %s, %s, %s, (NOW() - INTERVAL '3 hours'))
        """
        with instance_cursor() as cursor:
            cursor.execute(query, (user_id, route, action, ip_address, windows_username))
            cursor.connection.commit()
    except Exception as e:
        logging.error(f"Erro ao inserir log: {e}")
        raise


def consulta_email(email):
    with instance_cursor() as cursor:
        query = """
            SELECT id, nome, email, senha, email_confirmed, tipo_usuario, ativo, setor_id, profile_image_url
            FROM users
            WHERE email = %s
        """
        cursor.execute(query, (email,))
        request = cursor.fetchall()
        return request


def consulta_cpf(cpf):
    with instance_cursor() as cursor:
        query = """
                SELECT nome, cpf, senha
                FROM Users
                WHERE cpf = %s
                """
        cursor.execute(query, (cpf,))
        request = cursor.fetchall()
        return request


def consulta_geral(user_id):
    with instance_cursor() as cursor:
        query = "SELECT id, tipo_usuario, nome, data_nascimento, cpf, email, ativo, telefone, unidade_id, equipe_id, profile_image_url, assistente, canal, setor_id FROM users WHERE id = %s"
        cursor.execute(query, (user_id,))
        result = cursor.fetchone()
        return result


def add_registro(
    tipo_usuario,
    nome,
    data_nascimento,
    cpf,
    email,
    senha,
    email_confirmed,
    telefone,
    unidade_id,
    equipe_id,
    ativo,
    profile_image_url=None,
):
    with instance_cursor() as cursor:
        query = """
                INSERT INTO users (tipo_usuario, nome, data_nascimento, cpf, email, senha, email_confirmed, telefone, unidade_id, equipe_id, ativo, profile_image_url)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
        cursor.execute(
            query,
            (
                tipo_usuario,
                nome,
                data_nascimento,
                cpf,
                email,
                senha,
                email_confirmed,
                telefone,
                unidade_id,
                equipe_id,
                ativo,
                profile_image_url,
            ),
        )
        logging.info("Registro adicionado com sucesso.")


def atualizar_senha(id_usuario, nova_senha):
    with instance_cursor() as cursor:
        query = """
                UPDATE Users
                SET senha = %s
                WHERE id = %s
                """
        cursor.execute(query, (nova_senha, id_usuario))
        cursor.connection.commit()
        logging.info("Senha atualizada com sucesso.")


def atualizar_cadastro(
    user_id, nome=None, email=None, telefone=None, profile_image_url=None
):
    with instance_cursor() as cursor:
        campos = []
        valores = []

        if (nome):
            campos.append("nome = %s")
            valores.append(nome)

        if (email):
            campos.append("email = %s")
            valores.append(email)

        if (telefone):
            campos.append("telefone = %s")
            valores.append(telefone)

        if (profile_image_url):
            campos.append("profile_image_url = %s")
            valores.append(profile_image_url)

        if (not campos):
            logging.info("Nenhum dado para atualizar.")
            return

        query = f"UPDATE users SET {', '.join(campos)} WHERE id = %s"
        valores.append(user_id)
        cursor.execute(query, valores)
        cursor.connection.commit()
        logging.info("Cadastro atualizado com sucesso.")


def consulta_pswd(user_id):
    with instance_cursor() as cursor:
        query = """
                SELECT id, senha
                FROM Users
                WHERE id = %s
                """
        cursor.execute(query, (user_id,))
        request = cursor.fetchone()
        return request


def confirm_user_email(user_id):
    with instance_cursor() as cursor:
        query = "UPDATE Users SET email_confirmed = TRUE WHERE id = %s"
        cursor.execute(query, (user_id,))
        cursor.connection.commit()


def consulta_usuarios(pagina, por_pagina=25, coluna_ordenacao="", direcao_ordenacao="asc", filtro_coluna="", filtro_valor=""):
    offset = (pagina - 1) * por_pagina
    with instance_cursor() as cursor:
        # Construir a query base
        query = """
                SELECT id, tipo_usuario, nome, data_nascimento, cpf, email, email_confirmed, telefone
                FROM Users
                """

        # Adicionar filtro se especificado
        params = []
        if filtro_coluna and filtro_valor:
            # Mapear nomes de colunas amigáveis para nomes reais no banco
            colunas_map = {
                "id": "id",
                "tipo_usuario": "tipo_usuario",
                "nome": "nome",
                "data_nascimento": "data_nascimento",
                "cpf": "cpf",
                "email": "email",
                "email_confirmed": "email_confirmed",
                "telefone": "telefone"
            }

            coluna_db = colunas_map.get(filtro_coluna)
            if coluna_db:
                query += f" WHERE {coluna_db} ILIKE %s"
                params.append(f"%{filtro_valor}%")

        # Mapear nomes de colunas amigáveis para nomes reais no banco
        colunas_map = {
            "id": "id",
            "tipo_usuario": "tipo_usuario",
            "nome": "nome",
            "data_nascimento": "data_nascimento",
            "cpf": "cpf",
            "email": "email",
            "email_confirmed": "email_confirmed",
            "telefone": "telefone"
        }

        # Adicionar ordenação
        coluna_db = colunas_map.get(coluna_ordenacao, "id")  # Se a coluna não existir, usa "id" como padrão
        query += f" ORDER BY {coluna_db} {direcao_ordenacao.upper()}"

        # Adicionar paginação
        query += " LIMIT %s OFFSET %s"
        params.extend([por_pagina, offset])

        cursor.execute(query, params)
        return cursor.fetchall()


def consulta_total_de_usuarios(filtro_coluna="", filtro_valor=""):
    with instance_cursor() as cursor:
        # Construir a query base
        query = "SELECT COUNT(*) FROM Users"

        # Adicionar filtro se especificado
        params = []
        if filtro_coluna and filtro_valor:
            # Mapear nomes de colunas amigáveis para nomes reais no banco
            colunas_map = {
                "id": "id",
                "tipo_usuario": "tipo_usuario",
                "nome": "nome",
                "data_nascimento": "data_nascimento",
                "cpf": "cpf",
                "email": "email",
                "email_confirmed": "email_confirmed",
                "telefone": "telefone"
            }

            coluna_db = colunas_map.get(filtro_coluna)
            if coluna_db:
                query += f" WHERE {coluna_db} ILIKE %s"
                params.append(f"%{filtro_valor}%")

        cursor.execute(query, params)
        total_de_usuarios = cursor.fetchone()[0]
        return total_de_usuarios

def consulta_valores_coluna(coluna):
    """
    Retorna todos os valores únicos de uma coluna específica da tabela Users.

    Args:
        coluna (str): Nome da coluna no banco de dados

    Returns:
        list: Lista de valores únicos da coluna
    """
    with instance_cursor() as cursor:
        query = f"SELECT DISTINCT {coluna} FROM Users WHERE {coluna} IS NOT NULL ORDER BY {coluna}"
        cursor.execute(query)
        valores = [row[0] for row in cursor.fetchall()]
        return valores


def atualizar_cadastro_master(
    id_usuario,
    novo_nome,
    nova_data_nascimento,
    novo_cpf,
    novo_email,
    novo_tipo_usuario,
    novo_telefone,
    novo_unidade,
    novo_equipe,
    ativo,
    profile_image_url=None,
    nova_senha_hash=None,
):
    with instance_cursor() as cursor:
        campos = []
        valores = []

        if (novo_nome):
            campos.append("nome = %s")
            valores.append(novo_nome)

        if (nova_data_nascimento):
            campos.append("data_nascimento = %s")
            valores.append(nova_data_nascimento)

        if (novo_cpf):
            campos.append("cpf = %s")
            valores.append(novo_cpf)

        if (novo_email):
            campos.append("email = %s")
            valores.append(novo_email)

        if (novo_tipo_usuario):
            campos.append("tipo_usuario = %s")
            valores.append(novo_tipo_usuario)

        if (novo_unidade):
            campos.append("unidade_id = %s")
            valores.append(novo_unidade)

        if (novo_equipe is not None):
            campos.append("equipe_id = %s")
            valores.append(novo_equipe)

        campos.append("ativo = %s")
        valores.append(ativo)

        if (novo_telefone):
            campos.append("telefone = %s")
            valores.append(novo_telefone)

        if (profile_image_url):
            campos.append("profile_image_url = %s")
            valores.append(profile_image_url)

        if (nova_senha_hash):
            campos.append("senha = %s")
            valores.append(nova_senha_hash)

        query = f"UPDATE Users SET {', '.join(campos)} WHERE id = %s"
        valores.append(id_usuario)
        cursor.execute(query, valores)
        cursor.connection.commit()
        logging.info("Cadastro atualizado com sucesso.")


def excluir_user(user_id):
    with instance_cursor() as cursor:
        query = """
                DELETE FROM Users
                WHERE id = %s
                """
        cursor.execute(query, (user_id,))
        cursor.connection.commit()


def verificar_conflito(sala_id, data_inicio, data_fim):
    with instance_cursor() as cursor:
        query = """
                SELECT COUNT(*) FROM reserva
                WHERE sala_id = %s AND NOT (
                    data_fim <= %s OR
                    data_inicio >= %s
                )
                """
        cursor.execute(query, (sala_id, data_inicio, data_fim))
        count = cursor.fetchone()[0]
        return count > 0


def add_evento(title, usuario_id, sala_id, data_inicio, data_fim, allDay):
    if (verificar_conflito(sala_id, data_inicio, data_fim)):
        raise ValueError("Conflito de horário encontrado.")

    with instance_cursor() as cursor:
        query = """
                INSERT INTO reserva (title, usuario_id, sala_id, data_inicio, data_fim, allDay)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
        cursor.execute(
            query, (title, usuario_id, sala_id, data_inicio, data_fim, allDay)
        )
        cursor.connection.commit()
        logging.info("Evento adicionado com sucesso.")


def listar_eventos(sala_id):
    with instance_cursor() as cursor:
        query = """
                SELECT id, title, data_inicio, data_fim, allDay
                FROM reserva
                WHERE sala_id = %s
                """
        cursor.execute(query, (sala_id,))
        eventos_db = cursor.fetchall()

        eventos = [
            {
                "id": evento[0],
                "title": evento[1],
                "start": evento[2].isoformat(),
                "end": evento[3].isoformat(),
                "allDay": evento[4],
            }
            for evento in eventos_db
        ]

        return eventos


def buscar_evento(evento_id):
    with instance_cursor() as cursor:
        query = """
                SELECT r.id, r.title, r.data_inicio, r.data_fim, r.allDay, u.nome AS usuario_nome, s.nome AS sala_nome, r.usuario_id
                FROM reserva r
                JOIN users u ON r.usuario_id = u.id
                JOIN sala s ON r.sala_id = s.id
                WHERE r.id = %s
                """
        cursor.execute(query, (evento_id,))
        evento = cursor.fetchone()

        if (evento):
            return {
                "id": evento[0],
                "title": evento[1],
                "data_inicio": evento[2].isoformat(),
                "data_fim": evento[3].isoformat(),
                "allDay": evento[4],
                "usuario_nome": evento[5],
                "sala_nome": evento[6],
                "usuario_id": evento[7],
            }
        return None


def atualizar_evento(evento_id, title, data_inicio, data_fim, allDay):
    with instance_cursor() as cursor:
        query = """
                UPDATE reserva
                SET title = %s, data_inicio = %s, data_fim = %s, allDay = %s
                WHERE id = %s
                """
        cursor.execute(query, (title, data_inicio, data_fim, allDay, evento_id))
        cursor.connection.commit()


def excluir_evento(evento_id):
    with instance_cursor() as cursor:
        query = """
                DELETE FROM reserva
                WHERE id = %s
                """
        cursor.execute(query, (evento_id,))
        cursor.connection.commit()


def add_informativo(titulo, conteudo, url_imagem, usuario_id):
    with instance_cursor() as cursor:
        query = """
            INSERT INTO Informativos (titulo, conteudo, url_imagem, usuario_id)
            VALUES (%s, %s, %s, %s)
        """
        cursor.execute(query, (titulo, conteudo, url_imagem, usuario_id))
        cursor.connection.commit()


def listar_informativos(limit=10, offset=0):
    with instance_cursor() as cursor:
        query = """
                SELECT id, titulo, conteudo, data_publicacao, url_imagem
                FROM Informativos
                ORDER BY data_publicacao DESC
                LIMIT %s OFFSET %s
                """
        cursor.execute(query, (limit, offset))
        result = cursor.fetchall()

        columns = [desc[0] for desc in cursor.description]
        return [dict(zip(columns, row)) for row in result]


def buscar_informativo(id):
    with instance_cursor() as cursor:
        query = """
                SELECT id, titulo, conteudo, url_imagem, data_publicacao
                FROM Informativos
                WHERE id = %s
                """
        cursor.execute(query, (id,))
        result = cursor.fetchone()
        if (result):
            columns = [desc[0] for desc in cursor.description]
            return dict(zip(columns, result))
        return None


def deletar_informativo(id):
    with instance_cursor() as cursor:
        query = """
                DELETE FROM Informativos
                WHERE id = %s
                """
        cursor.execute(query, (id,))
        if (cursor.rowcount):
            cursor.connection.commit()
            return True
        return False


def consulta_meses_anos():
    with instance_cursor() as cursor:
        query = """
            SELECT DISTINCT TO_CHAR(mesAno, 'MM/YYYY') as mesAno
            FROM sumario_assistente
            ORDER BY mesAno DESC
        """
        cursor.execute(query)
        resultado = cursor.fetchall()
        return [row[0] for row in resultado]


def consulta_ranking(mes_ano):
    with instance_cursor() as cursor:
        query = """
            SELECT
                assistente,
                SUM(vltotal) as vltotal,
                SUM(vlcontrato) as vlcontrato,
                SUM(vlvida) as vlvida,
                SUM(vidacount) as vidacount,
                SUM(corretorcount) as corretorcount,
                SUM(contratocount) as contratocount
            FROM sumario_assistente
            WHERE TO_CHAR(mesAno, 'MM/YYYY') = ANY(%s)
            GROUP BY assistente
        """
        cursor.execute(query, (mes_ano.split(","),))
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]
        return [dict(zip(colunas, row)) for row in resultado]


def consulta_ranking_individual(mes_ano, assistente_nome):
    with instance_cursor() as cursor:
        query = """
            SELECT
                assistente,
                SUM(vltotal) as vltotal,
                SUM(vlcontrato) as vlcontrato,
                SUM(vlvida) as vlvida,
                SUM(vidacount) as vidacount,
                SUM(corretorcount) as corretorcount,
                SUM(contratocount) as contratocount
            FROM sumario_assistente
            WHERE TO_CHAR(mesAno, 'MM/YYYY') = ANY(%s)
            AND assistente = %s
            GROUP BY assistente
        """
        cursor.execute(query, (mes_ano.split(","), assistente_nome))
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]
        return [dict(zip(colunas, row)) for row in resultado]


def update_or_insert_data(data):
    with instance_cursor() as cursor:
        for item in data:
            try:
                mes_ano = (
                    f"{item['mesAno'].split('-')[0]}-{item['mesAno'].split('-')[1]}-01"
                )

                query = """
                INSERT INTO public.sumario_assistente (
                    assistente, mesano, vltotal, vlcontrato, vlvida, vlreceber, vlpagar, vllucro, vllucropercentual, vllucrototalpercentual, corretorcount, contratocount, vidacount, contratoporcorretor
                ) VALUES (
                    %(assistente)s, %(mesano)s, %(vltotal)s, %(vlcontrato)s, %(vlvida)s, %(vlreceber)s, %(vlpagar)s, %(vllucro)s, %(vllucropercentual)s, %(vllucrototalpercentual)s, %(corretorcount)s, %(contratocount)s, %(vidacount)s, %(contratoporcorretor)s
                )
                ON CONFLICT (assistente, mesano)
                DO UPDATE SET
                    vltotal = EXCLUDED.vltotal,
                    vlcontrato = EXCLUDED.vlcontrato,
                    vlvida = EXCLUDED.vlvida,
                    vlreceber = EXCLUDED.vlreceber,
                    vlpagar = EXCLUDED.vlpagar,
                    vllucro = EXCLUDED.vllucro,
                    vllucropercentual = EXCLUDED.vllucropercentual,
                    vllucrototalpercentual = EXCLUDED.vllucrototalpercentual,
                    corretorcount = EXCLUDED.corretorcount,
                    contratocount = EXCLUDED.contratocount,
                    vidacount = EXCLUDED.vidacount,
                    contratoporcorretor = EXCLUDED.contratoporcorretor;
                """

                query_params = {
                    "assistente": item["assistente"],
                    "mesano": mes_ano,
                    "vltotal": item["vlTotal"],
                    "vlcontrato": item["vlContrato"],
                    "vlvida": item["vlVida"],
                    "vlreceber": item["vlReceber"],
                    "vlpagar": item["vlPagar"],
                    "vllucro": item["vlLucro"],
                    "vllucropercentual": item["vlLucroPercentual"],
                    "vllucrototalpercentual": item["vlLucroTotalPercentual"],
                    "corretorcount": item["corretorCount"],
                    "contratocount": item["contratoCount"],
                    "vidacount": item["vidaCount"],
                    "contratoporcorretor": item["contratoPorCorretor"],
                }

                cursor.execute(query, query_params)
                cursor.connection.commit()  # Certificar que mudanças são salvas

            except Exception as e:
                # Aqui você pode decidir logar erros críticos se necessário
                pass


def consulta_mesesanos_empresarial():
    with instance_cursor() as cursor:
        query = """
            SELECT DISTINCT TO_CHAR(mesAno, 'MM/YYYY') as mesAno
            FROM sumario_empresarial
            ORDER BY mesAno DESC
        """
        cursor.execute(query)
        resultado = cursor.fetchall()
        return [row[0] for row in resultado]


def consulta_empresarial(mes_ano):
    with instance_cursor() as cursor:
        query = """
            SELECT assistente,
                   mesAno,
                   SUM(vltotal) as vltotal,
                   SUM(vlcontrato) as vlcontrato,
                   SUM(vlvida) as vlvida,
                   SUM(vlreceber) as vlreceber,
                   SUM(vlpagar) as vlpagar,
                   SUM(vllucro) as vllucro,
                   SUM(vllucropercentual) as vllucropercentual,
                   SUM(vllucrototalpercentual) as vllucrototalpercentual,
                   SUM(corretorcount) as corretorcount,
                   SUM(contratocount) as contratocount,
                   SUM(vidacount) as vidacount,
                   SUM(contratoporcorretor) as contratoporcorretor
            FROM sumario_empresarial
            WHERE TO_CHAR(mesAno, 'MM/YYYY') = ANY(%s)
            GROUP BY assistente, mesAno
        """
        cursor.execute(query, (mes_ano.split(","),))
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]
        return [dict(zip(colunas, row)) for row in resultado]


def consulta_empresarial_individual(mes_ano, assistente_nome):
    with instance_cursor() as cursor:
        # Divida mes_ano em uma lista e remova espaços extras
        meses_lista = [mes.strip() for mes in mes_ano.split(",")]

        # Crie a cláusula IN com placeholders para os parâmetros
        meses_in_clause = ', '.join(['%s'] * len(meses_lista))

        query = f"""
            SELECT
                assistente,
                SUM(vltotal) as vltotal,
                SUM(vlcontrato) as vlcontrato,
                SUM(vlvida) as vlvida,
                SUM(vidacount) as vidacount,
                SUM(corretorcount) as corretorcount,
                SUM(contratocount) as contratocount
            FROM sumario_empresarial
            WHERE TO_CHAR(mesAno, 'MM/YYYY') IN ({meses_in_clause})
            AND assistente = %s
            GROUP BY assistente
        """

        # Crie a lista de parâmetros, incluindo os meses e o nome do assistente
        parametros = meses_lista + [assistente_nome]

        cursor.execute(query, parametros)
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]
        return [dict(zip(colunas, row)) for row in resultado]


def update_or_insert_empresarial(data):
    with instance_cursor() as cursor:
        for item in data:
            try:
                mes_ano = item["mesAno"]

                query = """
                INSERT INTO public.sumario_empresarial (
                    assistente, mesano, vltotal, vlcontrato, vlvida, vlreceber, vlpagar, vllucro, vllucropercentual, vllucrototalpercentual, corretorcount, contratocount, vidacount, contratoporcorretor
                ) VALUES (
                    %(assistente)s, %(mesano)s, %(vltotal)s, %(vlcontrato)s, %(vlvida)s, %(vlreceber)s, %(vlpagar)s, %(vllucro)s, %(vllucropercentual)s, %(vllucrototalpercentual)s, %(corretorcount)s, %(contratocount)s, %(vidacount)s, %(contratoporcorretor)s
                )
                ON CONFLICT (assistente, mesano)
                DO UPDATE SET
                    vltotal = EXCLUDED.vltotal,
                    vlcontrato = EXCLUDED.vlcontrato,
                    vlvida = EXCLUDED.vlvida,
                    vlreceber = EXCLUDED.vlreceber,
                    vlpagar = EXCLUDED.vlpagar,
                    vllucro = EXCLUDED.vllucro,
                    vllucropercentual = EXCLUDED.vllucropercentual,
                    vllucrototalpercentual = EXCLUDED.vllucrototalpercentual,
                    corretorcount = EXCLUDED.corretorcount,
                    contratocount = EXCLUDED.contratocount,
                    vidacount = EXCLUDED.vidacount,
                    contratoporcorretor = EXCLUDED.contratoporcorretor;
                """

                query_params = {
                    "assistente": item["assistente"],
                    "mesano": mes_ano,
                    "vltotal": item["vlTotal"],
                    "vlcontrato": item["vlContrato"],
                    "vlvida": item["vlVida"],
                    "vlreceber": item["vlReceber"],
                    "vlpagar": item["vlPagar"],
                    "vllucro": item["vlLucro"],
                    "vllucropercentual": item["vlLucroPercentual"],
                    "vllucrototalpercentual": item["vlLucroTotalPercentual"],
                    "corretorcount": item["corretorCount"],
                    "contratocount": item["contratoCount"],
                    "vidacount": item["vidaCount"],
                    "contratoporcorretor": item["contratoPorCorretor"],
                }

                logging.info("Executando query: ", query)
                logging.info("Com parâmetros: ", query_params)

                cursor.execute(query, query_params)
                cursor.connection.commit()  # Certificar que mudanças são salvas
                logging.info("Dados atualizados com sucesso.")
            except Exception as e:
                logging.info(f"Erro ao inserir ou atualizar dados: {e}")
                pass


def consulta_mesesanos_operadora():
    with instance_cursor() as cursor:
        query = """
            SELECT DISTINCT TO_CHAR(mesAno, 'MM/YYYY') as mesAno
            FROM sumario_operadora
            ORDER BY mesAno DESC
        """
        cursor.execute(query)
        resultado = cursor.fetchall()
        return [row[0] for row in resultado]


def consulta_operadora(mes_ano):
    with instance_cursor() as cursor:
        query = """
            SELECT operadoranome AS operadora,
                   mesano,
                   SUM(vltotal) as vltotal,
                   SUM(vlcontrato) as vlcontrato,
                   SUM(vlvida) as vlvida,
                   SUM(vlreceber) as vlreceber,
                   SUM(vlpagar) as vlpagar,
                   SUM(vllucro) as vllucro,
                   SUM(vllucropercentual) as vllucropercentual,
                   SUM(vllucrototalpercentual) as vllucrototalpercentual,
                   SUM(corretorcount) as corretorcount,
                   SUM(contratocount) as contratocount,
                   SUM(vidacount) as vidacount,
                   SUM(contratoporcorretor) as contratoporcorretor
            FROM sumario_operadora
            WHERE TO_CHAR(mesAno, 'MM/YYYY') = ANY(%s)
            GROUP BY operadoranome, mesano
        """
        cursor.execute(query, (mes_ano.split(","),))
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]
        return [dict(zip(colunas, row)) for row in resultado]


def update_or_insert_operadora(data):
    with instance_cursor() as cursor:
        for item in data:
            try:
                mes_ano = item["mesAno"]

                query = """
                INSERT INTO public.sumario_operadora (
                    operadoraNome, mesano, vltotal, vlcontrato, vlvida, vlreceber, vlpagar, vllucro, vllucropercentual, vllucrototalpercentual, corretorcount, contratocount, vidacount, contratoporcorretor
                ) VALUES (
                    %(operadoraNome)s, %(mesano)s, %(vltotal)s, %(vlcontrato)s, %(vlvida)s, %(vlreceber)s, %(vlpagar)s, %(vllucro)s, %(vllucropercentual)s, %(vllucrototalpercentual)s, %(corretorcount)s, %(contratocount)s, %(vidacount)s, %(contratoporcorretor)s
                )
                ON CONFLICT (operadoraNome, mesano)
                DO UPDATE SET
                    vltotal = EXCLUDED.vltotal,
                    vlcontrato = EXCLUDED.vlcontrato,
                    vlvida = EXCLUDED.vlvida,
                    vlreceber = EXCLUDED.vlreceber,
                    vlpagar = EXCLUDED.vlpagar,
                    vllucro = EXCLUDED.vllucro,
                    vllucropercentual = EXCLUDED.vllucropercentual,
                    vllucrototalpercentual = EXCLUDED.vllucrototalpercentual,
                    corretorcount = EXCLUDED.corretorcount,
                    contratocount = EXCLUDED.contratocount,
                    vidacount = EXCLUDED.vidacount,
                    contratoporcorretor = EXCLUDED.contratoporcorretor;
                """

                query_params = {
                    "operadoraNome": item["operadoraNome"],
                    "mesano": mes_ano,
                    "vltotal": item["vlTotal"],
                    "vlcontrato": item["vlContrato"],
                    "vlvida": item["vlVida"],
                    "vlreceber": item["vlReceber"],
                    "vlpagar": item["vlPagar"],
                    "vllucro": item["vlLucro"],
                    "vllucropercentual": item["vlLucroPercentual"],
                    "vllucrototalpercentual": item["vlLucroTotalPercentual"],
                    "corretorcount": item["corretorCount"],
                    "contratocount": item["contratoCount"],
                    "vidacount": item["vidaCount"],
                    "contratoporcorretor": item["contratoPorCorretor"],
                }

                cursor.execute(query, query_params)
                cursor.connection.commit()  # Certificar que mudanças são salvas
                logging.info("Dados atualizados com sucesso.")
            except Exception as e:
                logging.info(f"Erro ao inserir ou atualizar dados: {e}")
                pass

    with instance_cursor() as cursor:
        for item in data:
            try:
                mes_ano = (
                    f"{item['mesAno'].split('-')[0]}-{item['mesAno'].split('-')[1]}-01"
                )

                query = """
                INSERT INTO public.sumario_assistente (
                    assistente, mesano, vltotal, vlcontrato, vlvida, vlreceber, vlpagar, vllucro, vllucropercentual, vllucrototalpercentual, corretorcount, contratocount, vidacount, contratoporcorretor
                ) VALUES (
                    %(assistente)s, %(mesano)s, %(vltotal)s, %(vlcontrato)s, %(vlvida)s, %(vlreceber)s, %(vlpagar)s, %(vllucro)s, %(vllucropercentual)s, %(vllucrototalpercentual)s, %(corretorcount)s, %(contratocount)s, %(vidacount)s, %(contratoporcorretor)s
                )
                ON CONFLICT (assistente, mesano)
                DO UPDATE SET
                    vltotal = EXCLUDED.vltotal,
                    vlcontrato = EXCLUDED.vlcontrato,
                    vlvida = EXCLUDED.vlvida,
                    vlreceber = EXCLUDED.vlreceber,
                    vlpagar = EXCLUDED.vlpagar,
                    vllucro = EXCLUDED.vllucro,
                    vllucropercentual = EXCLUDED.vllucropercentual,
                    vllucrototalpercentual = EXCLUDED.vllucrototalpercentual,
                    corretorcount = EXCLUDED.corretorcount,
                    contratocount = EXCLUDED.contratocount,
                    vidacount = EXCLUDED.vidacount,
                    contratoporcorretor = EXCLUDED.contratoporcorretor;
                """

                query_params = {
                    "assistente": item["assistente"],
                    "mesano": mes_ano,
                    "vltotal": item["vlTotal"],
                    "vlcontrato": item["vlContrato"],
                    "vlvida": item["vlVida"],
                    "vlreceber": item["vlReceber"],
                    "vlpagar": item["vlPagar"],
                    "vllucro": item["vlLucro"],
                    "vllucropercentual": item["vlLucroPercentual"],
                    "vllucrototalpercentual": item["vlLucroTotalPercentual"],
                    "corretorcount": item["corretorCount"],
                    "contratocount": item["contratoCount"],
                    "vidacount": item["vidaCount"],
                    "contratoporcorretor": item["contratoPorCorretor"],
                }

                cursor.execute(query, query_params)
                cursor.connection.commit()  # Certificar que mudanças são salvas

            except Exception as e:
                # Aqui você pode decidir logar erros críticos se necessário
                pass


def consulta_mesesanos_unidades():
    with instance_cursor() as cursor:
        query = """
            SELECT DISTINCT TO_CHAR(mesAno, 'MM/YYYY') as mesAno
            FROM sumario_assistente
            ORDER BY mesAno DESC
        """
        cursor.execute(query)
        resultado = cursor.fetchall()
        return [row[0] for row in resultado]


def consulta_unidades(mes_ano):
    if not mes_ano:
        raise ValueError("O parâmetro mes_ano não pode estar vazio")

    # Garante que mes_ano seja uma string com aspas
    mes_ano_str = f"'{mes_ano}'" if isinstance(mes_ano, str) else mes_ano

    query = f"""
    SELECT
        assistente_grupo,
        mesano,
        SUM(vltotal) AS total_vltotal,
        SUM(vlcontrato) AS total_vlcontrato,
        SUM(vlvida) AS total_vlvida,
        SUM(vlreceber) AS total_vlreceber,
        SUM(vlpagar) AS total_vlpagar,
        SUM(vllucro) AS total_vllucro,
        AVG(vllucropercentual) AS avg_vllucropercentual,
        AVG(vllucrototalpercentual) AS avg_vllucrototalpercentual,
        SUM(corretorcount) AS total_corretorcount,
        SUM(contratocount) AS total_contratocount,
        SUM(vidacount) AS total_vidacount,
        AVG(contratoporcorretor) AS avg_contratoporcorretor
    FROM (
        SELECT
            CASE
                WHEN assistente ILIKE '%RJ%' THEN 'BRAZIL HEALTH RJ'
                WHEN assistente ILIKE '%RIO%' THEN 'BRAZIL HEALTH RJ'
                WHEN assistente ILIKE '%BEATRIZ FIGUEIREDO ALVES%' THEN 'BRAZIL HEALTH RJ'
                WHEN assistente ILIKE '%GUILHERME PEREIRA FILHO%' THEN 'BRAZIL HEALTH RJ'
                WHEN assistente ILIKE '%BRAZIL HEALTH RJ ASSESSORIA EM SEGUROS LTDA%' THEN 'BRAZIL HEALTH RJ'
                WHEN assistente ILIKE '%Luanca%' THEN 'LUANCA BRH'
                WHEN assistente ILIKE '%Brazil Call%' THEN 'BRAZIL CALL'
                WHEN assistente ILIKE '%Solution%' THEN 'BRH SOLUTION'
                WHEN assistente ILIKE '%Campinas%' THEN 'BRAZIL HEALTH CAMPINAS'
                WHEN assistente ILIKE '%Confiance%' THEN 'CONFIANCE BRH'
                ELSE 'OUTRO'
            END AS assistente_grupo,
            mesano,
            vltotal,
            vlcontrato,
            vlvida,
            vlreceber,
            vlpagar,
            vllucro,
            vllucropercentual,
            vllucrototalpercentual,
            corretorcount,
            contratocount,
            vidacount,
            contratoporcorretor
        FROM public.sumario_assistente
        WHERE TO_CHAR(mesano, 'MM/YYYY') IN ({mes_ano})
    ) AS filtered
    GROUP BY assistente_grupo, mesano
    ORDER BY assistente_grupo, mesano;
    """

    with instance_cursor() as cursor:
        try:
            cursor.execute(query)
            resultado = cursor.fetchall()
            colunas = [desc[0] for desc in cursor.description]
            return [dict(zip(colunas, row)) for row in resultado]
        except Exception as e:
            logging.error(f"Erro ao executar a query: {e}")
            raise


UNIDADES_ILIKE_MAP = {
    9: [('%RJ%', 'BRAZIL HEALTH RJ'),
        ('%RIO%', 'BRAZIL HEALTH RJ'),
        ('%BEATRIZ FIGUEIREDO ALVES%', 'BRAZIL HEALTH RJ'),
        ('%GUILHERME PEREIRA FILHO%', 'BRAZIL HEALTH RJ'),
        ('%BRAZIL HEALTH RJ ASSESSORIA EM SEGUROS LTDA%', 'BRAZIL HEALTH RJ')],
    3: [('%Solution%', 'BRH SOLUTION')],
    4: [('%Confiance%', 'CONFIANCE BRH')],
    5: [('%Luanca%', 'LUANCA BRH')],
    7: [('%Brazil Call%', 'BRAZIL CALL')],
    10: [('%Campinas%', 'BRAZIL HEALTH CAMPINAS')],
}

def montar_case_unidade(unidade_id):
    conditions = UNIDADES_ILIKE_MAP.get(unidade_id, [])
    case_sql = ""
    for ilike, label in conditions:
        case_sql += f"WHEN assistente ILIKE '{ilike}' THEN '{label}'\n"
    case_sql += "ELSE 'OUTRO'"
    return case_sql


def consulta_unidades_usuario(mes_ano, unidade_id):
    if not mes_ano:
        raise ValueError("O parâmetro mes_ano não pode estar vazio")

    case_when = montar_case_unidade(unidade_id)

    # Garante que mes_ano seja uma string com aspas
    mes_ano_str = f"'{mes_ano}'" if isinstance(mes_ano, str) else mes_ano

    query = f"""
    SELECT
        assistente_grupo,
        mesano,
        SUM(vltotal) AS total_vltotal,
        SUM(vlcontrato) AS total_vlcontrato,
        SUM(vlvida) AS total_vlvida,
        SUM(vlreceber) AS total_vlreceber,
        SUM(vlpagar) AS total_vlpagar,
        SUM(vllucro) AS total_vllucro,
        AVG(vllucropercentual) AS avg_vllucropercentual,
        AVG(vllucrototalpercentual) AS avg_vllucrototalpercentual,
        SUM(corretorcount) AS total_corretorcount,
        SUM(contratocount) AS total_contratocount,
        SUM(vidacount) AS total_vidacount,
        AVG(contratoporcorretor) AS avg_contratoporcorretor
    FROM (
        SELECT
            CASE
                {case_when}
            END AS assistente_grupo,
            mesano,
            vltotal,
            vlcontrato,
            vlvida,
            vlreceber,
            vlpagar,
            vllucro,
            vllucropercentual,
            vllucrototalpercentual,
            corretorcount,
            contratocount,
            vidacount,
            contratoporcorretor
        FROM public.sumario_assistente
        WHERE TO_CHAR(mesano, 'MM/YYYY') IN ({mes_ano})
    ) AS filtered
    GROUP BY assistente_grupo, mesano
    ORDER BY assistente_grupo, mesano;
    """

    with instance_cursor() as cursor:
        try:
            cursor.execute(query)
            resultado = cursor.fetchall()
            colunas = [desc[0] for desc in cursor.description]
            return [dict(zip(colunas, row)) for row in resultado]
        except Exception as e:
            logging.info(f"Erro ao executar a query: {e}")
            raise


def consulta_comparativa(mes_ano_anterior, mes_ano_atual):
    # Converte o formato MM/YYYY para YYYY-MM-DD
    mes_ano_anterior_date = f"{mes_ano_anterior.split('/')[1]}-{mes_ano_anterior.split('/')[0]}-01"
    mes_ano_atual_date = f"{mes_ano_atual.split('/')[1]}-{mes_ano_atual.split('/')[0]}-01"

    query = f"""
    SELECT
        a.assistente,
        b.mesano AS mes_ano_atual,
        b.vltotal AS valor_atual,
        a.mesano AS mes_ano_anterior,
        a.vltotal AS valor_anterior,
        (b.vltotal - a.vltotal) AS diferenca_valor,
        ((b.vltotal - a.vltotal) / NULLIF(a.vltotal, 0)) * 100 AS diferenca_percentual
    FROM
        public.sumario_assistente a
    JOIN
        public.sumario_assistente b
    ON
        a.assistente = b.assistente
    WHERE
        a.mesano = '{mes_ano_anterior_date}'  -- mês anterior
        AND b.mesano = '{mes_ano_atual_date}'  -- mês atual
    """
    with instance_cursor() as cursor:
        cursor.execute(query)
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]

        # Formatar os resultados e converter valores decimais para float
        formatted_result = [
            {
                "assistente": row[0],
                "mes_ano_atual": row[1],
                "valor_atual": float(row[2]),  # Converte decimal para float
                "mes_ano_anterior": row[3],
                "valor_anterior": float(row[4]),  # Converte decimal para float
                "diferenca_valor": float(row[5]),  # Converte decimal para float
                "diferenca_percentual": float(row[6]) if row[6] is not None else 0  # Converte decimal para float e trata nulos
            }
            for row in resultado
        ]

        return formatted_result


def consulta_comparativa_individual(mes_ano_anterior, mes_ano_atual, assistente_nome):
    # Converte o formato MM/YYYY para YYYY-MM-DD
    mes_ano_anterior_date = f"{mes_ano_anterior.split('/')[1]}-{mes_ano_anterior.split('/')[0]}-01"
    mes_ano_atual_date = f"{mes_ano_atual.split('/')[1]}-{mes_ano_atual.split('/')[0]}-01"

    query = f"""
    SELECT
        a.assistente,
        b.mesano AS mes_ano_atual,
        b.vltotal AS valor_atual,
        a.mesano AS mes_ano_anterior,
        a.vltotal AS valor_anterior,
        (b.vltotal - a.vltotal) AS diferenca_valor,
        ((b.vltotal - a.vltotal) / NULLIF(a.vltotal, 0)) * 100 AS diferenca_percentual
    FROM
        public.sumario_assistente a
    JOIN
        public.sumario_assistente b
    ON
        a.assistente = b.assistente
    WHERE
        a.mesano = '{mes_ano_anterior_date}'  -- mês anterior
        AND b.mesano = '{mes_ano_atual_date}'  -- mês atual
        AND a.assistente = %s  -- nome do assistente
    """
    with instance_cursor() as cursor:
        cursor.execute(query, (assistente_nome,))
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]

        # Formatar os resultados e converter valores decimais para float
        formatted_result = [
            {
                "assistente": row[0],
                "mes_ano_atual": row[1],
                "valor_atual": float(row[2]),  # Converte decimal para float
                "mes_ano_anterior": row[3],
                "valor_anterior": float(row[4]),  # Converte decimal para float
                "diferenca_valor": float(row[5]),  # Converte decimal para float
                "diferenca_percentual": float(row[6]) if row[6] is not None else 0  # Converte decimal para float e trata nulos
            }
            for row in resultado
        ]

        return formatted_result


def update_sumario_op_ass(data):
    with instance_cursor() as cursor:
        try:
            # Log de início do processo
            logging.info("Iniciando atualização da tabela sumario_op_ass")

            # Apagar os dados existentes
            logging.info("Deletando dados existentes...")
            cursor.execute("DELETE FROM sumario_op_ass")

            # Inserir os novos dados
            insert_query = """
                INSERT INTO sumario_op_ass (
                    assistente, operadoraNome, vlTotal
                ) VALUES (
                    %(assistente)s, %(operadoraNome)s, %(vlTotal)s
                )
            """
            for item in data:
                cursor.execute(insert_query, item)

            # Log de finalização da inserção
            logging.info("Inserindo novos dados...")

            cursor.connection.commit()
            logging.info("Dados atualizados com sucesso no banco de dados.")
        except Exception as e:
            cursor.connection.rollback()
            logging.info(f"Erro ao atualizar banco de dados: {e}")
            raise e


def update_sumario_op_cor(data):
    with instance_cursor() as cursor:
        try:
            # Apagar os dados existentes
            cursor.execute("DELETE FROM sumario_op_cor")

            # Inserir os novos dados
            insert_query = """
                INSERT INTO sumario_op_cor (
                    assistente, corretor, operadoraNome, vlTotal
                ) VALUES (
                    %(assistente)s, %(corretor)s, %(operadoraNome)s, %(vlTotal)s
                )
            """
            for item in data:
                cursor.execute(insert_query, item)

            cursor.connection.commit()
        except Exception as e:
            cursor.connection.rollback()
            raise e


def consulta_ranking_op_ass():
    with instance_cursor() as cursor:
        query = """
            SELECT
                assistente,
                operadoranome,
                vlTotal
            FROM sumario_op_ass
            ORDER BY operadoranome, vlTotal DESC
        """
        cursor.execute(query)
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]
        return [dict(zip(colunas, row)) for row in resultado]


def consulta_ranking_op_cor():
    with instance_cursor() as cursor:
        query = """
            SELECT
                assistente,
                corretor,
                operadoranome,
                vlTotal
            FROM sumario_op_cor
            ORDER BY operadoranome, vlTotal DESC
        """
        cursor.execute(query)
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]
        return [dict(zip(colunas, row)) for row in resultado]


def consulta_operadora_top6(mes_ano):
    with instance_cursor() as cursor:
        query = """
            SELECT operadoranome AS operadora,
                   mesano,
                   SUM(vltotal) as vltotal
            FROM sumario_operadora
            WHERE TO_CHAR(mesAno, 'MM/YYYY') = %s
            GROUP BY operadoranome, mesano
            ORDER BY SUM(vltotal) DESC
            LIMIT 6
        """
        cursor.execute(query, (mes_ano,))
        resultado = cursor.fetchall()
        colunas = [desc[0] for desc in cursor.description]
        return [dict(zip(colunas, row)) for row in resultado]


def aniversariantes_mes():
    with instance_cursor() as cursor:
        query = """
            SELECT id, nome, data_nascimento, unidade_id, ativo, profile_image_url
            FROM public.users
            WHERE ativo = True;
        """
        cursor.execute(query)
        resultado = cursor.fetchall()
        aniversariantes = []
        for row in resultado:
            aniversariantes.append(
                {
                    "id": row[0],
                    "nome": row[1],
                    "data_nascimento": row[2].strftime("%Y-%m-%d"),
                    "unidade_id": row[3],
                    "ativo": row[4],
                    "profile_image_url": row[5],
                }
            )
        return aniversariantes


def insert_comissoes(data):
    with instance_cursor() as cursor:
        try:
            update_query = """
                UPDATE public.grade_comissoes
                SET grupo = %s, totais = %s, parcela_1 = %s, parcela_2 = %s, parcela_3 = %s, parcela_4 = %s, parcela_5 = %s,
                    parcela_6 = %s, parcela_7 = %s, parcela_8 = %s, parcela_9 = %s, parcela_10 = %s, parcela_11 = %s, parcela_12 = %s,
                    parcela_13 = %s, parcela_14 = %s, parcela_15 = %s, parcela_16 = %s, parcela_17 = %s, parcela_18 = %s, parcela_19 = %s,
                    parcela_20 = %s, parcela_21 = %s, parcela_22 = %s, parcela_23 = %s, parcela_24 = %s, parcela_25 = %s, parcela_26 = %s,
                    parcela_27 = %s, parcela_28 = %s, parcela_29 = %s, parcela_30 = %s
                WHERE modalidade = %s AND operadora = %s AND grade = %s AND comissionaveis = %s
            """

            insert_query = """
                INSERT INTO public.grade_comissoes (
                    operadora, modalidade, grupo, grade, comissionaveis, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7,
                    parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, parcela_13, parcela_14, parcela_15, parcela_16, parcela_17, parcela_18, parcela_19,
                    parcela_20, parcela_21, parcela_22, parcela_23, parcela_24, parcela_25, parcela_26, parcela_27, parcela_28, parcela_29, parcela_30
                ) VALUES (
                    %(Operadora)s, %(Modalidade)s, %(Grupo)s, %(Grade)s, %(Comissionaveis)s, %(Totais)s, %(parcela_1)s, %(parcela_2)s, %(parcela_3)s,
                    %(parcela_4)s, %(parcela_5)s, %(parcela_6)s, %(parcela_7)s, %(parcela_8)s, %(parcela_9)s, %(parcela_10)s, %(parcela_11)s, %(parcela_12)s,
                    %(parcela_13)s, %(parcela_14)s, %(parcela_15)s, %(parcela_16)s, %(parcela_17)s, %(parcela_18)s, %(parcela_19)s, %(parcela_20)s, %(parcela_21)s,
                    %(parcela_22)s, %(parcela_23)s, %(parcela_24)s, %(parcela_25)s, %(parcela_26)s, %(parcela_27)s, %(parcela_28)s, %(parcela_29)s, %(parcela_30)s
                )
            """

            processed_records = set()

            for item in data:
                modalidade = item.get('Modalidade')
                operadora = item.get('Operadora')
                grupo = item.get('Grupo')
                grade = item.get('Grade')
                comissionaveis = item.get('Comissionaveis')

                record_key = (modalidade, operadora, grade, comissionaveis)

                # Verifica se o registro já foi processado
                if (record_key in processed_records):
                    logging.info(f"Registro já processado: {record_key}, pulando atualização.")
                    continue

                logging.info(f"Tentando atualizar: modalidade={modalidade}, operadora={operadora}, grade={grade}, comissionaveis={comissionaveis}")

                update_values = (
                    grupo, item.get('Totais'), item.get('parcela_1'), item.get('parcela_2'), item.get('parcela_3'), item.get('parcela_4'),
                    item.get('parcela_5'), item.get('parcela_6'), item.get('parcela_7'), item.get('parcela_8'), item.get('parcela_9'),
                    item.get('parcela_10'), item.get('parcela_11'), item.get('parcela_12'), item.get('parcela_13'), item.get('parcela_14'),
                    item.get('parcela_15'), item.get('parcela_16'), item.get('parcela_17'), item.get('parcela_18'), item.get('parcela_19'),
                    item.get('parcela_20'), item.get('parcela_21'), item.get('parcela_22'), item.get('parcela_23'), item.get('parcela_24'),
                    item.get('parcela_25'), item.get('parcela_26'), item.get('parcela_27'), item.get('parcela_28'), item.get('parcela_29'),
                    item.get('parcela_30'), modalidade, operadora, grade, comissionaveis
                )

                cursor.execute(update_query, update_values)
                logging.info(f"Linha(s) afetada(s) pelo update: {cursor.rowcount}")

                if (cursor.rowcount == 0):
                    # Se nenhuma linha foi atualizada, insere um novo registro
                    logging.info(f"Nenhuma linha encontrada para atualizar. Inserindo novo registro: {item}")
                    required_fields = [
                        'Operadora', 'Modalidade', 'Grupo', 'Grade', 'Comissionaveis', 'Totais', 'parcela_1', 'parcela_2', 'parcela_3', 'parcela_4', 'parcela_5',
                        'parcela_6', 'parcela_7', 'parcela_8', 'parcela_9', 'parcela_10', 'parcela_11', 'parcela_12', 'parcela_13', 'parcela_14', 'parcela_15',
                        'parcela_16', 'parcela_17', 'parcela_18', 'parcela_19', 'parcela_20', 'parcela_21', 'parcela_22', 'parcela_23', 'parcela_24', 'parcela_25',
                        'parcela_26', 'parcela_27', 'parcela_28', 'parcela_29', 'parcela_30'
                    ]
                    for field in required_fields:
                        if (field not in item):
                            item[field] = None
                    cursor.execute(insert_query, item)
                    logging.info(f"Registro inserido: {item}")

                # Marca o registro como processado
                processed_records.add(record_key)

            cursor.connection.commit()
            logging.info("Todos os registros foram inseridos/atualizados com sucesso!")
        except Exception as e:
            cursor.connection.rollback()
            logging.error(f"Erro ao inserir/atualizar dados: {e}")
            raise e


def consulta_comissoes():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, operadora, modalidade, grupo, grade, comissionaveis, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7,
                parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, parcela_13, parcela_14, parcela_15, parcela_16, parcela_17, parcela_18, parcela_19, parcela_20,
                parcela_21, parcela_22, parcela_23, parcela_24, parcela_25, parcela_26, parcela_27, parcela_28, parcela_29, parcela_30
                    FROM grade_comissoes
                    WHERE grade='total'
                ORDER BY grupo ASC, operadora ASC;
            """
            cursor.execute(query,)
            results = cursor.fetchall()
            return results
    except Exception as e:
        logging.error(f"Erro ao consultar acessos: {e}")
        return []


def consulta_grade(grupo, operadora, modalidade):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT DISTINCT grade
                FROM grade_comissoes
                WHERE grupo = %s
                AND operadora = %s
                AND modalidade = %s
            """
            cursor.execute(query, (grupo, operadora, modalidade))
            grades = cursor.fetchall()
            return [grade[0] for grade in grades] if grades else []
    except Exception as e:
        logging.error(f"Erro ao consultar a grade: {e}")
        return []


def consulta_tabela(grupo, operadora, modalidade, grade1, grade2):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT comissionaveis, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7, parcela_8, parcela_9, parcela_10, parcela_11, parcela_12,
                parcela_13, parcela_14, parcela_15, parcela_16, parcela_17, parcela_18, parcela_19, parcela_20, parcela_21, parcela_22, parcela_23, parcela_24, parcela_25, parcela_26,
                parcela_27, parcela_28, parcela_29, parcela_30
                FROM grade_comissoes
                WHERE grupo = %s
                AND operadora = %s
                AND modalidade = %s
                AND grade IN (%s, %s)
                ORDER BY grade DESC, comissionaveis ASC
            """
            cursor.execute(query, (grupo, operadora, modalidade, grade1, grade2))
            tabela = cursor.fetchall()
            return tabela
    except Exception as e:
        logging.error(f"Erro ao consultar a tabela de comissões: {e}")
        return []


def consulta_admin():
    with instance_cursor() as cursor:
        query = """
            SELECT id, tipo_usuario, nome, data_nascimento, cpf, email, ativo, telefone, unidade_id, equipe_id, profile_image_url
            FROM users
            """
        cursor.execute(query)
        result = cursor.fetchall()
        return result


def consulta_valores(assistente, mesano):
    with instance_cursor() as cursor:
        query = """
        SELECT vltotal
        FROM sumario_assistente
        WHERE assistente = %s AND mesano = %s
        """
        cursor.execute(query, (str(assistente), mesano))
        result = cursor.fetchone()
        return result[0] if result else 0


def consulta_valores_anual(assistente, ano):
    with instance_cursor() as cursor:
        query = """
        SELECT SUM(vltotal)
        FROM sumario_assistente
        WHERE assistente = %s AND EXTRACT(YEAR FROM mesano) = %s
        """
        cursor.execute(query, (str(assistente), ano))
        result = cursor.fetchone()
        return result[0] if result else 0


def consulta_meta(canal_id):
    try:
        with instance_cursor() as cursor:
            query = """
            SELECT id, canal, meta
            FROM canal_meta
            WHERE id = %s
            """
            cursor.execute(query, (canal_id,))
            result = cursor.fetchone()
            return result[2] if result else None
    except Exception as e:
        logging.error(f"Erro ao consultar meta: {e}")
        return None


def get_metas_gerais():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, canal, meta
                FROM canal_meta ORDER BY id ASC
            """
            cursor.execute(query)
            rows = cursor.fetchall()

            result = [
                {
                    "id": row[0],
                    "canal": row[1],
                    "meta": row[2]
                }
                for row in rows
            ]

            return result
    except Exception as e:
        logging.error(f"Erro ao consultar metas gerais: {e}")
        return []


def update_meta(canal_id, meta):
    try:
        with instance_cursor() as cursor:
            query = """
            UPDATE canal_meta
            SET meta = %s
            WHERE id = %s
            """
            cursor.execute(query, (meta, canal_id))
            cursor.connection.commit()
            return True
    except Exception as e:
        cursor.connection.rollback()
        logging.error(f"Erro ao atualizar meta: {e}")
        return False


def get_datas_globais():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                id,
                dt_inicio_global,
                dt_final_global
                FROM datas_globais
                LIMIT 1
            """
            cursor.execute(query)
            row = cursor.fetchone()

            if row:
                return [{
                    "id": row[0],
                    "data_inicio": row[1].strftime("%Y-%m-%d") if row[1] else None,
                    "data_fim": row[2].strftime("%Y-%m-%d") if row[2] else None
                }]

            logging.error("Nenhum registro encontrado na tabela datas_globais")
            return []

    except Exception as e:
        logging.error(f"Erro ao consultar datas globais: {str(e)}")
        raise Exception(f"Erro ao consultar datas globais: {str(e)}")


def update_datas_globais(data_id, data_inicio, data_fim):
    try:
        with instance_cursor() as cursor:
            query = """
            UPDATE datas_globais
            SET dt_inicio_global = %s, dt_final_global = %s
            WHERE id = %s
            """
            cursor.execute(query, (data_inicio, data_fim, data_id))
            cursor.connection.commit()
            return True
    except Exception as e:
        cursor.connection.rollback()
        logging.error(f"Erro ao atualizar datas globais: {str(e)}")
        logging.error(f"Erro detalhado ao atualizar datas globais: {str(e)}")
        raise Exception(f"Erro ao atualizar datas globais: {str(e)}")


def get_datas_globais():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    dt_inicio_global,
                    dt_final_global
                FROM datas_globais
                LIMIT 1
            """
            cursor.execute(query)
            row = cursor.fetchone()
            if row:
                return {
                    "data_inicio": row[0],
                    "data_fim": row[1]
                }
            else:
                return None
    except Exception as e:
        logging.info(f"Erro ao consultar datas globais: {e}")
        return None


def get_metasporvida_operadora_metas():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT operadora, metas_vidas
                FROM meta_vidas_operadora
                ORDER BY operadora
            """
            cursor.execute(query)
            rows = cursor.fetchall()

            result = [
                {
                    "operadora": row[0],
                    "metas_vidas": row[1]
                }
                for row in rows
            ]

            return result
    except Exception as e:
        logging.error(f"Erro ao consultar metas de vidas por operadora: {e}")
        return []


def update_metasporvida_operadora_metas(operadora, valor):
    try:
        with instance_cursor() as cursor:
            query = """
            UPDATE meta_vidas_operadora
            SET metas_vidas = %s
            WHERE operadora = %s
            """
            cursor.execute(query, (valor, operadora))
            cursor.connection.commit()
            return True
    except Exception as e:
        cursor.connection.rollback()
        logging.error(f"Erro ao atualizar meta de vidas da operadora {operadora}: {e}")
        return False


def get_metas_vidas_assistentes_metas():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT assistente, operadora, equipe, metas_vidas
                FROM meta_vidas_assistente
                ORDER BY assistente, operadora
            """
            cursor.execute(query)
            rows = cursor.fetchall()

            result = [
                {
                    "assistente": row[0],
                    "operadora": row[1],
                    "equipe": row[2],
                    "metas_vidas": row[3]
                }
                for row in rows
            ]

            return result
    except Exception as e:
        logging.error(f"Erro ao consultar metas de vidas por assistente: {e}")
        return []


def update_metas_vidas_assistentes_metas(assistente, operadora, valor):
    try:
        with instance_cursor() as cursor:
            query = """
            UPDATE meta_vidas_assistente
            SET metas_vidas = %s
            WHERE assistente = %s AND operadora = %s
            """
            cursor.execute(query, (valor, assistente, operadora))
            cursor.connection.commit()
            return True
    except Exception as e:
        cursor.connection.rollback()
        logging.error(f"Erro ao atualizar meta de vidas do assistente {assistente} para operadora {operadora}: {e}")
        return False


def update_sumario_assistente_anual(data):
    with instance_cursor() as cursor:
        try:
            # Apagar os dados existentes
            cursor.execute("DELETE FROM public.sumario_assistente_anual")

            # Inserir os novos dados
            insert_query = """
                INSERT INTO public.sumario_assistente_anual (
                    assistente, vltotal, vlcontrato, vlvida, vlreceber, vlpagar, vllucro, vllucropercentual, vllucrototalpercentual, corretorcount, contratocount, vidacount, contratoporcorretor
                ) VALUES (
                    %(assistente)s, %(vlTotal)s, %(vlContrato)s, %(vlVida)s, %(vlReceber)s, %(vlPagar)s, %(vlLucro)s, %(vlLucroPercentual)s, %(vlLucroTotalPercentual)s, %(corretorCount)s, %(contratoCount)s, %(vidaCount)s, %(contratoPorCorretor)s
                )
            """
            for item in data:
                cursor.execute(insert_query, item)

            cursor.connection.commit()
        except Exception as e:
            cursor.connection.rollback()
            raise e


def update_sumario_assistente_ases(data):
    with instance_cursor() as cursor:
        try:
            # Apagar os dados existentes
            cursor.execute("DELETE FROM public.sumario_assistente_ases")

            # Inserir os novos dados
            insert_query = """
                INSERT INTO public.sumario_assistente_ases (
                    assistente, vltotal, vlcontrato, vlvida, vlreceber, vlpagar, vllucro, vllucropercentual, vllucrototalpercentual, corretorcount, contratocount, vidacount, contratoporcorretor
                ) VALUES (
                    %(assistente)s, %(vlTotal)s, %(vlContrato)s, %(vlVida)s, %(vlReceber)s, %(vlPagar)s, %(vlLucro)s, %(vlLucroPercentual)s, %(vlLucroTotalPercentual)s, %(corretorCount)s, %(contratoCount)s, %(vidaCount)s, %(contratoPorCorretor)s
                )
            """
            for item in data:
                cursor.execute(insert_query, item)

            cursor.connection.commit()
        except Exception as e:
            cursor.connection.rollback()
            raise e


def update_sumario_corretor_ases(data):
    with instance_cursor() as cursor:
        try:
            # Apagar os dados existentes
            cursor.execute("DELETE FROM public.sumario_corretor_ases")

            # Inserir os novos dados
            insert_query = """
                INSERT INTO public.sumario_corretor_ases (
                    assistente, corretor, vltotal, vlcontrato, vlvida, vlreceber, vlpagar, vllucro, vllucropercentual, vllucrototalpercentual, corretorcount, contratocount, vidacount, contratoporcorretor
                ) VALUES (
                    %(assistente)s, %(corretor)s, %(vlTotal)s, %(vlContrato)s, %(vlVida)s, %(vlReceber)s, %(vlPagar)s, %(vlLucro)s, %(vlLucroPercentual)s, %(vlLucroTotalPercentual)s, %(corretorCount)s, %(contratoCount)s, %(vidaCount)s, %(contratoPorCorretor)s
                )
            """
            for item in data:
                cursor.execute(insert_query, item)

            cursor.connection.commit()
        except Exception as e:
            cursor.connection.rollback()
            raise e


def consulta_ranking_ases():
    with instance_cursor() as cursor:
        query = """
            SELECT assistente, vltotal
            FROM sumario_assistente_ases
        """
        cursor.execute(query)
        result = cursor.fetchall()

        # Nomes das colunas retornadas pelo cursor
        column_names = [desc[0] for desc in cursor.description]

        # Converter cada linha para um dicionário
        result_dicts = [dict(zip(column_names, row)) for row in result]

        # Dicionário de mapeamento para canais
        canais = {
            "SUSEP EXTERNO": ["CAIQUE GONÇALVES", "CAIO VILLAPIANO", "F A S CONSULTORIA E GESTAO EM SEGUROS LTDA", "PRISCILA SILVA", "JACQUELINE MARIA DA SILVA", "PRISCILA VENTRE", "LUCIANA SARTORI CORRETORA E CONSULTORIA LTDA",
                              "SILVANA ZANARDI","CELSO RICARDO LORENÇATO"],
            "CO-WORKING": ["ALEXANDRE SAUDE ME (CO-WORKING)", "CAMILA CABRAL (CO-WORKING)"],
            "ASSESSORIA": ["DANIELE SIMAO DOS SANTOS", "TATIANE ALVES DOS SANTOS RIBEIRO"],
            "FRANQUIA": ["TATIANE DE OLIVEIRA AZEVEDO - FRANQUIA"],
            "UNIDADES": ["VANESSA TENERELLI ME (LUANCA BRH)", "CONFIANCE BRH", "BRH SOLUTION SAUDE ASSESSORIA E CORRETORA DE SEGUROS LTDA", "LUANCA BRH CONSULTORIA E BENEFICIOS E CORRETORA DE SEGUROS LTDA",
                         "MAHALO HEALTH PLANOS DE SAUDE (LUANCA BRH)", "ALEXANDRE DE CARVALHO GOES (SOLUTION)", "BRAZIL HEALTH RJ ASSESSORIA EM SEGUROS LTDA", "BRAZIL HEALTH CAMPINAS"],
            "BRAZIL CALL": ["MARCIA VIVIAN (BRAZIL CALL)"]
        }

        # Dicionário de metas para cada canal
        metas = {
            "SUSEP EXTERNO": {"meta_nacional": 2750000.00, "meta_internacional": 3300000.00},
            "CO-WORKING": {"meta_nacional": 1650000.00, "meta_internacional": 2000000.00},
            "ASSESSORIA": {"meta_nacional": 2750000.00, "meta_internacional": 3300000.00},
            "FRANQUIA": {"meta_nacional": 2750000.00, "meta_internacional": 3300000.00},
            "UNIDADES": {"meta_nacional": 1650000.00, "meta_internacional": 2000000.00},
            "BRAZIL CALL": {"meta_nacional": 1650000.00, "meta_internacional": 2000000.00},
            "Desconhecido": {"meta_nacional": 0, "meta_internacional": 0}
        }

        # Adicionar o campo canal e metas com base no assistente
        for item in result_dicts:
            item["canal"] = "Desconhecido"  # Valor padrão
            for canal, assistentes in canais.items():
                if (item["assistente"] in assistentes):
                    item["canal"] = canal
                    break

            # Definir metas com base no canal
            item["meta_nacional"] = metas[item["canal"]]["meta_nacional"]
            item["meta_internacional"] = metas[item["canal"]]["meta_internacional"]

        return result_dicts


def consulta_ranking_ases_corretor():
    with instance_cursor() as cursor:
        query = """
            SELECT assistente, corretor, SUM(vltotal) as vltotal
            FROM sumario_corretor_ases
            GROUP BY assistente, corretor
        """
        cursor.execute(query)
        result = cursor.fetchall()

        # Nomes das colunas retornadas pelo cursor
        column_names = [desc[0] for desc in cursor.description]

        # Converter cada linha para um dicionário
        result_dicts = [dict(zip(column_names, row)) for row in result]

        # Dicionário de mapeamento para canais
        canais = {
            "SUSEP EXTERNO": ["CAIQUE GONÇALVES", "CAIO VILLAPIANO", "F A S CONSULTORIA E GESTAO EM SEGUROS LTDA", "PRISCILA SILVA", "JACQUELINE MARIA DA SILVA", "PRISCILA VENTRE", "LUCIANA SARTORI CORRETORA E CONSULTORIA LTDA",
                              "SILVANA ZANARDI","CELSO RICARDO LORENÇATO"],
            "CO-WORKING": ["ALEXANDRE SAUDE ME (CO-WORKING)", "CAMILA CABRAL (CO-WORKING)"],
            "ASSESSORIA": ["DANIELE SIMAO DOS SANTOS", "TATIANE ALVES DOS SANTOS RIBEIRO"],
            "FRANQUIA": ["TATIANE DE OLIVEIRA AZEVEDO - FRANQUIA"],
            "UNIDADES": ["VANESSA TENERELLI ME (LUANCA BRH)", "CONFIANCE BRH", "BRH SOLUTION SAUDE ASSESSORIA E CORRETORA DE SEGUROS LTDA", "LUANCA BRH CONSULTORIA E BENEFICIOS E CORRETORA DE SEGUROS LTDA",
                         "MAHALO HEALTH PLANOS DE SAUDE (LUANCA BRH)", "ALEXANDRE DE CARVALHO GOES (SOLUTION)", "BRAZIL HEALTH RJ ASSESSORIA EM SEGUROS LTDA", "BRAZIL HEALTH CAMPINAS"],
            "BRAZIL CALL": ["MARCIA VIVIAN (BRAZIL CALL)"]
        }

        # Dicionário de metas para cada canal
        metas = {
            "SUSEP EXTERNO": {"meta_nacional": 220000.00, "meta_internacional": 330000.00},
            "CO-WORKING": {"meta_nacional": 130000.00, "meta_internacional": 190000.00},
            "ASSESSORIA": {"meta_nacional": 220000.00, "meta_internacional": 330000.00},
            "FRANQUIA": {"meta_nacional": 220000.00, "meta_internacional": 330000.00},
            "UNIDADES": {"meta_nacional": 130000.00, "meta_internacional": 190000.00},
            "BRAZIL CALL": {"meta_nacional": 110000.00, "meta_internacional": 165000.00},
            "Desconhecido": {"meta_nacional": 0, "meta_internacional": 0}
        }

        # Adicionar o campo canal e metas com base no assistente
        for item in result_dicts:
            item["canal"] = "Desconhecido"  # Valor padrão
            for canal, assistentes in canais.items():
                if (item["assistente"] in assistentes):
                    item["canal"] = canal
                    break

            # Definir metas com base no canal
            item["meta_nacional"] = metas[item["canal"]]["meta_nacional"]
            item["meta_internacional"] = metas[item["canal"]]["meta_internacional"]

        return result_dicts


def consulta_nome_equipe(equipe_id):
    with instance_cursor() as cursor:
        query = "SELECT equipe FROM equipes WHERE id = %s"
        cursor.execute(query, (equipe_id,))
        result = cursor.fetchone()
        return result[0] if result else None


def consulta_assistentes(equipe_id):
    with instance_cursor() as cursor:
        query = "SELECT id, assistente, canal FROM users WHERE equipe_id = %s"
        cursor.execute(query, (equipe_id,))
        results = cursor.fetchall()
        return results


def consulta_valores_superintendente(assistentes, mesano):
    if (not isinstance(assistentes, list)):
        assistentes = [assistentes]

    with instance_cursor() as cursor:
        placeholders = ', '.join(['%s'] * len(assistentes))
        query = f"""
        SELECT SUM(vltotal)
        FROM sumario_assistente
        WHERE assistente IN ({placeholders}) AND mesano = %s
        """
        cursor.execute(query, (*assistentes, mesano))
        result = cursor.fetchone()
        return result[0] if result else 0


def consulta_valores_anual_superintendente(assistentes, ano):
    if (not isinstance(assistentes, list)):
        assistentes = [assistentes]

    with instance_cursor() as cursor:
        placeholders = ', '.join(['%s'] * len(assistentes))
        query = f"""
        SELECT SUM(vltotal)
        FROM sumario_assistente
        WHERE assistente IN ({placeholders}) AND EXTRACT(YEAR FROM mesano) = %s
        """
        cursor.execute(query, (*assistentes, ano))
        result = cursor.fetchone()
        return result[0] if result else 0


def listar_setores():
    setores_permitidos = [1, 3, 4, 7]

    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, nome
                FROM setores
                WHERE id IN %s;
            """
            cursor.execute(query, (tuple(setores_permitidos),))
            setores = cursor.fetchall()

            setores_dict = [{"id": setor[0], "nome": setor[1]} for setor in setores]
            return setores_dict
    except Exception as e:
        log(f"Erro ao listar setores: {e}")
        return []


def atualizar_setor(user_id, setor_id):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE users
                SET setor_id = %s
                WHERE id = %s;
            """
            cursor.execute(query, (setor_id, user_id))
            if (cursor.rowcount == 0):
                log(f"Nenhuma linha afetada ao tentar atualizar o setor para user_id {user_id} com setor_id {setor_id}")
                return False
            log(f"Setor atualizado para user_id {user_id} com setor_id {setor_id}")
            return True
    except Exception as e:
        log(f"Erro ao atualizar o setor no banco de dados: {e}")
        return False


def consulta_acessos(setor_id=None):
    try:
        with instance_cursor() as cursor:
            if (setor_id):
                query = """
                    SELECT
                        a.id,
                        p.nome AS nome_portal,
                        p.link,
                        a.login,
                        a.senha,
                        a.unidade,
                        a.codigo_acesso,
                        a.outros,
                        a.modalidade,
                        p.id AS portal_id  -- Corrigido: adicionei a vírgula aqui
                    FROM
                        acessos a
                    JOIN
                        portais p ON a.portal_id = p.id
                    JOIN
                        setores s ON a.setor_id = s.id
                    WHERE
                        s.id = %s;
                """
                cursor.execute(query, (setor_id,))
            else:
                query = """
                    SELECT
                        a.id,
                        p.nome AS nome_portal,
                        p.link,
                        a.login,
                        a.senha,
                        a.unidade,
                        a.codigo_acesso,
                        a.outros,
                        a.modalidade,
                        p.id AS portal_id  -- Corrigido: adicionei a vírgula aqui
                    FROM
                        acessos a
                    JOIN
                        portais p ON a.portal_id = p.id
                    JOIN
                        setores s ON a.setor_id = s.id;
                """
                cursor.execute(query)

            results = cursor.fetchall()
            processed_results = [
                tuple('--' if value is None else value for value in row)
                for row in results
            ]
            return processed_results
    except Exception as e:
        log(f"Erro ao consultar acessos: {e}")
        return []


def add_portal(nome, link):
    with instance_cursor() as cursor:
        query = """
            INSERT INTO portais (nome, link)
            VALUES (%s, %s)
        """
        cursor.execute(query, (nome, link))
        cursor.connection.commit()


def consulta_portais_to_insert(nome, link):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, nome, link
                FROM portais
                WHERE nome = %s AND link = %s;
            """
            cursor.execute(query, (nome, link))
            results = cursor.fetchall()
            return results
    except Exception as e:
        log(f"Erro ao consultar acessos: {e}")
        return []


def consulta_portais():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, nome, link
	                FROM portais;
            """
            cursor.execute(query,)
            results = cursor.fetchall()
            return results
    except Exception as e:
        log(f"Erro ao consultar acessos: {e}")
        return []


def consulta_acessos_to_insert(setor_id, portal_id, login, senha):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, setor_id, portal_id, login, senha
                FROM acessos
                WHERE setor_id = %s AND portal_id = %s AND login = %s AND senha = %s;
            """
            cursor.execute(query, (setor_id, portal_id, login, senha))
            results = cursor.fetchall()
            return results
    except Exception as e:
        log(f"Erro ao consultar acessos: {e}")
        return []


def add_acessos(setor_id, portal_id, login, senha, unidade, codigo_acesso, outros, modalidade):
    with instance_cursor() as cursor:
        query = """
            INSERT INTO acessos (setor_id, portal_id, login, senha, unidade, codigo_acesso, outros, modalidade)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        cursor.execute(query, (setor_id, portal_id, login, senha, unidade, codigo_acesso, outros, modalidade))
        cursor.connection.commit()


def atualizar_acesso(acesso_id, portal_id, novo_nome_portal, novo_link_portal, novo_login, nova_senha, nova_unidade, novo_codigo_acesso, novos_outros, nova_modalidade):
    try:
        # Gerenciador de conexão e cursor
        with instance_cursor() as cursor:
            # Atualizar a tabela 'portais'
            query_portal = """
                UPDATE portais
                SET nome = %s, link = %s
                WHERE id = %s
            """
            cursor.execute(query_portal, (novo_nome_portal, novo_link_portal, portal_id))

            # Atualizar a tabela 'acessos'
            query_acessos = """
                UPDATE acessos
                SET login = %s,
                    senha = %s,
                    unidade = %s,
                    codigo_acesso = %s,
                    outros = %s,
                    modalidade = %s
                WHERE id = %s
            """
            cursor.execute(query_acessos, (novo_login, nova_senha, nova_unidade, novo_codigo_acesso, novos_outros, nova_modalidade, acesso_id))

            # Confirmar todas as alterações
            cursor.connection.commit()
            log(f"Acesso com ID {acesso_id} e Portal com ID {portal_id} atualizados com sucesso.")

    except psycopg2.DatabaseError as e:
        # Em caso de erro, registrar a exceção e reverter as alterações
        log(f"Erro ao atualizar acesso e portal: {e}")
        raise e  # Opcionalmente, pode-se relançar a exceção


def add_reembolsos(user_id, nome, departamento, email, telefone, data_despesa, tipo_despesa, valor_despesa, descricao, link_anexo, status):
    try:
        with instance_cursor() as cursor:
            logging.info("Cursor criado com sucesso.")
            query = """
                INSERT INTO solicitacao_reembolso (user_id, nome, departamento, email, telefone, data_despesa, tipo_despesa, valor_despesa, descricao, link_anexo, status, data_solicitacao)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW() - INTERVAL '3 hours')
            """
            cursor.execute(query, (user_id, nome, departamento, email, telefone, data_despesa, tipo_despesa, valor_despesa, descricao, link_anexo, status))
            logging.info("Query de inserção executada com sucesso.")
            cursor.connection.commit()
            logging.info("Transação confirmada com sucesso.")
    except Exception as e:
        logging.error(f"Erro ao inserir dados: {e}")
        raise


def consulta_reembolso():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    sr.id,
                    sr.nome,
                    sr.departamento,
                    sr.email,
                    sr.telefone,
                    sr.data_despesa,
                    sr.tipo_despesa,
                    sr.valor_despesa,
                    sr.descricao,
                    sr.link_anexo,
                    sr.status,
                    sr.user_id,
                    sr.user_aprovador,
                    sr.data_aprovacao,
                    COALESCE(u.nome, '') AS nome_aprovador,
                    sr.data_solicitacao
                FROM
                    public.solicitacao_reembolso sr
                LEFT JOIN
                    public.users u
                ON
                    sr.user_aprovador = u.id;
            """
            cursor.execute(query)
            results = cursor.fetchall()

            # Formata datas no padrão desejado
            formatted_results = []
            for row in results:
                row = list(row)
                if row[13]:
                    row[13] = row[13].strftime('%d/%m/%Y - %H:%M')
                if row[15]:
                    row[15] = row[15].strftime('%d/%m/%Y - %H:%M')
                formatted_results.append(row)
            return formatted_results
    except Exception as e:
        logging.error(f"Erro ao consultar reembolsos: {e}")
        return []


def consulta_reembolso_usuario(user_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    sr.id,
                    sr.nome,
                    sr.departamento,
                    sr.email,
                    sr.telefone,
                    sr.data_despesa,
                    sr.tipo_despesa,
                    sr.valor_despesa,
                    sr.descricao,
                    sr.link_anexo,
                    sr.status,
                    sr.user_id,
                    sr.user_aprovador,
                    sr.data_aprovacao,
                    COALESCE(u.nome, '') AS nome_aprovador,
                    sr.data_solicitacao
                FROM
                    public.solicitacao_reembolso sr
                LEFT JOIN
                    public.users u
                ON
                    sr.user_aprovador = u.id
                WHERE sr.user_id = %s;
            """
            cursor.execute(query, (user_id,))
            results = cursor.fetchall()

            # Formata datas no padrão desejado
            formatted_results = []
            for row in results:
                row = list(row)
                if row[13]:
                    row[13] = row[13].strftime('%d/%m/%Y - %H:%M')
                if row[15]:
                    row[15] = row[15].strftime('%d/%m/%Y - %H:%M')
                formatted_results.append(row)
            return formatted_results
    except Exception as e:
        logging.error(f"Erro ao consultar reembolsos do usuário {user_id}: {e}")
        return []


def consulta_detalhes_reembolso(reembolso_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    sr.id,
                    sr.nome,
                    sr.departamento,
                    sr.email,
                    sr.telefone,
                    sr.data_despesa,
                    sr.tipo_despesa,
                    sr.valor_despesa,
                    sr.descricao,
                    sr.link_anexo,
                    sr.status,
                    sr.user_id,
                    sr.user_aprovador,
                    sr.data_aprovacao,
                    COALESCE(u.nome, '') AS nome_aprovador,
                    sr.data_solicitacao
                FROM
                    public.solicitacao_reembolso sr
                LEFT JOIN
                    public.users u
                ON
                    sr.user_aprovador = u.id
                WHERE sr.id = %s;
            """
            cursor.execute(query, (reembolso_id,))
            reembolso = cursor.fetchone()
            return reembolso
    except Exception as e:
        logging.error(f"Erro ao consultar detalhes do reembolso: {e}")
        return None


def atualizar_status_reembolso(reembolso_id, novo_status, user_aprovador):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE solicitacao_reembolso
                SET status = %s, user_aprovador = %s, data_aprovacao = NOW() - INTERVAL '3 hours'
                WHERE id = %s
            """
            cursor.execute(query, (novo_status, user_aprovador, reembolso_id))
            cursor.connection.commit()
            return True
    except Exception as e:
        logging.error(f"Erro ao atualizar status do reembolso: {e}")
        return False


def add_fastmoney(tipo_adiantamento, codigo_proposta, corretor, supervisor, modalidade, operadora, subproduto,
                        segurado, valor_proposta, acordo, tabela_padrao, comprovante_link, boleto_link, status, user_id):
    with instance_cursor() as cursor:
        try:
            query = """
                INSERT INTO solicitacoes_fastmoney (
                    tipo_adiantamento, cod_proposta, corretor, supervisor, modalidade, operadora, sub_produto,
                    segurado, valor_proposta, acordo, tabela_padrao, comprovante_link, boleto_link, status, user_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(query, (tipo_adiantamento, codigo_proposta, corretor, supervisor, modalidade, operadora, subproduto,
                        segurado, valor_proposta, acordo, tabela_padrao, comprovante_link, boleto_link, status, user_id))
            logging.info("Query de inserção executada com sucesso.")
            cursor.connection.commit()
            logging.info("Transação confirmada com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao inserir dados: {e}")
            raise


def consulta_fastmoney(start_date=None, end_date=None):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, user_id, corretor, segurado, operadora, valor_proposta, modalidade, tipo_adiantamento, acordo, tabela_padrao, comprovante_link, status, created_at, supervisor, cod_proposta, sub_produto, boleto_link
                FROM solicitacoes_fastmoney
            """
            params = []
            if (start_date and end_date):
                query += " WHERE created_at BETWEEN %s AND %s"
                params.append(start_date)
                params.append(end_date)

            cursor.execute(query, params)
            results = cursor.fetchall()

            # Formatação dos resultados
            formatted_results = []
            for row in results:
                created_at = row[12]
                if created_at:
                    try:
                        # Garante que created_at é um objeto datetime
                        if isinstance(created_at, str):
                            created_at = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        # Ajusta o fuso horário
                        created_at = created_at - timedelta(hours=3)
                        formatted_created_at = created_at.strftime('%d/%m/%Y %H:%M:%S')
                        row = list(row)
                        row[12] = formatted_created_at
                    except Exception as e:
                        logging.error(f"Erro ao formatar data: {e}")
                formatted_results.append(tuple(row))

            return formatted_results
    except Exception as e:
        logging.error(f"Erro ao consultar Fast Money: {e}")
        return []


def consulta_fastmoney_usuario(user_id, start_date=None, end_date=None):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, user_id, corretor, segurado, operadora, valor_proposta, modalidade, tipo_adiantamento, acordo, tabela_padrao, comprovante_link, status, created_at, supervisor, cod_proposta, sub_produto, boleto_link
                FROM solicitacoes_fastmoney
                WHERE user_id = %s
            """
            params = [user_id]

            # Adicionando condição para datas, caso sejam fornecidas
            if (start_date and end_date):
                query += " AND created_at BETWEEN %s AND %s"
                params.append(start_date)
                params.append(end_date)

            cursor.execute(query, params)
            results = cursor.fetchall()

            # Formatação dos resultados
            formatted_results = []
            for row in results:
                created_at = row[11]  # A coluna created_at está na posição 11
                if created_at:
                    try:
                        # Garante que created_at é um objeto datetime
                        if isinstance(created_at, str):
                            created_at = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        # Ajusta o fuso horário
                        created_at = created_at - timedelta(hours=3)
                        formatted_created_at = created_at.strftime('%d/%m/%Y %H:%M:%S')
                        row = list(row)
                        row[11] = formatted_created_at
                    except Exception as e:
                        logging.error(f"Erro ao formatar data: {e}")
                formatted_results.append(tuple(row))

            return formatted_results

    except Exception as e:
        logging.error(f"Erro ao consultar Fast Money: {e}")
        return []


def consulta_fastmoney_unidade(unidade_id, start_date=None, end_date=None):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT sf.id, sf.user_id, sf.corretor, sf.segurado, sf.operadora, sf.valor_proposta,
                       sf.modalidade, sf.tipo_adiantamento, sf.acordo, sf.tabela_padrao,
                       sf.comprovante_link, sf.boleto_link, sf.status, sf.created_at, sf.supervisor,
                       sf.cod_proposta, sf.sub_produto, sf.motivo_rejeicao, sf.comentario_rejeicao
                FROM solicitacoes_fastmoney sf
                JOIN users u ON sf.user_id = u.id
                WHERE u.unidade_id = %s
            """
            params = [unidade_id]

            if start_date and end_date:
                query += " AND sf.created_at BETWEEN %s AND %s"
                params.extend([start_date, end_date])

            cursor.execute(query, params)
            results = cursor.fetchall()

            formatted_results = []
            for row in results:
                created_at = row[13]  # posição do campo created_at na lista
                if created_at:
                    try:
                        if isinstance(created_at, str):
                            created_at = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = created_at - timedelta(hours=3)
                        formatted_created_at = created_at.strftime('%d/%m/%Y %H:%M:%S')
                        row = list(row)
                        row[13] = formatted_created_at
                    except Exception as e:
                        logging.error(f"Erro ao formatar data: {e}")
                formatted_results.append(tuple(row))

            return formatted_results

    except Exception as e:
        logging.error(f"Erro ao consultar Fast Money: {e}")
        return []


def consulta_detalhes_fastmoney(fastmoney_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, user_id, corretor, segurado, operadora, valor_proposta, modalidade, tipo_adiantamento, acordo, tabela_padrao, comprovante_link, boleto_link, status, supervisor, cod_proposta, sub_produto, motivo_rejeicao, comentario_rejeicao
                FROM solicitacoes_fastmoney
                WHERE id = %s;
            """
            cursor.execute(query, (fastmoney_id,))
            fastmoney = cursor.fetchone()
            if (fastmoney):
                logging.info(f"Dados retornados para fastmoney_id {fastmoney_id}: {fastmoney}")
            else:
                logging.warning(f"Nenhum dado encontrado para fastmoney_id {fastmoney_id}")
            return fastmoney
    except Exception as e:
        logging.error(f"Erro ao consultar detalhes do fastmoney_id {fastmoney_id}: {e}")
        return None


def atualizar_status_fastmoney(fastmoney_id, novo_status):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE solicitacoes_fastmoney
                SET status = %s
                WHERE id = %s
            """
            cursor.execute(query, (novo_status, fastmoney_id))
            return True
    except Exception as e:
        logging.error(f"Erro ao atualizar status do reembolso: {e}")
        return False


def reanalisar_fastmoney(fastmoney_id, comentario):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE solicitacoes_fastmoney
                SET status = %s, comentario_rejeicao = %s
                WHERE id = %s
            """
            # 'rejeitada' é o novo status padrão para a solicitação, e também salvamos o comentário.
            cursor.execute(query, ('rejeitada', comentario, fastmoney_id))
            return True
    except Exception as e:
        logging.error(f"Erro ao atualizar solicitação de Fast Money: {e}")
        return False


def rejeicao_status_fastmoney(fastmoney_id, novo_status, motivo_rejeicao=None, comentario_rejeicao=None):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE solicitacoes_fastmoney
                SET status = %s, motivo_rejeicao = %s, comentario_rejeicao = %s
                WHERE id = %s
            """
            cursor.execute(query, (novo_status, motivo_rejeicao, comentario_rejeicao, fastmoney_id))
            return True
    except Exception as e:
        logging.error(f"Erro ao atualizar status do Fast Money: {e}")
        return False


def consulta_permissao(user_id, permissao_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT 1
                FROM user_permissoes
                WHERE user_id = %s AND permissao_id = %s

                UNION ALL

                SELECT 1
                FROM user_grupos ug
                JOIN grupo_permissoes gp ON ug.grupo_id = gp.grupo_id
                WHERE ug.user_id = %s AND gp.permissao_id = %s;
            """

            # Executa a consulta
            cursor.execute(query, (user_id, permissao_id, user_id, permissao_id))
            resultado = cursor.fetchone()

            # Se retornar algum resultado, significa que o usuário tem a permissão
            if (resultado):
                return True
            else:
                return False
    except Exception as e:
        # Loga o erro e retorna False
        logging.error(f"Erro ao consultar permissão para o usuário {user_id} e permissão {permissao_id}: {e}")
        return False


def consulta_lista_permissoes(permissao_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT up.id, up.user_id, up.permissao_id, u.nome AS usuario_nome, p.nome AS permissao_nome
                    FROM user_permissoes up
                JOIN users u ON up.user_id = u.id
                JOIN permissoes p ON up.permissao_id = p.id
                WHERE permissao_id = %s;
            """
            cursor.execute(query, (permissao_id,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar permissões: {e}")
        return None


def consulta_todos_usuarios():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, nome
	                FROM users
            """
            cursor.execute(query, ())
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar permissões: {e}")
        return None


def add_permissoes(user_id, permissao_id):
    try:
        with instance_cursor() as cursor:
            query = """
                INSERT INTO user_permissoes(user_id, permissao_id)
                    VALUES (%s, %s)
            """
            cursor.execute(query, (user_id, permissao_id))
            cursor.connection.commit()
            logging.info("Permissão adicionada ao usuário.")
    except Exception as e:
        logging.error(f'Erro ao adicionar a permissão no usuário: {e}')
        raise


def excluir_permissoes(user_id, permissao_id):
    try:
        with instance_cursor() as cursor:
            query = """
                DELETE FROM user_permissoes
                WHERE user_id = %s AND permissao_id = %s
            """
            cursor.execute(query, (user_id, permissao_id))
            cursor.connection.commit()
            logging.info("Permissão removida do usuário.")
    except Exception as e:
        logging.error(f'Erro ao remover a permissão do usuário: {e}')
        raise


def consulta_tabela_geral(grade, operadora, modalidade):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT operadora, modalidade, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7, parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, grade
	            FROM grade_comissoes
                WHERE grade = %s AND operadora = %s AND modalidade = %s AND comissionaveis = '1º corretor'
            """
            cursor.execute(query, (grade, operadora, modalidade,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar a tabela: {e}")
        return None


def consulta_tabela_op_grade(grade, operadora):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT operadora, modalidade, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7, parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, grade
	            FROM grade_comissoes
                WHERE grade = %s AND operadora = %s AND comissionaveis = '1º corretor'
            """
            cursor.execute(query, (grade, operadora,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar a tabela: {e}")
        return None


def consulta_tabela_grade_mod(grade, modalidade):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT operadora, modalidade, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7, parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, grade
	            FROM grade_comissoes
                WHERE grade = %s AND modalidade = %s AND comissionaveis = '1º corretor'
            """
            cursor.execute(query, (grade, modalidade,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar a tabela: {e}")
        return None


def consulta_tabela_grade(grade):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT operadora, modalidade, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7, parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, grade
	            FROM grade_comissoes
                WHERE grade = %s AND comissionaveis = '1º corretor'
            """
            cursor.execute(query, (grade,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar filtros por grade: {e}")
        return None


def consulta_tabela_op(operadora):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT operadora, modalidade, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7, parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, grade
	            FROM grade_comissoes
                WHERE operadora = %s AND comissionaveis = '1º corretor'
            """
            cursor.execute(query, (operadora,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar filtros por grade: {e}")
        return None


def consulta_tabela_mod(modalidade):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT operadora, modalidade, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7, parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, grade
	            FROM grade_comissoes
                WHERE modalidade = %s AND comissionaveis = '1º corretor'
            """
            cursor.execute(query, (modalidade,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar filtros por grade: {e}")
        return None


def consulta_tabelas():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT operadora, modalidade, totais, parcela_1, parcela_2, parcela_3, parcela_4, parcela_5, parcela_6, parcela_7, parcela_8, parcela_9, parcela_10, parcela_11, parcela_12, grade
	            FROM grade_comissoes
                WHERE comissionaveis = '1º corretor'
            """
            cursor.execute(query, ())
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar a tabela: {e}")
        return None


def consulta_filtros_comissoes():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT DISTINCT grade, operadora, modalidade
                FROM grade_comissoes
            """
            cursor.execute(query)
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar os filtros: {e}")
        return None


def consulta_filtros_por_grade(grade):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT DISTINCT operadora, modalidade
                FROM grade_comissoes
                WHERE grade = %s
            """
            cursor.execute(query, (grade,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar filtros por grade: {e}")
        return None


def consulta_canal_grade(canal_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, canal
                    FROM canal_meta
                    WHERE id = %s
            """
            cursor.execute(query, (canal_id,))
            return cursor.fetchall()
    except Exception as e:
        logging.error(f"Erro ao consultar filtros por grade: {e}")
        return None


def add_ticket(user_id, setor_id, assunto, description, modalidade, operadora, tipo_chamado):
    try:
        with instance_cursor() as cursor:
            query = """
                INSERT INTO tickets (user_id, setor_id, assunto, description, modalidade, operadora, tipo_chamado, status, created_at, ultima_interacao)
                VALUES (%s, %s, %s, %s, %s, %s, %s, 'Aberto', NOW(), NOW())
                RETURNING id;
            """
            cursor.execute(query, (user_id, setor_id, assunto, description, modalidade, operadora, tipo_chamado))
            result = cursor.fetchone()

            if (result is None):
                raise Exception("Falha ao inserir o ticket: Nenhum ID retornado.")

            ticket_id = result[0]
            return ticket_id
    except Exception as e:
        logging.error(f"Erro ao inserir o chamado: {e}")
        raise e


def add_ticket_file(ticket_id, file_url, file_name):
    try:
        with instance_cursor() as cursor:
            query = """
                INSERT INTO ticket_files (ticket_id, file_url, file_name, uploaded_at)
                VALUES (%s, %s, %s, NOW())
            """
            cursor.execute(query, (ticket_id, file_url, file_name))
    except Exception as e:
        logging.error(f"Erro ao inserir o arquivo do chamado: {e}")
        raise e


def format_date(date_str):
    if (date_str):
        day, month, year = date_str.split('/')
        return f"{year}-{month}-{day}"
    return None


def add_formulario_pfadesao(ticket_id, multinotas, login_multinotas, senha_multinotas, cod_corretor,
                            modalidade, operadora, administradora, entidade, vigencia, copart,
                            aprov_carencia, odonto, contato_respon, email, telefone, valor_cotacao, info_complementares, tipo_copart):
    try:
        # Converte campos conforme necessário
        cod_corretor = int(cod_corretor) if (cod_corretor) else None
        # Verifica se `valor_cotacao` é uma string antes de aplicar o replace; caso contrário, mantém o valor float
        if (isinstance(valor_cotacao, str)):
            valor_cotacao = float(valor_cotacao.replace("R$", "").replace(".", "").replace(",", "."))

        with instance_cursor() as cursor:
            query = """
                INSERT INTO formulario_emissao_pfadesao (tickets_id, multinotas, login_multinotas, senha_multinotas,
                    cod_corretor, modalidade, operadora, administradora, entidade, vigencia, copart,
                    aprov_carencia, odonto, contato_respon, email, telefone, valor_cotacao, info_complementares, tipo_copart)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id;
            """
            cursor.execute(query, (ticket_id, multinotas, login_multinotas, senha_multinotas, cod_corretor,
                                   modalidade, operadora, administradora, entidade, vigencia, copart,
                                   aprov_carencia, odonto, contato_respon, email, telefone, valor_cotacao, info_complementares, tipo_copart))
            result = cursor.fetchone()

            # Verifica se o resultado retornou corretamente
            if (result is None or len(result) < 1):
                raise Exception("Nenhum ID retornado ao inserir o formulário PF/Adesão.")

            return result[0]

    except Exception as e:
        logging.error(f"Erro ao inserir o formulário PF/Adesão: {e}")
        raise e


def add_formulario_porte1(ticket_id, multinotas, login_multinotas, senha_multinotas, cod_corretor,
                          modalidade, operadora, nome_empresa, cnpj, copart, compulsorio,
                          aprov_carencia, vigencia, vencimento, odonto, tabela_regiao, contato_responsavel,
                          email, telefone, valor_cotacao, info_complementares, tipo_copart):
    try:
        cod_corretor = int(cod_corretor) if (cod_corretor) else None
        # Verifica se `valor_cotacao` é uma string para aplicar o replace; caso contrário, mantém o valor float
        if (isinstance(valor_cotacao, str)):
            valor_cotacao = float(valor_cotacao.replace("R$", "").replace(".", "").replace(",", "."))

        with instance_cursor() as cursor:
            query = """
                INSERT INTO formulario_emissao_porte1 (tickets_id, multinotas, login_multinotas, senha_multinotas,
                    cod_corretor, modalidade, operadora, nome_empresa, cnpj, copart, compulsorio,
                    aprov_carencia, vigencia, vencimento, odonto, tabela_regiao, contato_responsavel, email, telefone, valor_cotacao, info_complementares, tipo_copart)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id;
            """
            cursor.execute(query, (ticket_id, multinotas, login_multinotas, senha_multinotas, cod_corretor,
                                   modalidade, operadora, nome_empresa, cnpj, copart, compulsorio,
                                   aprov_carencia, vigencia, vencimento, odonto, tabela_regiao, contato_responsavel,
                                   email, telefone, valor_cotacao, info_complementares, tipo_copart))
            result = cursor.fetchone()

            if (result is None or len(result) < 1):
                raise Exception("Nenhum ID retornado ao inserir o formulário PME.")

            return result[0]

    except Exception as e:
        logging.error(f"Erro ao inserir o formulário PME: {e}")
        raise e


def add_beneficiario(tipo_beneficiario, nome, plano, acomodacao, faixa_etaria, grau_parentesco, civil,
                     dt_nasci, idade, telefone, email, formulario_pfadesao_id=None, formulario_porte1_id=None):
    try:
        with instance_cursor() as cursor:
            query = """
                INSERT INTO beneficiarios_formulario (tipo_beneficiario, nome, plano, acomodacao, faixa_etaria,
                    grau_parentesco, civil, dt_nasci, idade, telefone, email, formulario_pfadesao_id, formulario_porte1_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id;
            """
            cursor.execute(query, (tipo_beneficiario, nome, plano, acomodacao, faixa_etaria, grau_parentesco, civil,
                                   dt_nasci, idade, telefone, email, formulario_pfadesao_id, formulario_porte1_id))
            result = cursor.fetchone()

            if (result is None):
                raise Exception("Falha ao inserir o beneficiário: Nenhum ID retornado.")

            return result[0]
    except Exception as e:
        logging.error(f"Erro ao inserir o beneficiário: {e}")
        raise e

def atualizar_protocolo(ticket_id, protocolo):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE tickets
                SET protocolo = %s
                WHERE id = %s;
            """
            cursor.execute(query, (protocolo, ticket_id))
    except Exception as e:
        logging.error(f"Erro ao atualizar o protocolo do ticket {ticket_id}: {e}")
        raise e


def adicionar_horas_uteis(data_inicial, horas_uteis):
    horas_restantes = horas_uteis

    while (horas_restantes > 0):
        # Se for um dia útil (segunda a sexta)
        if (data_inicial.weekday() < 5):
            # Avança um dia completo, subtraindo 24 horas da contagem de horas restantes
            data_inicial += timedelta(hours=24)
            horas_restantes -= 24
        else:
            # Se for fim de semana, avança para a próxima segunda-feira
            days_to_monday = 7 - data_inicial.weekday()
            data_inicial += timedelta(days=days_to_monday)
    return data_inicial


def listar_todos_chamados():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT t.id, t.user_id, t.setor_id, t.assunto, t.description, t.status, t.created_at, t.ultima_interacao, t.protocolo, t.modalidade, t.operadora, t.tipo_chamado, t.user_respons_id,
                       u.nome as usuario_nome, s.nome as setor_nome, ur.nome AS responsavel_nome, t.proposta_cadastrada
                FROM tickets t
                JOIN users u ON t.user_id = u.id
                JOIN setores s ON t.setor_id = s.id
                LEFT JOIN public.users ur ON t.user_respons_id = ur.id
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query)
            chamados = cursor.fetchall()

            # Converte para uma lista de dicionários
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'assunto': chamado[3],
                'description': chamado[4],
                'status': chamado[5],
                'created_at': chamado[6] - timedelta(hours=3),
                'ultima_interacao': chamado[7] - timedelta(hours=3) if (chamado[7]) else None,
                'usuario_nome': chamado[13],
                'setor_nome': chamado[14],
                'protocolo': chamado[8],
                'modalidade': chamado[9],
                'user_responsavel': chamado[15],
                'tipo_chamado': chamado[11],
                'proposta_cadastrada': chamado[16],
                'operadora': chamado[10]
            } for chamado in chamados]

            # Calcular a data limite da próxima interação e dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] == "Resolvido" or chamado['status'] == "Aguardando Solicitante"):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

                # Verifica se a proposta está cadastrada ou não.
                if (chamado['proposta_cadastrada'] == False):
                    chamado['prop_c'] = "Não"
                else:
                    chamado['prop_c'] = "Sim"

            return chamados_dict
    except Exception as e:
        logging.error(f"Erro ao listar chamados com detalhes: {e}")
        return []


def listar_chamados_por_setor(setor_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT t.id, t.user_id, t.setor_id, t.assunto, t.description, t.status, t.created_at, t.ultima_interacao, t.protocolo, t.modalidade, t.operadora, t.tipo_chamado, t.user_respons_id,
                       u.nome as usuario_nome, s.nome as setor_nome, ur.nome AS responsavel_nome, t.proposta_cadastrada, t.operadora
                FROM tickets t
                JOIN users u ON t.user_id = u.id
                JOIN setores s ON t.setor_id = s.id
                LEFT JOIN public.users ur ON t.user_respons_id = ur.id
                WHERE t.setor_id = %s
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query, (setor_id,))
            chamados = cursor.fetchall()

            # Converte para uma lista de dicionários
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'assunto': chamado[3],
                'description': chamado[4],
                'status': chamado[5],
                'created_at': chamado[6] - timedelta(hours=3),
                'ultima_interacao': chamado[7] - timedelta(hours=3) if (chamado[7]) else None,
                'usuario_nome': chamado[13],
                'setor_nome': chamado[14],
                'protocolo': chamado[8],
                'modalidade': chamado[9],
                'user_responsavel': chamado[15],
                'tipo_chamado': chamado[11],
                'proposta_cadastrada': chamado[16],
                'operadora': chamado[17]
            } for chamado in chamados]

            # Calcular a data limite da próxima interação e dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] == "Resolvido" or chamado['status'] == "Aguardando Solicitante"):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

                # Verifica se a proposta está cadastrada ou não.
                if (chamado['proposta_cadastrada'] == False):
                    chamado['prop_c'] = "Não"
                else:
                    chamado['prop_c'] = "Sim"

            return chamados_dict
    except Exception as e:
        logging.error(f"Erro ao listar chamados por setor: {e}")
        return []


def listar_chamados_brcall():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT t.id, t.user_id, t.setor_id, t.assunto, t.description, t.status, t.created_at, t.ultima_interacao, t.protocolo, t.modalidade, t.operadora, t.tipo_chamado, t.user_respons_id,
                       u.nome as usuario_nome, s.nome as setor_nome, ur.nome AS responsavel_nome, t.proposta_cadastrada, t.operadora
                FROM tickets t
                JOIN users u ON t.user_id = u.id
                JOIN setores s ON t.setor_id = s.id
                LEFT JOIN public.users ur ON t.user_respons_id = ur.id
                WHERE u.setor_id IN (9, 10)
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query)
            chamados = cursor.fetchall()

            # Converte para uma lista de dicionários
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'assunto': chamado[3],
                'description': chamado[4],
                'status': chamado[5],
                'created_at': chamado[6] - timedelta(hours=3),
                'ultima_interacao': chamado[7] - timedelta(hours=3) if (chamado[7]) else None,
                'usuario_nome': chamado[13],
                'setor_nome': chamado[14],
                'protocolo': chamado[8],
                'modalidade': chamado[9],
                'user_responsavel': chamado[15],
                'tipo_chamado': chamado[11],
                'proposta_cadastrada': chamado[16],
                'operadora': chamado[17]
            } for chamado in chamados]

            # Calcular a data limite da próxima interação e dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] == "Resolvido" or chamado['status'] == "Aguardando Solicitante"):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

                # Verifica se a proposta está cadastrada ou não.
                if (chamado['proposta_cadastrada'] == False):
                    chamado['prop_c'] = "Não"
                else:
                    chamado['prop_c'] = "Sim"

            return chamados_dict
    except Exception as e:
        logging.error(f"Erro ao listar chamados por setor: {e}")
        return []


def listar_chamados_usina():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    t.id,
                    t.user_id,
                    t.setor_id,
                    t.assunto,
                    t.description,
                    t.status,
                    t.created_at,
                    t.ultima_interacao,
                    t.protocolo,
                    t.modalidade,
                    t.operadora,
                    t.tipo_chamado,
                    t.user_respons_id,
                    u.nome AS usuario_nome,
                    s.nome AS setor_nome,
                    ur.nome AS responsavel_nome,
                    t.proposta_cadastrada,
                    t.operadora
                FROM tickets t
                JOIN users u
                    ON t.user_id = u.id
                JOIN setores s
                    ON t.setor_id = s.id
                LEFT JOIN public.users ur
                    ON t.user_respons_id = ur.id
                WHERE t.setor_id = 1
                AND (u.setor_id IS NULL OR u.setor_id NOT IN (9, 10, 13))
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query)
            chamados = cursor.fetchall()

            # Converte para uma lista de dicionários
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'assunto': chamado[3],
                'description': chamado[4],
                'status': chamado[5],
                'created_at': chamado[6] - timedelta(hours=3),
                'ultima_interacao': chamado[7] - timedelta(hours=3) if (chamado[7]) else None,
                'usuario_nome': chamado[13],
                'setor_nome': chamado[14],
                'protocolo': chamado[8],
                'modalidade': chamado[9],
                'user_responsavel': chamado[15],
                'tipo_chamado': chamado[11],
                'proposta_cadastrada': chamado[16],
                'operadora': chamado[17]
            } for chamado in chamados]

            # Calcular a data limite da próxima interação e dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] == "Resolvido" or chamado['status'] == "Aguardando Solicitante"):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

                # Verifica se a proposta está cadastrada ou não.
                if (chamado['proposta_cadastrada'] == False):
                    chamado['prop_c'] = "Não"
                else:
                    chamado['prop_c'] = "Sim"

            return chamados_dict
    except Exception as e:
        logging.error(f"Erro ao listar chamados por setor: {e}")
        return []


def listar_chamados_rj():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    t.id,
                    t.user_id,
                    t.setor_id,
                    t.assunto,
                    t.description,
                    t.status,
                    t.created_at,
                    t.ultima_interacao,
                    t.protocolo,
                    t.modalidade,
                    t.operadora,
                    t.tipo_chamado,
                    t.user_respons_id,
                    u.nome AS usuario_nome,
                    s.nome AS setor_nome,
                    ur.nome AS responsavel_nome,
                    t.proposta_cadastrada,
                    t.operadora
                FROM tickets t
                JOIN users u
                    ON t.user_id = u.id
                JOIN setores s
                    ON t.setor_id = s.id
                LEFT JOIN public.users ur
                    ON t.user_respons_id = ur.id
                WHERE u.setor_id IN (12)
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query)
            chamados = cursor.fetchall()

            # Converte para uma lista de dicionários
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'assunto': chamado[3],
                'description': chamado[4],
                'status': chamado[5],
                'created_at': chamado[6] - timedelta(hours=3),
                'ultima_interacao': chamado[7] - timedelta(hours=3) if (chamado[7]) else None,
                'usuario_nome': chamado[13],
                'setor_nome': chamado[14],
                'protocolo': chamado[8],
                'modalidade': chamado[9],
                'user_responsavel': chamado[15],
                'tipo_chamado': chamado[11],
                'proposta_cadastrada': chamado[16],
                'operadora': chamado[17]
            } for chamado in chamados]

            # Calcular a data limite da próxima interação e dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] == "Resolvido" or chamado['status'] == "Aguardando Solicitante"):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

                # Verifica se a proposta está cadastrada ou não.
                if (chamado['proposta_cadastrada'] == False):
                    chamado['prop_c'] = "Não"
                else:
                    chamado['prop_c'] = "Sim"

            return chamados_dict
    except Exception as e:
        logging.error(f"Erro ao listar chamados por setor: {e}")
        return []


def listar_chamados_luanca():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    t.id,
                    t.user_id,
                    t.setor_id,
                    t.assunto,
                    t.description,
                    t.status,
                    t.created_at,
                    t.ultima_interacao,
                    t.protocolo,
                    t.modalidade,
                    t.operadora,
                    t.tipo_chamado,
                    t.user_respons_id,
                    u.nome AS usuario_nome,
                    s.nome AS setor_nome,
                    ur.nome AS responsavel_nome,
                    t.proposta_cadastrada,
                    t.operadora
                FROM tickets t
                JOIN users u
                    ON t.user_id = u.id
                JOIN setores s
                    ON t.setor_id = s.id
                LEFT JOIN public.users ur
                    ON t.user_respons_id = ur.id
                WHERE u.setor_id IN (13)
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query)
            chamados = cursor.fetchall()

            # Converte para uma lista de dicionários
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'assunto': chamado[3],
                'description': chamado[4],
                'status': chamado[5],
                'created_at': chamado[6] - timedelta(hours=3),
                'ultima_interacao': chamado[7] - timedelta(hours=3) if (chamado[7]) else None,
                'usuario_nome': chamado[13],
                'setor_nome': chamado[14],
                'protocolo': chamado[8],
                'modalidade': chamado[9],
                'user_responsavel': chamado[15],
                'tipo_chamado': chamado[11],
                'proposta_cadastrada': chamado[16],
                'operadora': chamado[17]
            } for chamado in chamados]

            # Calcular a data limite da próxima interação e dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] == "Resolvido" or chamado['status'] == "Aguardando Solicitante"):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

                # Verifica se a proposta está cadastrada ou não.
                if (chamado['proposta_cadastrada'] == False):
                    chamado['prop_c'] = "Não"
                else:
                    chamado['prop_c'] = "Sim"

            return chamados_dict
    except Exception as e:
        logging.error(f"Erro ao listar chamados por setor: {e}")
        return []


def consulta_assistentes_luanca():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    id, nome
                FROM
                    public.users
                WHERE
                    setor_id = 13
                    AND tipo_usuario IN (2, 4);
            """
            cursor.execute(query)
            assistentes = cursor.fetchall()

            # Convert to list of dictionaries
            assistentes_dict = []
            for assistente in assistentes:
                assistente_dict = {
                    'id': assistente[0],
                    'nome': assistente[1]
                }
                assistentes_dict.append(assistente_dict)

            return assistentes_dict
    except Exception as e:
        logging.error(f"Erro ao consultar assistentes Luanca: {e}")
        return []


def atualizar_assistente_chamado(chamado_id, user_id):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE tickets
                SET user_id = %s
                WHERE id = %s
            """
            cursor.execute(query, (user_id, chamado_id))
            cursor.connection.commit()
            return True
    except Exception as e:
        logging.error(f"Erro ao atualizar o responsável do chamado {chamado_id}: {e}")
        return False



def listar_chamados_por_user(user_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT t.id, t.user_id, t.setor_id, t.protocolo, t.assunto, t.tipo_chamado, t.status, t.created_at, t.ultima_interacao, t.user_respons_id,
                       u.nome as usuario_nome, s.nome as setor_nome, ur.nome AS responsavel_nome
                FROM tickets t
                JOIN users u ON t.user_id = u.id
                JOIN setores s ON t.setor_id = s.id
                LEFT JOIN public.users ur ON t.user_respons_id = ur.id
                WHERE t.user_id = %s
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query, (user_id,))
            chamados = cursor.fetchall()

            # Convert to list of dictionaries
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'protocolo': chamado[3],
                'assunto': chamado[4],
                'tipo_chamado': chamado[5],
                'status': chamado[6],
                'created_at': chamado[7] - timedelta(hours=3),
                'ultima_interacao': chamado[8] - timedelta(hours=3) if (chamado[8]) else None,
                'user_respons_id': chamado[9],
                'usuario_nome': chamado[10],
                'setor_nome': chamado[11],
                'user_responsavel': chamado[12]
            } for chamado in chamados]

            # Calculate next interaction deadline and dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] == "Resolvido" or chamado['status'] == "Aguardando Solicitante"):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

            return chamados_dict
    except Exception as e:
        logging.error(f"Error listing tickets by user: {e}")
        return []


def listar_chamados_franquia(user_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT t.id, t.user_id, t.setor_id, t.protocolo, t.assunto, t.tipo_chamado, t.status, t.created_at, t.ultima_interacao, t.user_respons_id,
                       u.nome as usuario_nome, s.nome as setor_nome, ur.nome AS responsavel_nome
                FROM tickets t
                JOIN users u ON t.user_id = u.id
                JOIN setores s ON t.setor_id = s.id
                LEFT JOIN public.users ur ON t.user_respons_id = ur.id
                WHERE t.user_id = %s OR u.tipo_usuario = 6
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query, (user_id,))
            chamados = cursor.fetchall()

            # Convert to list of dictionaries
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'protocolo': chamado[3],
                'assunto': chamado[4],
                'tipo_chamado': chamado[5],
                'status': chamado[6],
                'created_at': chamado[7] - timedelta(hours=3),
                'ultima_interacao': chamado[8] - timedelta(hours=3) if (chamado[8]) else None,
                'user_respons_id': chamado[9],
                'usuario_nome': chamado[10],
                'setor_nome': chamado[11],
                'user_responsavel': chamado[12]
            } for chamado in chamados]

            # Calculate next interaction deadline and dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] in ["Resolvido", "Aguardando Solicitante"]):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

            return chamados_dict
    except Exception as e:
        logging.error(f"Error listing tickets by user: {e}")
        return []
    

def listar_chamados_rj_colaborador(user_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT t.id, t.user_id, t.setor_id, t.protocolo, t.assunto, t.tipo_chamado, t.status, t.created_at, t.ultima_interacao, t.user_respons_id,
                       u.nome as usuario_nome, s.nome as setor_nome, ur.nome AS responsavel_nome
                FROM tickets t
                JOIN users u ON t.user_id = u.id
                JOIN setores s ON t.setor_id = s.id
                LEFT JOIN public.users ur ON t.user_respons_id = ur.id
                WHERE t.user_id = %s OR u.setor_id = 12
                ORDER BY t.ultima_interacao ASC;
            """
            cursor.execute(query, (user_id,))
            chamados = cursor.fetchall()

            # Convert to list of dictionaries
            chamados_dict = [{
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'protocolo': chamado[3],
                'assunto': chamado[4],
                'tipo_chamado': chamado[5],
                'status': chamado[6],
                'created_at': chamado[7] - timedelta(hours=3),
                'ultima_interacao': chamado[8] - timedelta(hours=3) if (chamado[8]) else None,
                'user_respons_id': chamado[9],
                'usuario_nome': chamado[10],
                'setor_nome': chamado[11],
                'user_responsavel': chamado[12]
            } for chamado in chamados]

            # Calculate next interaction deadline and dias_aberto
            for chamado in chamados_dict:
                if (chamado['status'] in ["Resolvido", "Aguardando Solicitante"]):
                    chamado['data_limite'] = None
                elif (chamado['ultima_interacao']):
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['ultima_interacao'], 72)
                else:
                    chamado['data_limite'] = adicionar_horas_uteis(chamado['created_at'], 72)

                # Calcular os dias que o chamado está aberto (dias_aberto) com arredondamento para cima
                if (chamado['status'] == "Resolvido" and chamado['ultima_interacao']):
                    dias_aberto = (chamado['ultima_interacao'] - chamado['created_at']).total_seconds() / (24 * 3600)
                else:
                    dias_aberto = (datetime.now() - chamado['created_at']).total_seconds() / (24 * 3600)

                chamado['dias_aberto'] = math.ceil(dias_aberto)

            return chamados_dict
    except Exception as e:
        logging.error(f"Error listing tickets by user: {e}")
        return []


def consulta_detalhes_chamado(chamado_id):
    try:
        with instance_cursor() as cursor:
            # Consulta inicial para obter os dados do chamado, incluindo o sub-status atual
            query = """
                SELECT t.id, t.user_id, t.setor_id, t.assunto, t.description, t.status, t.created_at, t.ultima_interacao,
                       t.protocolo, u.nome as usuario_nome, s.nome as setor_nome, t.proposta_cadastrada, t.modalidade,
                       ts.sub_status, u.tipo_usuario
                FROM tickets t
                JOIN users u ON t.user_id = u.id
                JOIN setores s ON t.setor_id = s.id
                LEFT JOIN ticket_substatus ts ON t.id = ts.ticket_id AND t.status = ts.status
                WHERE t.id = %s;
            """
            cursor.execute(query, (chamado_id,))
            chamado = cursor.fetchone()

            if (not chamado):
                logging.info(f"Chamado {chamado_id} não encontrado.")
                return None

            # Mapeia os campos do chamado, incluindo o sub_status
            chamado_dict = {
                'id': chamado[0],
                'user_id': chamado[1],
                'setor_id': chamado[2],
                'assunto': chamado[3],
                'description': chamado[4],
                'status': chamado[5],
                'sub_status': chamado[13],  # Inclui o sub_status no dicionário
                'created_at': chamado[6] - timedelta(hours=3) if (chamado[6]) else None,
                'ultima_interacao': chamado[7] - timedelta(hours=3) if (chamado[7]) else None,
                'usuario_nome': chamado[9],
                'setor_nome': chamado[10],
                'protocolo': chamado[8],
                'proposta_cadastrada': chamado[11],
                'prop_c': "Sim" if (chamado[11]) else "Não",
                'modalidade': chamado[12],
                'tipo_usuario': chamado[14]
            }

            # Determina a modalidade e consulta o formulário correto
            modalidade = chamado_dict['modalidade']
            if (modalidade in ["PME", "PME - Odontológico"]):
                tabela = "formulario_emissao_porte1"
                formulario_campos = """
                    id, tickets_id, multinotas, login_multinotas, senha_multinotas, cod_corretor, modalidade, operadora, nome_empresa,
                    cnpj, copart, tipo_copart, compulsorio, aprov_carencia, vigencia, vencimento, odonto, tabela_regiao,
                    contato_responsavel, email, telefone, cod_proposta, valor_cotacao, info_complementares
                """
                fk_beneficiario_coluna = "formulario_porte1_id"
            elif (modalidade in ["Adesão", "PF", "PF - Odontológico"]):
                tabela = "formulario_emissao_pfadesao"
                formulario_campos = """
                    id, tickets_id, multinotas, login_multinotas, senha_multinotas, cod_corretor, modalidade, operadora, administradora,
                    entidade, vigencia, copart, aprov_carencia, odonto, contato_respon, email, telefone, cod_proposta, valor_cotacao, info_complementares, tipo_copart
                """
                fk_beneficiario_coluna = "formulario_pfadesao_id"
            else:
                logging.warning(f"Modalidade {modalidade} não é válida para consulta.")
                return chamado_dict

            # Consulta os dados do formulário correspondente
            formulario_query = f"SELECT {formulario_campos} FROM {tabela} WHERE tickets_id = %s"
            cursor.execute(formulario_query, (chamado_id,))
            formulario = cursor.fetchone()

            if (formulario):
                # Mapear o formulário específico
                if (modalidade in ["PME", "PME - Odontológico"]):
                    # Formatação dos campos específicos para PME
                    valor_cotacao = f"R$ {formulario[22]:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".") if (formulario[22]) else "--"
                    vigencia = formulario[14].strftime('%d/%m/%Y') if (formulario[14]) else "--"
                    vencimento = formulario[15].strftime('%d/%m/%Y') if (formulario[15]) else "--"

                    formulario_dict = {
                        'Multinotas': "Sim" if (formulario[2]) else "Não",
                        'Login do Corretor': formulario[3] or "--",
                        'Senha  do Corretor': formulario[4] or "--",
                        'Código do Corretor': formulario[5] or "--",
                        'Modalidade': formulario[6] or "--",
                        'Operadora': formulario[7] or "--",
                        'Nome da Empresa': formulario[8] or "--",
                        'CNPJ': formulario[9] or "--",
                        'Coparticipação': "Sim" if formulario[10] else "Não",
                        'Tipo da Coparticipação': formulario[11] or "--",
                        'Compulsório': "Sim" if formulario[12] else "Não",
                        'Aproveitamento de Carência': "Sim" if formulario[13] else "Não",
                        'Vigência': vigencia,
                        'Vencimento': vencimento,
                        'Odontológico Incluso': "Sim" if (formulario[16]) else "Não",
                        'Tabela/Região': formulario[17] or "--",
                        'Contato do Responsável': formulario[18] or "--",
                        'E-mail': formulario[19] or "--",
                        'Telefone': formulario[20] or "--",
                        'Valor da Cotação': valor_cotacao,
                        'Código da Proposta': formulario[21] or "--",
                        'Informações Complementares': formulario[23] or "--"
                    }
                else:
                    # Formatação dos campos específicos para PF
                    valor_cotacao = f"R$ {formulario[18]:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".") if (formulario[18]) else "--"
                    vigencia = formulario[10].strftime('%d/%m/%Y') if (formulario[10]) else "--"

                    formulario_dict = {
                        'Multinotas': "Sim" if (formulario[2]) else "Não",
                        'Login do Corretor': formulario[3] or "--",
                        'Senha  do Corretor': formulario[4] or "--",
                        'Código do Corretor': formulario[5] or "--",
                        'Modalidade': formulario[6] or "--",
                        'Operadora': formulario[7] or "--",
                        'Administradora': formulario[8] or "--",
                        'Entidade': formulario[9] or "--",
                        'Vigência': vigencia,
                        'Coparticipação': "Sim" if formulario[11] else "Não",
                        'Tipo de Coparticipação': formulario[20] or "--",
                        'Aproveitamento de Carência': "Sim" if formulario[12] else "Não",
                        'Odontológico Incluso': "Sim" if formulario[13] else "Não",
                        'Contato do Responsável': formulario[14] or "--",
                        'E-mail': formulario[15] or "--",
                        'Telefone': formulario[16] or "--",
                        'Valor da Cotação': valor_cotacao,
                        'Código da Proposta': formulario[17] or "--",
                        'Informações Complementares': formulario[19] or "--",
                    }

                chamado_dict['formulario'] = formulario_dict

                # Consulta dos beneficiários associados ao formulário
                beneficiarios_query = f"""
                    SELECT id, tipo_beneficiario, nome, plano, acomodacao, faixa_etaria, grau_parentesco, civil, dt_nasci,
                           idade, telefone, email
                    FROM beneficiarios_formulario
                    WHERE {fk_beneficiario_coluna} = %s
                """
                cursor.execute(beneficiarios_query, (formulario[0],))
                beneficiarios = cursor.fetchall()

                # Mapear os beneficiários para o dicionário
                beneficiarios_lista = []
                for b in beneficiarios:
                    beneficiario_dict = {
                        'id': b[0],
                        'tipo_beneficiario': b[1],
                        'nome': b[2],
                        'plano': b[3],
                        'acomodacao': b[4],
                        'faixa_etaria': b[5],
                        'grau_parentesco': b[6],
                        'civil': b[7],
                        'dt_nasci': b[8],
                        'idade': b[9],
                        'telefone': b[10],
                        'email': b[11]
                    }
                    beneficiarios_lista.append(beneficiario_dict)

                chamado_dict['beneficiarios'] = beneficiarios_lista
            else:
                logging.info(f"Nenhum formulário encontrado para chamado {chamado_id} com a modalidade {modalidade}.")

            return chamado_dict

    except Exception as e:
        logging.error(f"Erro ao consultar detalhes do chamado {chamado_id}: {e}")
        return None


def atualizar_status_chamado(chamado_id, novo_status):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE tickets
                SET status = %s, ultima_interacao = NOW()
                WHERE id = %s;
            """
            cursor.execute(query, (novo_status, chamado_id))
    except Exception as e:
        logging.error(f"Erro ao atualizar o status do chamado {chamado_id}: {e}")
        raise e


def atualizar_substatus_chamado(status, sub_status, user_id, chamado_id):
    try:
        with instance_cursor() as cursor:
            query = """
                INSERT INTO ticket_substatus (ticket_id, status, sub_status, user_id, atualizado_em)
                VALUES (%s, %s, %s, %s, NOW())
                ON CONFLICT (ticket_id, status)
                DO UPDATE SET
                    sub_status = EXCLUDED.sub_status,
                    user_id = EXCLUDED.user_id,
                    atualizado_em = NOW()
                RETURNING *;
            """
            cursor.execute(query, (chamado_id, status, sub_status, user_id))
            result = cursor.fetchone()

            if (result):
                logging.info(f"Substatus do chamado {chamado_id} atualizado/inserido para o status '{status}' com sucesso.")
                return True
            else:
                logging.warning(f"Nenhuma alteração feita para o chamado {chamado_id}.")
                return False
    except Exception as e:
        logging.error(f"Erro ao atualizar o substatus do chamado {chamado_id}: {e}")
        raise


def atualizar_propC_chamado(chamado_id, novo_proposta):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE tickets
                SET proposta_cadastrada = %s
                WHERE id = %s;
            """
            cursor.execute(query, (novo_proposta, chamado_id))
            cursor.connection.commit()  # Confirma a transação
    except Exception as e:
        logging.error(f"Erro ao atualizar o status do chamado {chamado_id}: {e}")
        raise e


def atualizar_formulario_pme(cod_proposta, chamado_id):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE formulario_emissao_porte1
                SET cod_proposta = %s
                WHERE tickets_id = %s;
            """
            cursor.execute(query, (cod_proposta, chamado_id))
            cursor.connection.commit()
    except Exception as e:
        logging.error(f"Erro ao atualizar o status do chamado {chamado_id}: {e}")
        raise e


def atualizar_formulario_pf(cod_proposta, chamado_id):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE formulario_emissao_pfadesao
                SET cod_proposta = %s
                WHERE tickets_id = %s;
            """
            cursor.execute(query, (cod_proposta, chamado_id))
            cursor.connection.commit()
    except Exception as e:
        logging.error(f"Erro ao atualizar o código da proposta {chamado_id}: {e}")
        raise e


def chat_ticket(chamado_id, user_id, mensagem):
    try:
        with instance_cursor() as cursor:
            # Recupera o user_respons_id (usuário responsável pelo ticket) e o solicitante_id
            query_responsavel = """
                SELECT COALESCE(user_respons_id, user_id) AS user_respons_id, user_id
                FROM tickets
                WHERE id = %s;
            """
            cursor.execute(query_responsavel, (chamado_id,))
            result = cursor.fetchone()

            # Verificar se result é None
            if (result is None):
                logging.error(f"Chamado {chamado_id} não encontrado.")
                raise ValueError("Chamado não encontrado.")

            # Converte valores para int e define user_respons_id para o solicitante_id se necessário
            user_respons_id = int(result[0])  # Responsável (ou solicitante se responsável for None)
            solicitante_id = int(result[1])    # Solicitante

            # Atualiza a última interação no ticket apenas se o remetente não for o solicitante
            if (user_id != solicitante_id):
                query_status = """
                    UPDATE tickets
                    SET ultima_interacao = NOW()
                    WHERE id = %s;
                """
                cursor.execute(query_status, (chamado_id,))

            if (mensagem):
                # Insere a nova mensagem
                query_mensagem = """
                    INSERT INTO conversa_ticket (ticket_id, user_id, mensagem, created_at)
                    VALUES (%s, %s, %s, NOW()) RETURNING id;
                """
                cursor.execute(query_mensagem, (chamado_id, user_id, mensagem))
                conversa_id = cursor.fetchone()[0]

                # Força a conversão de user_id para int
                user_id = int(user_id)

                # Verifica se o remetente da mensagem é o solicitante ou o responsável
                if (user_id == solicitante_id):
                    # O solicitante visualiza a própria mensagem (TRUE) e o responsável ainda não visualizou (FALSE)
                    query_visualizacao = """
                        INSERT INTO visualizacao_msg_ticket (ticket_id, conversa_id, user_id, visualizado, data_visualizacao)
                        VALUES (%s, %s, %s, TRUE, NOW()),   -- Solicitante visualizou
                               (%s, %s, %s, FALSE, NULL);    -- Responsável ainda não visualizou
                    """
                    cursor.execute(
                        query_visualizacao,
                        (
                            chamado_id,
                            conversa_id,
                            solicitante_id,
                            chamado_id,
                            conversa_id,
                            user_respons_id,
                        ),
                    )
                else:
                    # O responsável visualiza (TRUE) e o solicitante ainda não visualizou (FALSE)
                    query_visualizacao = """
                        INSERT INTO visualizacao_msg_ticket (ticket_id, conversa_id, user_id, visualizado, data_visualizacao)
                        VALUES (%s, %s, %s, TRUE, NOW()),   -- Responsável visualizou
                               (%s, %s, %s, FALSE, NULL);    -- Solicitante ainda não visualizou
                    """
                    cursor.execute(
                        query_visualizacao,
                        (
                            chamado_id,
                            conversa_id,
                            user_respons_id,
                            chamado_id,
                            conversa_id,
                            solicitante_id,
                        ),
                    )

    except Exception as e:
        logging.error(
            f"Erro ao atualizar status e salvar resposta do chamado {chamado_id}: {e}"
        )
        raise e


def atualizar_visualizacao_mensagens(chamado_id, user_id):
    try:
        with instance_cursor() as cursor:
            logging.info(f"Atualizando visualização para ticket {chamado_id} e usuário {user_id}")

            query_visualizacao = """
                UPDATE visualizacao_msg_ticket
                SET visualizado = TRUE, data_visualizacao = NOW()
                WHERE ticket_id = %s AND user_id = %s AND visualizado = FALSE;
            """
            cursor.execute(query_visualizacao, (chamado_id, user_id))
            logging.info(f"{cursor.rowcount} registros atualizados para visualização")

            if (cursor.rowcount == 0):
                logging.warning(f"Nenhuma mensagem para marcar como visualizada para o chamado {chamado_id} e usuário {user_id}")
            else:
                logging.info(f"Mensagens visualizadas com sucesso para o chamado {chamado_id} pelo usuário {user_id}")

    except Exception as e:
        logging.error(f"Erro ao atualizar a visualização das mensagens do chamado {chamado_id}: {e}")
        raise e


def listar_mensagens_chamado(chamado_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT ct.user_id, ct.mensagem, ct.created_at, u.nome, u.profile_image_url
                FROM conversa_ticket ct
                JOIN users u ON ct.user_id = u.id
                WHERE ct.ticket_id = %s
                ORDER BY ct.created_at ASC;
            """
            cursor.execute(query, (chamado_id,))
            mensagens = cursor.fetchall()

            # Converter para uma lista de dicionários
            mensagens_dict = [{
                'user_id': mensagem[0],
                'mensagem': mensagem[1],
                'created_at': mensagem[2] - timedelta(hours=3),  # Ajustando timezone
                'usuario_nome': mensagem[3],
                'profile_image_url': mensagem[4] if (mensagem[4]) else '/static/default-avatar.png'
            } for mensagem in mensagens]

            return mensagens_dict
    except Exception as e:
        logging.error(f"Erro ao listar mensagens do chamado {chamado_id}: {e}")
        return []


def consulta_ticket_file(ticket_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    id,
                    file_name,
                    REPLACE(file_url, 'https://intranet-picture-profile.s3.amazonaws.com/', '') AS file_url,
                    uploaded_at,
                    file_url
                FROM
                    ticket_files
                WHERE ticket_id = %s;
            """
            cursor.execute(query, (ticket_id,))
            files = cursor.fetchall()

            files_dict = []
            for file in files:
                # Ajuste de fuso horário
                data_upload = file[3] - timedelta(hours=3)

                # Formatação da data após ajustar o fuso horário
                data_upload_formatada = data_upload.strftime('%d/%m/%Y - %H:%M:%S')

                files_dict.append({
                    'id': file [0],
                    'nome_arquivo': file[1],
                    'arquivo': file[2],
                    'data_upload': data_upload_formatada,
                    'url_arquivo': file[4]
                })

            return files_dict
    except Exception as e:
        logging.error(f"Erro ao listar os arquivos do chamdo {ticket_id}: {e}")


def delete_ticket_file(file_id):
    try:
        with instance_cursor() as cursor:
            query = """
                DELETE FROM ticket_files
                WHERE id = %s;
            """
            cursor.execute(query, (file_id,))
            logging.info(f"Arquivo com ID {file_id} deletado do banco de dados com sucesso.")
    except Exception as e:
        logging.error(f"Erro ao deletar o arquivo do banco de dados: {e}")
        raise e


def atualizar_responsavel_chamado(chamado_id, user_id):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE tickets
                SET user_respons_id = %s
                WHERE id = %s AND status = 'Aberto'
            """
            cursor.execute(query, (user_id, chamado_id))
            cursor.connection.commit()  # Confirma a transação
            return True
    except Exception as e:
        logging.error(f"Erro ao atualizar o responsável do chamado {chamado_id}: {e}")
        return False


def consulta_ticket_file(ticket_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    id,
                    file_name,
                    REPLACE(file_url, 'https://intranet-picture-profile.s3.amazonaws.com/', '') AS file_url,
                    uploaded_at,
                    file_url
                FROM
                    ticket_files
                WHERE ticket_id = %s;
            """
            cursor.execute(query, (ticket_id,))
            files = cursor.fetchall()

            files_dict = []
            for file in files:
                # Ajuste de fuso horário
                data_upload = file[3] - timedelta(hours=3)

                # Formatação da data após ajustar o fuso horário
                data_upload_formatada = data_upload.strftime('%d/%m/%Y - %H:%M:%S')

                files_dict.append({
                    'id': file [0],
                    'nome_arquivo': file[1],
                    'arquivo': file[2],
                    'data_upload': data_upload_formatada,
                    'url_arquivo': file[4]
                })

            return files_dict
    except Exception as e:
        logging.error(f"Erro ao listar os arquivos do chamdo {ticket_id}: {e}")


def delete_ticket_file(file_id):
    try:
        with instance_cursor() as cursor:
            query = """
                DELETE FROM ticket_files
                WHERE id = %s;
            """
            cursor.execute(query, (file_id,))
            logging.info(f"Arquivo com ID {file_id} deletado do banco de dados com sucesso.")
    except Exception as e:
        logging.error(f"Erro ao deletar o arquivo do banco de dados: {e}")
        raise e


def atualizar_responsavel_chamado(chamado_id, user_id):
    try:
        with instance_cursor() as cursor:
            query = """
                UPDATE tickets
                SET user_respons_id = %s
                WHERE id = %s AND status = 'Aberto'
            """
            cursor.execute(query, (user_id, chamado_id))
            cursor.connection.commit()  # Confirma a transação
            return True
    except Exception as e:
        logging.error(f"Erro ao atualizar o responsável do chamado {chamado_id}: {e}")
        return False

def add_acompanhamento(modalidade, data_list):
    try:
        with instance_cursor() as cursor:
            # Passo 1: Apagar todos os registros com a modalidade fornecida
            delete_query = """
                DELETE FROM acompanhamento_propostas
                WHERE modalidade = %s;
            """
            cursor.execute(delete_query, (modalidade,))
            logging.info(f"Registros com modalidade {modalidade} apagados.")

            # Passo 2: Inserir os novos registros
            insert_query = """
                INSERT INTO acompanhamento_propostas (
                    tipo_pme, porte, operadora, filial, unidade, local_recebimento, tipo_venda, linha_produto,
                    grupo_proposta, contrato_medico, contrato_dental, cnpj_empresa, nome_razao_social, e_kit,
                    nome_fantasia_contrato, natureza_juridica, tipo_produto, proposta_comissionada_medica,
                    proposta_comissionada_dental, tipo_comissionamento, motivo_nao_comissionar, normativa_venda,
                    numero_proposta, numero_cotacao, mes_referencia, vigencia_proposta, municipio, bairro, estado,
                    qtd_portabilidade_dental, qtd_portabilidade_medica, qtde_total_benef_medico, qtde_total_benef_dental,
                    valor_total_medica, valor_total_dental, valor_taxa_implantacao, valor_aditivos, nome_plano_medico,
                    nome_plano_dental, qtde_benef_por_plano, valor_benef_por_plano, cpf_corretor, nome_corretor,
                    cpf_supervisor, nome_supervisor, codigo_produtor, cnpj_produtor, nome_produtor, codigo_plataforma,
                    nome_plataforma, cpf_gestor_produtor_cnpj, nome_gestor_produtor_cnpj, situacao, possui_imagem,
                    data_protocolo, data_digitalizacao, data_digitacao, data_pronta_para_analise, data_em_analise,
                    qtd_devolucoes, data_primeira_devolucao, motivo_primeira_devolucao, envio_retificadora_primeira_devolucao,
                    obs_primeira_devolucao, motivos_devolucoes_beneficiarios, data_devolucao, motivo_ultima_devolucao,
                    envio_retificadora_ultima_devolucao, obs_ultima_devolucao, data_reapresentacao, data_envio_para_under,
                    data_analise_tecnica, data_cancelamento, data_declinio, data_implantacao, venda_online, venda_ecorretora,
                    motivo_cancelamento, obs_cancelamento, e_faturamento, pte, forma_pagto, obs_analise, compulsoria,
                    vigencia_contrato, data_assinatura, data_contrato_assinado, data_aceite_banco, data_aguard_confirm_pagto,
                    data_cancelada, situacao_prop_cancelada, forma_pagto_selecionada, forma_pagto_efetuada, assistente_sisweb,
                    beneficiarios_dental, beneficiarios_medica, data_retorno_apos_1_devolucao, data_retorno_apos_ult_devolucao,
                    forma_de_pagamento, idade_beneficiario, local_de_recebimento, motivo_envio_para_under,
                    nome_gestor_do_produtor_cnpj, nome_titular, normativa_de_vendas, observacao_primeira_devolucao,
                    observacao_ultima_devolucao, proposta_idoso, qtde_de_devolucoes, situacao_dentro_da_analise_tecnica,
                    tipo_de_contrato, valor_desconto, valor_liquido, modalidade, ultima_atualizacao
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, NOW()
                );
            """

            expected_columns = [
                'tipo_pme', 'porte', 'operadora', 'filial', 'unidade', 'local_recebimento', 'tipo_venda', 'linha_produto',
                'grupo_proposta', 'contrato_medico', 'contrato_dental', 'cnpj_empresa', 'nome_razao_social', 'e_kit',
                'nome_fantasia_contrato', 'natureza_juridica', 'tipo_produto', 'proposta_comissionada_medica',
                'proposta_comissionada_dental', 'tipo_comissionamento', 'motivo_nao_comissionar', 'normativa_venda',
                'numero_proposta', 'numero_cotacao', 'mes_referencia', 'vigencia_proposta', 'municipio', 'bairro', 'estado',
                'qtd_portabilidade_dental', 'qtd_portabilidade_medica', 'qtde_total_benef_medico', 'qtde_total_benef_dental',
                'valor_total_medica', 'valor_total_dental', 'valor_taxa_implantacao', 'valor_aditivos', 'nome_plano_medico',
                'nome_plano_dental', 'qtde_benef_por_plano', 'valor_benef_por_plano', 'cpf_corretor', 'nome_corretor',
                'cpf_supervisor', 'nome_supervisor', 'codigo_produtor', 'cnpj_produtor', 'nome_produtor', 'codigo_plataforma',
                'nome_plataforma', 'cpf_gestor_produtor_cnpj', 'nome_gestor_produtor_cnpj', 'situacao', 'possui_imagem',
                'data_protocolo', 'data_digitalizacao', 'data_digitacao', 'data_pronta_para_analise', 'data_em_analise',
                'qtd_devolucoes', 'data_primeira_devolucao', 'motivo_primeira_devolucao', 'envio_retificadora_primeira_devolucao',
                'obs_primeira_devolucao', 'motivos_devolucoes_beneficiarios', 'data_devolucao', 'motivo_ultima_devolucao',
                'envio_retificadora_ultima_devolucao', 'obs_ultima_devolucao', 'data_reapresentacao', 'data_envio_para_under',
                'data_analise_tecnica', 'data_cancelamento', 'data_declinio', 'data_implantacao', 'venda_online', 'venda_ecorretora',
                'motivo_cancelamento', 'obs_cancelamento', 'e_faturamento', 'pte', 'forma_pagto', 'obs_analise', 'compulsoria',
                'vigencia_contrato', 'data_assinatura', 'data_contrato_assinado', 'data_aceite_banco', 'data_aguard_confirm_pagto',
                'data_cancelada', 'situacao_prop_cancelada', 'forma_pagto_selecionada', 'forma_pagto_efetuada', 'assistente_sisweb',
                'beneficiarios_dental', 'beneficiarios_medica', 'data_retorno_apos_1_devolucao', 'data_retorno_apos_ult_devolucao',
                'forma_de_pagamento', 'idade_beneficiario', 'local_de_recebimento', 'motivo_envio_para_under',
                'nome_gestor_do_produtor_cnpj', 'nome_titular', 'normativa_de_vendas', 'observacao_primeira_devolucao',
                'observacao_ultima_devolucao', 'proposta_idoso', 'qtde_de_devolucoes', 'situacao_dentro_da_analise_tecnica',
                'tipo_de_contrato', 'valor_desconto', 'valor_liquido', 'modalidade'
            ]

            for data in data_list:
                # Filtrar e garantir que apenas as colunas esperadas sejam usadas
                cleaned_data = {key: data.get(key, None) for key in expected_columns}

                cleaned_data['modalidade'] = modalidade

                if (len(cleaned_data) != insert_query.count('%s')):
                    logging.error(f"Número de valores fornecidos ({len(cleaned_data)}) não corresponde ao número de colunas na query ({insert_query.count('%s')}).")
                    continue

                # Garantir que valores NaN, NaT ou nulos sejam convertidos para None (NULL no PostgreSQL)
                for key, value in cleaned_data.items():
                    if pd.isna(value):
                        cleaned_data[key] = None

                cursor.execute(insert_query, tuple(cleaned_data.values()))

            logging.info(f"{len(data_list)} novos registros inseridos para a modalidade {modalidade}.")
    except Exception as e:
        logging.error(f"Erro ao gerenciar o acompanhamento: {e}")
        raise e


def consulta_acompanhamento_geral():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    COALESCE(assistente_sisweb, 'Não Informado') as assistente,
                    situacao,
                    COUNT(*) as qtd_beneficiarios,
                    SUM(qtde_benef_por_plano) as soma_beneficiarios,
                    (ultima_atualizacao - INTERVAL '3 hours')::timestamp as ultima_atualizacao
                FROM
                    acompanhamento_propostas
                GROUP BY
                    assistente,
                    situacao,
                    ultima_atualizacao
                ORDER BY
                    assistente ASC,
                    situacao ASC;
            """
            cursor.execute(query)
            acompanhamento = cursor.fetchall()

            # Formatar os dados para o front-end
            acompanhamento_formatado = {}
            for acomp in acompanhamento:
                assistente = acomp[0]
                if (assistente not in acompanhamento_formatado):
                    acompanhamento_formatado[assistente] = []

                acompanhamento_formatado[assistente].append({
                    'situacao': acomp[1],
                    'qtd_beneficiarios': acomp[2],
                    'soma_beneficiarios': acomp[3],
                    'ultima_atualizacao': acomp[4].strftime('%d/%m/%Y %H:%M:%S') if acomp[4] else None
                })

            return acompanhamento_formatado
    except Exception as e:
        logging.error(f"Erro ao consultar acompanhamento: {e}")
        return None


def consulta_acompanhamento_assistente(assistente):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    COALESCE(assistente_sisweb, 'Não Informado') as assistente,
                    situacao,
                    COUNT(*) as qtd_beneficiarios,
                    SUM(qtde_benef_por_plano) as soma_beneficiarios,
                    (ultima_atualizacao - INTERVAL '3 hours')::timestamp as ultima_atualizacao
                FROM
                    acompanhamento_propostas
                WHERE
                    assistente_sisweb = %s
                GROUP BY
                    assistente,
                    situacao,
                    ultima_atualizacao
                ORDER BY
                    assistente ASC,
                    situacao ASC;
            """
            cursor.execute(query, (assistente,))
            acompanhamento = cursor.fetchall()

            # Formatar os dados para o front-end
            acompanhamento_formatado = {}
            for acomp in acompanhamento:
                assistente = acomp[0]
                if (assistente not in acompanhamento_formatado):
                    acompanhamento_formatado[assistente] = []

                acompanhamento_formatado[assistente].append({
                    'situacao': acomp[1],
                    'qtd_beneficiarios': acomp[2],
                    'soma_beneficiarios': acomp[3],
                    'ultima_atualizacao': acomp[4].strftime('%d/%m/%Y %H:%M:%S') if acomp[4] else None
                })

            return acompanhamento_formatado
    except Exception as e:
        logging.error(f"Erro ao consultar acompanhamento: {e}")
        return None


def consulta_acompanhamento_detalhada(assistente, situacao):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT porte, operadora, tipo_venda, cnpj_empresa, nome_razao_social, numero_proposta, qtde_benef_por_plano, valor_benef_por_plano, nome_corretor, mes_referencia, situacao
                FROM acompanhamento_propostas
                WHERE assistente_sisweb = %s AND situacao = %s
            """
            cursor.execute(query, (assistente, situacao))
            detalhes = cursor.fetchall()

            detalhes_formatado = [
                {
                    'porte': detalhe[0],
                    'operadora': detalhe[1],
                    'tipo_venda': detalhe[2],
                    'cnpj_empresa': detalhe[3],
                    'nome_razao_social': detalhe[4],
                    'numero_proposta': detalhe[5],
                    'qtde_beneficiarios': detalhe[6],
                    'valor_beneficiarios': f"R$ {detalhe[7]:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.') if (detalhe[7] is not None) else 'R$ 0,00',
                    'nome_corretor': detalhe[8],
                    'mes_referencia': datetime.strptime(detalhe[9], '%Y-%m-%d %H:%M:%S').strftime('%m/%Y') if detalhe[9] else None,
                    'situacao': detalhe[10]
                } for detalhe in detalhes
            ]

            return detalhes_formatado

    except Exception as e:
        logging.error(f"Erro ao buscar detalhes de acompanhamento: {e}")
        return None


def add_processo_seletivo(titulo, descricao, requisitos, data_inicio, data_fim):
    try:
        with instance_cursor() as cursor:
            query = """
                INSERT INTO seletivo(titulo, descricao, requisitos, data_inicio, data_fim)
                    VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(query, (titulo, descricao, requisitos, data_inicio, data_fim))
            cursor.connection.commit()
    except Exception as e:
        logging.error(f'Erro ao adicionar o processo seletivo: {e}')
        raise


def consulta_processo_seletivo():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, titulo, descricao, requisitos,
                       TO_CHAR(data_inicio, 'DD/MM/YYYY') as data_inicio,
                       TO_CHAR(data_fim, 'DD/MM/YYYY') as data_fim
                FROM public.seletivo;
            """
            cursor.execute(query, ())
            results = cursor.fetchall()
            return results
    except Exception as e:
        logging.error(f"Erro ao consultar os processos: {e}")
        return []


def consulta_processo_seletivo_detalhado(seletivo_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT titulo, descricao, requisitos,
                       TO_CHAR(data_inicio, 'DD/MM/YYYY') as data_inicio,
                       TO_CHAR(data_fim, 'DD/MM/YYYY') as data_fim
                FROM public.seletivo
                WHERE id = %s;
            """
            cursor.execute(query, (seletivo_id,))
            result = cursor.fetchone()

            if (result):
                return {
                    "titulo": result[0],
                    "descricao": result[1],
                    "requisitos": result[2],
                    "data_inicio": result[3],
                    "data_fim": result[4]
                }
            else:
                return None
    except Exception as e:
        logging.error(f"Erro ao consultar os processos: {e}")
        return None


def add_inscricao_seletivo(id_seletivo, id_user, nome, departamento_atual, cargo_atual, cnh, carro, formacao, formacao_detal, certificacao, motivo):
    try:
        with instance_cursor() as cursor:
            query = """
                INSERT INTO inscricao_seletivo(id_seletivo, id_user, nome, departamento_atual, cargo_atual, cnh, carro, formacao, formacao_detal, certificacao, motivo)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(query, (id_seletivo, id_user, nome, departamento_atual, cargo_atual,  cnh, carro, formacao, formacao_detal, certificacao, motivo))
            cursor.connection.commit()
    except Exception as e:
        logging.error(f'Erro ao adicionar o processo seletivo: {e}')
        raise


def consulta_processos():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, titulo, data_fim
                FROM seletivo;
            """
            cursor.execute(query, ())
            result = cursor.fetchall()

            processos = []
            for row in result:
                processos.append({
                    "id": row[0],
                    "titulo": row[1],
                    "dt_fim": row[2]
                })

            return processos
    except Exception as e:
        logging.error(f"Erro ao consultar os processos: {e}")
        return []


def consulta_candidaturas(id_seletivo):
    formacao_map = {
        "ensino_medio_incompleto": "Ensino médio incompleto",
        "ensino_medio_completo": "Ensino médio completo",
        "ensino_tecnico_incompleto": "Ensino técnico incompleto",
        "ensino_tecnico_completo": "Ensino técnico completo",
        "ensino_superior_incompleto": "Ensino superior incompleto",
        "ensino_superior_completo": "Ensino superior completo",
        "pos_graduacao": "Pós-Graduação",
        "mestrado": "Mestrado",
        "doutorado": "Doutorado"
    }

    try:
        with instance_cursor() as cursor:
            query = """
                SELECT id, nome, departamento_atual, cargo_atual, motivo, cnh, carro, formacao, formacao_detal, certificacao
                FROM inscricao_seletivo
                WHERE id_seletivo = %s;
            """
            cursor.execute(query, (id_seletivo,))
            result = cursor.fetchall()

            candidaturas = []
            for row in result:
                candidaturas.append({
                    "id": row[0],
                    "nome": row[1],
                    "departamento_atual": row[2],
                    "cargo_atual": row[3],
                    "motivo": row[4],
                    "cnh": "Sim" if (row[5]) else "Não",
                    "carro": "Sim" if (row[6]) else "Não",
                    "formacao": formacao_map.get(row[7], "Formação desconhecida"),
                    "formacao_detal": row[8],
                    "certificacao": row[9],
                })

            return candidaturas
    except Exception as e:
        logging.error(f"Erro ao consultar candidaturas: {e}")
        return []


def fetch_boletos():
    with instance_cursor() as cursor:
        try:
            query = """
                SELECT bc.id,
                    bc.codigo_proposta,
                    bc.vlboleto,
                    bc.corretor_nome,
                    bc.segurado,
                    bc.nome_operadora,
                    bc.supervisor_nome,
                    bc.sub_produto,
                    bc.comissionamento_grade_producao,
                    bc.comissionamento_grade_corretor,
                    bc.modalidade,
                    bc.codigo_barras,
                    bc.data_envio,
                    u.nome AS nome_usuario,
                    bc.boleto_upload,
                    bc.comprovante_upload,
                    bc.status
                FROM public.boletos_corretor bc
                JOIN public.users u ON bc.user_id::integer = u.id
                ORDER BY bc.data_envio DESC;
            """
            cursor.execute(query)
            boletos = cursor.fetchall()
            colunas = [desc[0] for desc in cursor.description]

            boletos_dict = []
            for boleto in boletos:
                boleto_dict = dict(zip(colunas, boleto))

                # Formatar a data_envio no formato dd/mm/aaaa - hh:mm:ss
                if (boleto_dict['data_envio']):
                    data_envio = boleto_dict['data_envio']
                    data_envio_ajustada = data_envio - timedelta(hours=3)

                    # Formatar a data no formato dd/mm/aaaa - hh:mm:ss
                    boleto_dict['data_envio'] = data_envio_ajustada.strftime('%d/%m/%Y - %H:%M:%S')

                boletos_dict.append(boleto_dict)

            return boletos_dict
        except Exception as e:
            logging.error(f"Erro ao buscar boletos: {e}")
            return []


def fetch_boletos_usuario(user_id):
    with instance_cursor() as cursor:
        try:
            query = """
                SELECT bc.id,
                    bc.codigo_proposta,
                    bc.vlboleto,
                    bc.corretor_nome,
                    bc.segurado,
                    bc.nome_operadora,
                    bc.supervisor_nome,
                    bc.sub_produto,
                    bc.comissionamento_grade_producao,
                    bc.comissionamento_grade_corretor,
                    bc.modalidade,
                    bc.codigo_barras,
                    bc.data_envio,
                    u.nome AS nome_usuario,
                    bc.boleto_upload,
                    bc.comprovante_upload,
                    bc.status
                FROM public.boletos_corretor bc
                JOIN public.users u ON bc.user_id = u.id
                WHERE user_id = %s
                ORDER BY bc.data_envio DESC;
            """
            cursor.execute(query, (user_id,))
            boletos = cursor.fetchall()
            colunas = [desc[0] for desc in cursor.description]

            boletos_dict = []
            for boleto in boletos:
                boleto_dict = dict(zip(colunas, boleto))

                # Formatar a data_envio no formato dd/mm/aaaa - hh:mm:ss
                if (boleto_dict['data_envio']):
                    data_envio = boleto_dict['data_envio']
                    data_envio_ajustada = data_envio - timedelta(hours=3)

                    # Formatar a data no formato dd/mm/aaaa - hh:mm:ss
                    boleto_dict['data_envio'] = data_envio_ajustada.strftime('%d/%m/%Y - %H:%M:%S')

                boletos_dict.append(boleto_dict)

            return boletos_dict
        except Exception as e:
            logging.error(f"Erro ao buscar boletos: {e}")
            return []


def upload_formlpaccount(nome, email, telefone, hascnpj, plano_atual, cidade, periodo_contato):
    try:
        with instance_cursor() as cursor:
            query = """
                INSERT INTO form_lpaccount (nome, email, telefone, hascnpj, plano_atual, cidade, periodo_contato)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(query, (nome, email, telefone, hascnpj, plano_atual, cidade, periodo_contato))
            cursor.connection.commit()
    except Exception as e:
        logging.error(f"Erro ao inserir dados no banco de dados: {e}")
        raise e


def consulta_formlpaccount():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT nome, email, telefone, hascnpj, plano_atual, cidade, periodo_contato
                FROM form_lpaccount
            """
            cursor.execute(query)
            result = cursor.fetchall()
            return result
    except Exception as e:
        logging.error(f"Erro ao consultar os dados: {e}")
        return []


def atualizar_comprovante_upload(codigo_proposta, comprovante_url):
    with instance_cursor() as cursor:
        try:
            query = """
                UPDATE boletos_corretor
                SET comprovante_upload = %s
                WHERE codigo_proposta = %s
            """
            cursor.execute(query, (comprovante_url, codigo_proposta))
            cursor.connection.commit()
            logging.info("Comprovante atualizado com sucesso no banco de dados.")

        except Exception as e:
            logging.error(f"Erro ao atualizar comprovante no banco de dados: {e}")
            raise


def atualizar_status_boleto_db(codigo_proposta, status):
    with instance_cursor() as cursor:
        try:
            query = """
                UPDATE boletos_corretor
                SET status = %s
                WHERE codigo_proposta = %s
            """
            cursor.execute(query, (status, codigo_proposta))
            cursor.connection.commit()
            logging.info("Status do boleto atualizado com sucesso no banco de dados.")
        except Exception as e:
            logging.error(f"Erro ao atualizar status no banco de dados: {e}")
            raise


def add_boleto_corretor(codigo_proposta, vlBoleto, corretor_nome, segurado, nome_operadora, supervisor_nome,
                        sub_produto, comissionamento_grade_producao, comissionamento_grade_corretor,
                        modalidade, codigo_barras, user_id, boleto_upload, status='Pendente'):
    with instance_cursor() as cursor:
        try:
            query = """
                INSERT INTO boletos_corretor (
                    codigo_proposta, vlBoleto, corretor_nome, segurado, nome_operadora, supervisor_nome,
                    sub_produto, comissionamento_grade_producao, comissionamento_grade_corretor,
                    modalidade, codigo_barras, data_envio, user_id, boleto_upload, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, %s, %s, %s)
            """
            cursor.execute(query, (codigo_proposta, vlBoleto, corretor_nome, segurado, nome_operadora, supervisor_nome,
                                   sub_produto, comissionamento_grade_producao, comissionamento_grade_corretor,
                                   modalidade, codigo_barras, user_id, boleto_upload, status))
            cursor.connection.commit()
            logging.info("Query de inserção executada com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao inserir dados: {e}")


def salvar_senha_boleto(codigo_proposta, senha):
    with instance_cursor() as cursor:
        query = """
            INSERT INTO boletos_senhas (codigo_proposta, senha)
            VALUES (%s, %s)
            ON CONFLICT (codigo_proposta) DO UPDATE SET senha = EXCLUDED.senha;
        """
        cursor.execute(query, (codigo_proposta, senha))
        cursor.connection.commit()


def obter_senha_boleto(codigo_proposta):
    with instance_cursor() as cursor:
        query = "SELECT senha FROM boletos_senhas WHERE codigo_proposta = %s"
        cursor.execute(query, (codigo_proposta,))
        result = cursor.fetchone()
        return result[0] if result else None


def add_fornecedor(nome, cnpj_cpf, endereco, telefone, email, categoria, site=None, observacoes=None):
    """
    Insere um fornecedor na tabela fornecedores.
    """
    with instance_cursor() as cursor:
        try:
            query = """
                INSERT INTO fornecedores (
                    nome, cnpj_cpf, endereco, telefone, email, categoria, site, observacoes, criado_em, atualizado_em
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            cursor.execute(query, (nome, cnpj_cpf, endereco, telefone, email, categoria, site, observacoes))
            cursor.connection.commit()
            logging.info("Fornecedor cadastrado com sucesso.")
        except psycopg2.IntegrityError as e:
            logging.error(f"Erro de integridade ao cadastrar fornecedor: {e}")
            raise ValueError("CPF/CNPJ já cadastrado.")
        except Exception as e:
            logging.error(f"Erro ao cadastrar fornecedor: {e}")
            raise


def get_all_fornecedores():
    """
    Retorna todos os fornecedores cadastrados.
    """
    with instance_cursor() as cursor:
        try:
            query = """
                SELECT
                    id, nome, cnpj_cpf, endereco, telefone, email, categoria,
                    COALESCE(site, '-') AS site,
                    COALESCE(observacoes, '-') AS observacoes
                FROM fornecedores
                ORDER BY id DESC
            """
            cursor.execute(query)
            fornecedores = cursor.fetchall()
            return [
                dict(zip([col[0] for col in cursor.description], row))
                for row in fornecedores
            ]
        except Exception as e:
            logging.error(f"Erro ao buscar fornecedores: {e}")
            return []


def get_fornecedor_id(fornecedor_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    id,
                    nome,
                    cnpj_cpf,
                    endereco,
                    telefone,
                    email,
                    categoria,
                    site,
                    observacoes
                FROM fornecedores
                WHERE id = %s
            """
            cursor.execute(query, (fornecedor_id,))
            row = cursor.fetchone()
            colnames = [desc[0] for desc in cursor.description]
            return dict(zip(colnames, row)) if row else None
    except Exception as e:
        logging.error(f"Erro ao buscar fornecedor: {e}")
        return None


def add_funcionario(nome, cpf, telefone, email):
    with instance_cursor() as cursor:
        try:
            query = """
                INSERT INTO funcionarios (
                    nome, cpf, telefone, email, criado_em, atualizado_em
                ) VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            cursor.execute(query, (nome, cpf, telefone, email))
            cursor.connection.commit()
            logging.info("Funcionário cadastrado com sucesso.")
        except psycopg2.IntegrityError as e:
            logging.error(f"Erro de integridade ao cadastrar funcionário: {e}")
        except Exception as e:
            logging.error(f"Erro ao cadastrar funcionário: {e}")
            raise


def get_all_funcionario():
    with instance_cursor() as cursor:
        try:
            query = """
                SELECT
                    id, nome, cpf, telefone, email
                FROM funcionarios
                ORDER BY id DESC
            """
            cursor.execute(query)
            funcionarios = cursor.fetchall()
            return [
                dict(zip([col[0] for col in cursor.description], row))
                for row in funcionarios
            ]
        except Exception as e:
            logging.error(f"Erro ao buscar funcionário: {e}")
            return []


def get_funcionario_id(funcionario_id):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    id,
                    nome,
                    cpf,
                    telefone,
                    email
                FROM funcionarios
                WHERE id = %s
            """
            cursor.execute(query, (funcionario_id,))
            row = cursor.fetchone()
            colnames = [desc[0] for desc in cursor.description]
            return dict(zip(colnames, row)) if row else None
    except Exception as e:
        logging.error(f"Erro ao buscar funcionário: {e}")
        return None



def add_dados_bancarios(nome_banco, numero_banco, agencia, digito_agencia, tipo_conta, conta_corrente, conta_poupanca, conta_salario, digito_conta, fornecedor_id):
    with instance_cursor() as cursor:
        try:
            query = """
                INSERT INTO contas_dadosbancarios (
                    nome_banco, numero_banco, agencia, digito_agencia, tipo_conta,
                    conta_corrente, conta_poupanca, conta_salario, digito_conta, fornecedor_id,
                    criado_em, atualizado_em
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            cursor.execute(query, (
                nome_banco, numero_banco, agencia, digito_agencia, tipo_conta,
                conta_corrente, conta_poupanca, conta_salario, digito_conta, fornecedor_id
            ))
            cursor.connection.commit()
            logging.info("Dados bancários cadastrados com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao cadastrar dados bancários: {e}")
            raise


def add_dados_funcionario(nome_banco, numero_banco, agencia, digito_agencia, tipo_conta, conta_corrente, conta_poupanca, conta_salario, digito_conta, funcionario_id):
    with instance_cursor() as cursor:
        try:
            query = """
                INSERT INTO contas_dadosbancarios (
                    nome_banco, numero_banco, agencia, digito_agencia, tipo_conta,
                    conta_corrente, conta_poupanca, conta_salario, digito_conta, funcionario_id,
                    criado_em, atualizado_em
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            cursor.execute(query, (
                nome_banco, numero_banco, agencia, digito_agencia, tipo_conta,
                conta_corrente, conta_poupanca, conta_salario, digito_conta, funcionario_id
            ))
            cursor.connection.commit()
            logging.info("Dados bancários cadastrados com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao cadastrar dados bancários: {e}")
            raise


def get_all_dados_bancarios():
    with instance_cursor() as cursor:
        try:
            query = """
                SELECT
                    cdb.id,
                    cdb.nome_banco AS banco,
                    cdb.numero_banco,
                    cdb.agencia,
                    cdb.digito_agencia,
                    COALESCE(cdb.conta_corrente, cdb.conta_poupanca, cdb.conta_salario) AS conta,
                    cdb.digito_conta,
                    cdb.tipo_conta AS tipo,
                    f.nome AS fornecedor,
                    fun.nome AS funcionario,
                    cdb.criado_em,
                    cdb.atualizado_em
                FROM contas_dadosbancarios cdb
                LEFT JOIN fornecedores f ON cdb.fornecedor_id = f.id
                LEFT JOIN funcionarios fun ON cdb.funcionario_id = fun.id
                ORDER BY cdb.id DESC
            """
            cursor.execute(query)
            dados_bancarios = cursor.fetchall()
            return [
                dict(zip([col[0] for col in cursor.description], row))
                for row in dados_bancarios
            ]
        except Exception as e:
            logging.error(f"Erro ao buscar dados bancários: {e}")
            return []


def get_dados_bancarios_id(dados_bancarios_id):
    with instance_cursor() as cursor:
        try:
            query = """
                SELECT
                    cdb.id,
                    cdb.nome_banco AS banco,
                    cdb.numero_banco,
                    cdb.agencia,
                    cdb.digito_agencia,
                    COALESCE(cdb.conta_corrente, cdb.conta_poupanca, cdb.conta_salario) AS conta,
                    cdb.digito_conta,
                    cdb.tipo_conta AS tipo
                FROM contas_dadosbancarios cdb
                LEFT JOIN fornecedores f ON cdb.fornecedor_id = f.id
                LEFT JOIN funcionarios fun ON cdb.funcionario_id = fun.id
                WHERE cdb.id = %s
            """
            cursor.execute(query, (dados_bancarios_id,))
            dados_bancarios = cursor.fetchall()
            return [
                dict(zip([col[0] for col in cursor.description], row))
                for row in dados_bancarios
            ]
        except Exception as e:
            logging.error(f"Erro ao buscar dados bancários: {e}")
            return []


def atualizar_dados_bancarios(dados_bancarios_id, nome_banco, numero_banco, agencia, digito_agencia, conta, digito_conta, tipo_conta):
    with instance_cursor() as cursor:
        try:
            # Normaliza o tipo de conta
            tipo_conta = tipo_conta.strip().lower()
            if tipo_conta == 'correntes':
                tipo_conta = 'corrente'

            # Define qual coluna será preenchida baseado no tipo de conta
            conta_corrente = conta if tipo_conta == 'corrente' else None
            conta_poupanca = conta if tipo_conta == 'poupança' else None
            conta_salario = conta if tipo_conta == 'salário' else None

            query = """
                UPDATE contas_dadosbancarios
                SET nome_banco = %s,
                    numero_banco = %s,
                    agencia = %s,
                    digito_agencia = %s,
                    conta_corrente = %s,
                    conta_poupanca = %s,
                    conta_salario = %s,
                    digito_conta = %s,
                    tipo_conta = %s,
                    atualizado_em = CURRENT_TIMESTAMP
                WHERE id = %s
            """
            cursor.execute(query, (
                nome_banco, numero_banco, agencia, digito_agencia,
                conta_corrente, conta_poupanca, conta_salario,
                digito_conta, tipo_conta, dados_bancarios_id
            ))
            cursor.connection.commit()
            logging.info("Dados bancários atualizados com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao atualizar dados bancários: {e}")
            raise


def delete_dados_bancarios(dados_bancarios_id):
    with instance_cursor() as cursor:
        try:
            query = """
                DELETE FROM contas_dadosbancarios
                WHERE id = %s
            """
            cursor.execute(query, (dados_bancarios_id,))
            cursor.connection.commit()
            logging.info("Dados bancários deletados com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao deletar dados bancários: {e}")
            raise


def add_empresa(nome_empresa, cnpj, endereco, telefone, email, observacoes):
    """
    Adiciona uma nova empresa ao banco de dados.
    """
    with instance_cursor() as cursor:
        try:
            query = """
                INSERT INTO empresas_grupo (
                    nome_empresa, cnpj, endereco, telefone, email, observacoes,
                    criado_em, atualizado_em
                ) VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            cursor.execute(query, (
                nome_empresa, cnpj, endereco, telefone, email, observacoes
            ))
            cursor.connection.commit()
            logging.info("Empresa cadastrada com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao cadastrar empresa: {e}")
            raise


def get_all_empresas():
    """
    Retorna todas as empresas cadastradas.
    """
    with instance_cursor() as cursor:
        try:
            query = """
                SELECT
                    id,
                    nome_empresa,
                    cnpj,
                    endereco,
                    telefone,
                    email,
                    observacoes,
                    criado_em,
                    atualizado_em
                FROM empresas_grupo
                ORDER BY id DESC
            """
            cursor.execute(query)
            empresas = cursor.fetchall()
            return [
                dict(zip([col[0] for col in cursor.description], row))
                for row in empresas
            ]
        except Exception as e:
            logging.error(f"Erro ao buscar empresas: {e}")
            return []


def get_empresa_by_id(empresa_id):
    with instance_cursor() as cursor:
        try:
            query = """
                SELECT * FROM empresas_grupo WHERE id = %s
            """
            cursor.execute(query, (empresa_id,))
            empresa = cursor.fetchone()
            return dict(zip([col[0] for col in cursor.description], empresa))
        except Exception as e:
            logging.error(f"Erro ao buscar empresa: {e}")
            return None


def atualizar_empresa(empresa_id, nome_empresa, cnpj, endereco, telefone, email, observacoes):
    with instance_cursor() as cursor:
        try:
            query = """
                UPDATE empresas_grupo
                SET nome_empresa = %s,
                    cnpj = %s,
                    endereco = %s,
                    telefone = %s,
                    email = %s,
                    observacoes = %s,
                    atualizado_em = CURRENT_TIMESTAMP
                WHERE id = %s
            """
            cursor.execute(query, (nome_empresa, cnpj, endereco, telefone, email, observacoes, empresa_id))
            cursor.connection.commit()
            logging.info("Empresa atualizada com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao atualizar empresa: {e}")
            raise


def add_conta(
    nome_conta,
    empresa_pagadora_id,
    fornecedor_id,
    valor,
    data_vencimento,
    codigo_barras,
    recorrencia,
    observacoes,
    boleto_filename,
    quantidade_recorrencia,
    tipo_conta
):
    with instance_cursor() as cursor:
        # 1) Insere os dados da conta (sem o valor)
        query_conta = """
            INSERT INTO contas (
                nome_conta,
                empresa_pagadora_id,
                fornecedor_id,
                observacoes,
                boleto_filename,
                tipo_conta,
                criado_em,
                atualizado_em
            )
            VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
        """
        cursor.execute(query_conta, (
            nome_conta,
            empresa_pagadora_id,
            fornecedor_id,
            observacoes,
            boleto_filename,
            tipo_conta
        ))
        new_conta_id = cursor.fetchone()[0]

        # 2) Insere as parcelas na tabela 'recorrencias'
        query_rec = """
            INSERT INTO recorrencias (
                conta_id,
                valor,
                data_vencimento,
                codigo_barras,
                tipo_recorrencia,
                quantidade_parcelas,
                boleto_filename,
                criado_em,
                atualizado_em
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """
        for i in range(quantidade_recorrencia):
            if recorrencia == 'Semanal':
                data_vencimento_atual = data_vencimento + relativedelta(weeks=i)
            elif recorrencia == 'Mensal':
                data_vencimento_atual = data_vencimento + relativedelta(months=i)
            elif recorrencia == 'Anual':
                data_vencimento_atual = data_vencimento + relativedelta(months=12 * i)
            else:
                # Para recorrência "Única" ou desconhecida, mantém a data original
                data_vencimento_atual = data_vencimento

            # Só a primeira recorrência recebe código de barras e boleto
            if i == 0:
                codigo_barras_atual = codigo_barras
                boleto_filename_atual = boleto_filename
            else:
                codigo_barras_atual = None
                boleto_filename_atual = None

            cursor.execute(query_rec, (
                new_conta_id,
                valor,
                data_vencimento_atual,
                codigo_barras_atual,
                recorrencia,
                quantidade_recorrencia,
                boleto_filename_atual
            ))

        cursor.connection.commit()


def add_conta_funcionario(
    nome_conta,
    empresa_pagadora_id,
    funcionario_id,
    valor,
    data_vencimento,
    codigo_barras,
    recorrencia,
    observacoes,
    boleto_filename,
    quantidade_recorrencia,
    tipo_conta
):
    with instance_cursor() as cursor:
        # 1) Insere os dados da conta (sem o valor)
        query_conta = """
            INSERT INTO contas (
                nome_conta,
                empresa_pagadora_id,
                funcionario_id,
                observacoes,
                boleto_filename,
                tipo_conta,
                criado_em,
                atualizado_em
            )
            VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
        """
        cursor.execute(query_conta, (
            nome_conta,
            empresa_pagadora_id,
            funcionario_id,
            observacoes,
            boleto_filename,
            tipo_conta
        ))
        new_conta_id = cursor.fetchone()[0]

        # 2) Insere as parcelas na tabela 'recorrencias'
        query_rec = """
            INSERT INTO recorrencias (
                conta_id,
                valor,
                data_vencimento,
                codigo_barras,
                tipo_recorrencia,
                quantidade_parcelas,
                boleto_filename,
                criado_em,
                atualizado_em
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """
        for i in range(quantidade_recorrencia):
            if recorrencia == 'Semanal':
                data_vencimento_atual = data_vencimento + relativedelta(weeks=i)
            elif recorrencia == 'Mensal':
                data_vencimento_atual = data_vencimento + relativedelta(months=i)
            elif recorrencia == 'Anual':
                data_vencimento_atual = data_vencimento + relativedelta(months=12 * i)
            else:
                # Para recorrência "Única" ou desconhecida, mantém a data original
                data_vencimento_atual = data_vencimento

            # Só a primeira recorrência recebe código de barras e boleto
            if i == 0:
                codigo_barras_atual = codigo_barras
                boleto_filename_atual = boleto_filename
            else:
                codigo_barras_atual = None
                boleto_filename_atual = None

            cursor.execute(query_rec, (
                new_conta_id,
                valor,
                data_vencimento_atual,
                codigo_barras_atual,
                recorrencia,
                quantidade_recorrencia,
                boleto_filename_atual
            ))

        cursor.connection.commit()


def get_all_contas_calendario():
    with instance_cursor() as cursor:
        query = """
            SELECT
                c.id AS id,
                c.nome_conta,
                e.nome_empresa AS empresa_pagadora,
                f.nome        AS fornecedor,
                r.valor,
                r.data_vencimento,
                r.codigo_barras,
                r.tipo_recorrencia AS recorrencia,
                c.tipo_conta,
                c.observacoes,
                r.boleto_filename
            FROM contas c
            LEFT JOIN recorrencias r   ON r.conta_id = c.id
            LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
            LEFT JOIN fornecedores f   ON f.id = c.fornecedor_id
            ORDER BY r.data_vencimento ASC
        """
        cursor.execute(query)
        rows = cursor.fetchall()

        colnames = [desc[0] for desc in cursor.description]
        lista = []
        for row in rows:
            conta = dict(zip(colnames, row))
            # Formata a data de vencimento
            if conta['data_vencimento']:
                conta['data_vencimento'] = conta['data_vencimento'].strftime('%d/%m/%Y')
            lista.append(conta)
        return lista


def get_all_contas_calendario_luanca():
    with instance_cursor() as cursor:
        query = """
            SELECT
                c.id AS id,
                c.nome_conta,
                e.nome_empresa AS empresa_pagadora,
                f.nome AS fornecedor,
                r.valor,
                r.data_vencimento,
                r.codigo_barras,
                r.tipo_recorrencia AS recorrencia,
                c.tipo_conta,
                c.observacoes,
                r.boleto_filename
            FROM contas c
            LEFT JOIN recorrencias r   ON r.conta_id = c.id
            LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
            LEFT JOIN fornecedores f   ON f.id = c.fornecedor_id
            WHERE e.nome_empresa = 'LUANCA BRH CONSULTORIA E BENEFICIOS E CORRETORA DE SEGUROS LTDA'
            ORDER BY r.data_vencimento ASC;
        """
        cursor.execute(query)
        rows = cursor.fetchall()

        colnames = [desc[0] for desc in cursor.description]
        lista = []
        for row in rows:
            conta = dict(zip(colnames, row))
            # Formata a data de vencimento
            if conta['data_vencimento']:
                conta['data_vencimento'] = conta['data_vencimento'].strftime('%d/%m/%Y')
            lista.append(conta)
        return lista


def get_contas_por_mes(start, end):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    c.id AS id,
                    c.nome_conta,
                    e.nome_empresa AS empresa_pagadora,
                    f.nome AS fornecedor,
                    fun.nome AS funcionario,
                    r.id AS recorrencia_id,
                    r.valor,
                    r.data_vencimento,
                    r.codigo_barras,
                    r.tipo_recorrencia AS recorrencia,
                    c.tipo_conta,
                    c.observacoes,
                    r.data_pagamento,
                    r.boleto_filename,
                    r.comprovante_filename,
                    db.nome_banco,
                    db.numero_banco,
                    db.agencia,
                    db.digito_agencia,
                    db.tipo_conta AS tipo_conta_bancaria,
                    db.conta_corrente,
                    db.digito_conta,
                    f.cnpj_cpf
                FROM contas c
                LEFT JOIN recorrencias r ON r.conta_id = c.id
                LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
                LEFT JOIN fornecedores f ON f.id = c.fornecedor_id
                LEFT JOIN funcionarios fun ON c.funcionario_id = fun.id
                LEFT JOIN contas_dadosbancarios db ON db.fornecedor_id = f.id
                WHERE r.data_vencimento BETWEEN %s AND %s
                ORDER BY c.id DESC
            """
            cursor.execute(query, (start, end))
            rows = cursor.fetchall()

            colnames = [desc[0] for desc in cursor.description]
            lista = []
            for row in rows:
                conta = dict(zip(colnames, row))
                if conta['data_vencimento']:
                    conta['data_vencimento'] = conta['data_vencimento'].strftime('%d/%m/%Y')
                lista.append(conta)
            return lista
    except Exception as e:
        logging.error(f"Erro ao buscar contas por mês: {e}")
        return []


def get_luanca_contas(start, end):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    c.id AS id,
                    c.nome_conta,
                    e.nome_empresa AS empresa_pagadora,
                    f.nome AS fornecedor,
                    fun.nome AS funcionario,
                    r.id AS recorrencia_id,
                    r.valor,
                    r.data_vencimento,
                    r.codigo_barras,
                    r.tipo_recorrencia AS recorrencia,
                    c.tipo_conta,
                    c.observacoes,
                    r.data_pagamento,
                    r.boleto_filename,
                    r.comprovante_filename,
                    db.nome_banco,
                    db.numero_banco,
                    db.agencia,
                    db.digito_agencia,
                    db.tipo_conta AS tipo_conta_bancaria,
                    db.conta_corrente,
                    db.digito_conta,
                    f.cnpj_cpf
                FROM contas c
                LEFT JOIN recorrencias r ON r.conta_id = c.id
                LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
                LEFT JOIN fornecedores f ON f.id = c.fornecedor_id
                LEFT JOIN funcionarios fun ON c.funcionario_id = fun.id
                LEFT JOIN contas_dadosbancarios db ON db.fornecedor_id = f.id
                WHERE r.data_vencimento BETWEEN %s AND %s
                AND e.nome_empresa IN ('LUANCA BRH CONSULTORIA E BENEFICIOS E CORRETORA DE SEGUROS LTDA',
                                    'AMERICAN BROKER ASSESSORIA E CORRETORA DE SEGS LTDA',
                                    'CONFIANCE BRH ASSESSORIA E CORRETORA DE SEGUROS LTDA',
                                    'MAHALO HEALTH PLANOS DE SAUDE LTDA')
                ORDER BY c.id DESC;
            """
            cursor.execute(query, (start, end))
            rows = cursor.fetchall()

            colnames = [desc[0] for desc in cursor.description]
            lista = []
            for row in rows:
                conta = dict(zip(colnames, row))
                # Formata a data de vencimento
                if conta['data_vencimento']:
                    conta['data_vencimento'] = conta['data_vencimento'].strftime('%d/%m/%Y')
                if conta['data_pagamento'] and conta['data_pagamento'] is not None:
                    conta['data_pagamento'] = conta['data_pagamento'].strftime('%d/%m/%Y')
                lista.append(conta)
            return lista
    except Exception as e:
        logging.error(f"Erro ao buscar contas Luanca por mês: {e}")
        return []


def get_pagamentos_por_mes(start, end):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    c.id AS id,
                    c.nome_conta,
                    e.nome_empresa AS empresa_pagadora,
                    f.nome        AS fornecedor,
                    fun.nome AS funcionario,
                    r.id AS recorrencia_id,
                    r.valor,
                    r.data_vencimento,
                    r.data_pagamento,
                    r.comprovante_filename
                FROM contas c
                LEFT JOIN recorrencias r   ON r.conta_id = c.id
                LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
                LEFT JOIN fornecedores f   ON f.id = c.fornecedor_id
                LEFT JOIN funcionarios fun ON c.funcionario_id = fun.id
                WHERE r.data_vencimento BETWEEN %s AND %s
                ORDER BY c.id DESC
            """
            cursor.execute(query, (start, end))
            rows = cursor.fetchall()

            colnames = [desc[0] for desc in cursor.description]
            lista = []
            for row in rows:
                conta = dict(zip(colnames, row))
                if conta['data_vencimento']:
                    conta['data_vencimento'] = conta['data_vencimento'].strftime('%d/%m/%Y')
                if conta['data_pagamento']:
                    conta['data_pagamento'] = conta['data_pagamento'].strftime('%d/%m/%Y')
                lista.append(conta)
            return lista
    except Exception as e:
        logging.error(f"Erro ao buscar pagamentos por mês: {e}")
        return []


def get_pagamentos_luanca(start, end):
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT
                    c.id AS id,
                    c.nome_conta,
                    e.nome_empresa AS empresa_pagadora,
                    f.nome        AS fornecedor,
                    fun.nome AS funcionario,
                    r.id AS recorrencia_id,
                    r.valor,
                    r.data_vencimento,
                    r.data_pagamento,
                    r.comprovante_filename
                FROM contas c
                LEFT JOIN recorrencias r   ON r.conta_id = c.id
                LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
                LEFT JOIN fornecedores f   ON f.id = c.fornecedor_id
                LEFT JOIN funcionarios fun ON c.funcionario_id = fun.id
                WHERE r.data_vencimento BETWEEN %s AND %s
                AND e.nome_empresa = 'LUANCA BRH CONSULTORIA E BENEFICIOS E CORRETORA DE SEGUROS LTDA'
                ORDER BY c.id DESC
            """
            cursor.execute(query, (start, end))
            rows = cursor.fetchall()

            colnames = [desc[0] for desc in cursor.description]
            lista = []
            for row in rows:
                conta = dict(zip(colnames, row))
                if conta['data_vencimento']:
                    conta['data_vencimento'] = conta['data_vencimento'].strftime('%d/%m/%Y')
                if conta['data_pagamento']:
                    conta['data_pagamento'] = conta['data_pagamento'].strftime('%d/%m/%Y')
                lista.append(conta)
            return lista
    except Exception as e:
        logging.error(f"Erro ao buscar pagamentos por mês: {e}")
        return []


def delete_conta(conta_id):
    try:
        with instance_cursor() as cursor:
            query = "DELETE FROM contas WHERE id = %s;"
            cursor.execute(query, (conta_id,))
            cursor.connection.commit()
    except Exception as e:
        logging.error(f"Erro ao deletar conta do banco de dados: {e}")
        raise e


def update_contas(valor, data_vencimento, codigo_barras, observacoes, tipo_conta, conta_id, recorrencia_id, boleto_filename=None):
    try:
        with instance_cursor() as cursor:
            # Atualiza dados na tabela "contas"
            cursor.execute("""
                SELECT observacoes
                FROM contas
                WHERE id = %s
            """, (conta_id,))
            row = cursor.fetchone()
            if not row:
                raise ValueError("Conta não encontrada")
            observacoes_atual = row[0]

            if observacoes is None:
                observacoes = observacoes_atual

            cursor.execute("""
                UPDATE contas
                SET observacoes = %s, tipo_conta = %s, atualizado_em = NOW()
                WHERE id = %s
            """, (observacoes, tipo_conta, conta_id))

            # Atualiza dados na tabela "recorrencias" para a recorrência específica
            if recorrencia_id:
                cursor.execute("""
                    SELECT valor, data_vencimento, codigo_barras, boleto_filename
                    FROM recorrencias
                    WHERE id = %s AND conta_id = %s
                    LIMIT 1
                """, (recorrencia_id, conta_id))
                rec = cursor.fetchone()
                if not rec:
                    logging.warning("Recorrência não encontrada para a conta. Nenhuma atualização de recorrência realizada.")
                else:
                    valor_atual, data_atual, codigo_barras_atual, boleto_filename_atual = rec

                    if valor is None:
                        valor = valor_atual
                    if data_vencimento is None:
                        data_vencimento = data_atual
                    if codigo_barras is None:
                        codigo_barras = codigo_barras_atual
                    if boleto_filename is None:
                        boleto_filename = boleto_filename_atual

                    cursor.execute("""
                        UPDATE recorrencias
                        SET valor = %s,
                            data_vencimento = %s,
                            codigo_barras = %s,
                            boleto_filename = %s,
                            atualizado_em = NOW()
                        WHERE id = %s AND conta_id = %s
                    """, (valor, data_vencimento, codigo_barras, boleto_filename, recorrencia_id, conta_id))

            cursor.connection.commit()

    except Exception as e:
        logging.error(f"Erro ao atualizar conta no banco de dados: {e}")
        raise e


def get_conta_by_id(conta_id, recorrencia_id):
    with instance_cursor() as cursor:
        query = """
            SELECT
                c.id AS id,
                c.nome_conta,
                e.nome_empresa AS empresa_pagadora,
                f.nome AS fornecedor,
                r.id AS recorrencia_id,
                r.valor,
                r.data_vencimento,
                r.codigo_barras,
                r.tipo_recorrencia AS recorrencia,
                c.tipo_conta,
                c.observacoes,
                r.data_pagamento,
                r.boleto_filename,
                r.comprovante_filename,
                db.nome_banco,
                db.numero_banco,
                db.agencia,
                db.digito_agencia,
                db.tipo_conta AS tipo_conta_bancaria,
                db.conta_corrente,
                db.digito_conta,
                f.cnpj_cpf
            FROM contas c
            LEFT JOIN recorrencias r ON r.conta_id = c.id
            LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
            LEFT JOIN fornecedores f ON f.id = c.fornecedor_id
            LEFT JOIN contas_dadosbancarios db ON db.fornecedor_id = f.id
            WHERE c.id = %s AND r.id = %s
        """
        cursor.execute(query, (conta_id, recorrencia_id))
        row = cursor.fetchone()  # Como estamos buscando uma linha específica
        if not row:
            return None

        colnames = [desc[0] for desc in cursor.description]
        conta_dict = dict(zip(colnames, row))
        if conta_dict.get('data_vencimento'):
            conta_dict['data_vencimento'] = conta_dict['data_vencimento'].strftime('%d/%m/%Y')
        if conta_dict['data_pagamento']:
                conta_dict['data_pagamento'] = conta_dict['data_pagamento'].strftime('%d/%m/%Y')
        return conta_dict


def consulta_contas_recorrentes():
    with instance_cursor() as cursor:
        query = """
            SELECT
                c.id,
                c.nome_conta,
                c.tipo_conta,
                e.nome_empresa AS empresa_pagadora,
                f.nome AS fornecedor,
                r.valor,
                c.criado_em,
                MIN(r.data_vencimento) AS data_vencimento,
                COUNT(r.conta_id) AS total_recorrencias
            FROM contas c
            JOIN recorrencias r ON c.id = r.conta_id
            JOIN empresas_grupo e ON c.empresa_pagadora_id = e.id
            JOIN fornecedores f ON c.fornecedor_id = f.id
            GROUP BY c.id, c.nome_conta, e.nome_empresa, f.nome, r.valor, c.criado_em
            HAVING COUNT(r.conta_id) > 1
        """
        cursor.execute(query)
        columns = [desc[0] for desc in cursor.description]
        rows = cursor.fetchall()
        resultado = []
        for row in rows:
            resultado.append(dict(zip(columns, row)))
        return resultado


def get_contas_recorrentes_luanca():
    with instance_cursor() as cursor:
        query = """
            SELECT
                c.id,
                c.nome_conta,
                c.tipo_conta,
                e.nome_empresa AS empresa_pagadora,
                f.nome AS fornecedor,
                r.valor,
                c.criado_em,
                MIN(r.data_vencimento) AS data_vencimento,
                COUNT(r.conta_id) AS total_recorrencias
            FROM contas c
            JOIN recorrencias r ON c.id = r.conta_id
            JOIN empresas_grupo e ON c.empresa_pagadora_id = e.id
            JOIN fornecedores f ON c.fornecedor_id = f.id
            WHERE e.nome_empresa = 'LUANCA BRH CONSULTORIA E BENEFICIOS E CORRETORA DE SEGUROS LTDA'
            GROUP BY c.id, c.nome_conta, e.nome_empresa, f.nome, r.valor, c.criado_em
            HAVING COUNT(r.conta_id) > 1
        """
        cursor.execute(query)
        columns = [desc[0] for desc in cursor.description]
        rows = cursor.fetchall()
        resultado = []
        for row in rows:
            resultado.append(dict(zip(columns, row)))
        return resultado


def consulta_contas_recorrentes_id(recorrente_id):
    with instance_cursor() as cursor:
        # 1) Pega a conta + recorrencia principal (equivalente a 'query1')
        query1 = """
            SELECT
                c.id,
                c.nome_conta,
                c.tipo_conta,
                e.nome_empresa AS empresa_pagadora,
                f.nome         AS fornecedor,
                r.valor,
                c.criado_em,
                r.data_vencimento,
                r.tipo_recorrencia AS recorrencia
            FROM contas c
            LEFT JOIN recorrencias r ON r.conta_id = c.id
            LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
            LEFT JOIN fornecedores f   ON f.id = c.fornecedor_id
            WHERE c.id = %s
        """
        cursor.execute(query1, (recorrente_id,))
        rows1 = cursor.fetchall()
        colnames1 = [desc[0] for desc in cursor.description]

        query2 = """
            SELECT
                c.id,
                c.nome_conta,
                c.tipo_conta,
                e.nome_empresa AS empresa_pagadora,
                f.nome AS fornecedor,
                r.valor,
                c.criado_em,
                r.data_vencimento,
                r.tipo_recorrencia AS recorrencia,
                ROW_NUMBER() OVER (PARTITION BY r.conta_id ORDER BY c.criado_em ASC) AS numero_da_parcela,
                COUNT(*) OVER (PARTITION BY r.conta_id) AS total_de_parcelas
            FROM contas c
            LEFT JOIN recorrencias r ON r.conta_id = c.id
            LEFT JOIN empresas_grupo e ON e.id = c.empresa_pagadora_id
            LEFT JOIN fornecedores f ON f.id = c.fornecedor_id
            WHERE r.conta_id = (
                SELECT r2.conta_id
                FROM recorrencias r2
                WHERE r2.conta_id = %s
                LIMIT 1
            )
            ORDER BY c.id ASC
        """
        cursor.execute(query2, (recorrente_id,))
        rows2 = cursor.fetchall()
        colnames2 = [desc[0] for desc in cursor.description]

        def format_results(rows, colnames):
            result_list = []
            for row in rows:
                conta = dict(zip(colnames, row))
                for date_field in ['data_vencimento', 'criado_em']:
                    if conta.get(date_field) and isinstance(conta[date_field], datetime):
                        conta[date_field] = conta[date_field].strftime('%d/%m/%Y')
                if 'valor' in conta and conta['valor'] is not None:
                    conta['valor'] = float(conta['valor'])
                result_list.append(conta)
            return result_list

        return {
            "conta": format_results(rows1, colnames1),
            "parcelas": format_results(rows2, colnames2)
        }


def atualizar_pagamento(recorrencia_id, data_pagamento, comprovante_filename):
    with instance_cursor() as cursor:
        try:
            query = """
                UPDATE recorrencias
                SET data_pagamento = %s, comprovante_filename = %s, atualizado_em = NOW()
                WHERE id = %s
            """
            cursor.execute(query, (data_pagamento, comprovante_filename, recorrencia_id))
            cursor.connection.commit()
        except Exception as e:
            logging.error(f"Erro ao atualizar pagamento no banco de dados: {e}")
            raise e


# Função para fazer o update de fornecedores
def atualizar_fornecedor(fornecedor_id, nome, cnpj_cpf, endereco, telefone, email, categoria, site, observacoes):
    with instance_cursor() as cursor:
        try:
            query = """
                UPDATE fornecedores
                SET nome = %s, cnpj_cpf = %s, endereco = %s, telefone = %s, email = %s, categoria = %s, site = %s, observacoes = %s, criado_em = NOW(), atualizado_em = NOW()
                WHERE id = %s
            """
            cursor.execute(query, (nome, cnpj_cpf, endereco, telefone, email, categoria, site, observacoes, fornecedor_id))
            cursor.connection.commit()
            logging.info("Fornecedor atualizado com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao atualizar fornecedor: {e}")
            raise e

# Função para fazer o update de funcionários
def atualizar_funcionario(funcionario_id, nome, cpf, telefone, email):
    with instance_cursor() as cursor:
        try:
            query = """
                UPDATE funcionarios
                SET nome = %s, cpf = %s, telefone = %s, email = %s, atualizado_em = CURRENT_TIMESTAMP
                WHERE id = %s
            """
            cursor.execute(query, (nome, cpf, telefone, email, funcionario_id))
            cursor.connection.commit()
            logging.info("Funcionário atualizado com sucesso.")
        except Exception as e:
            logging.error(f"Erro ao atualizar funcionário: {e}")
            raise e


def add_projeto(nome_projeto, descricao, justificativa, responsavel,
                prioridade, complexidade, data_inicio, data_entrega, status):
    with instance_cursor() as cursor:
        query = """
            INSERT INTO projetos (
                nome_projeto, descricao, justificativa, responsavel,
                prioridade, complexidade, data_inicio, data_entrega,
                status, data_criacao, data_atualizacao
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """
        cursor.execute(query, (
            nome_projeto, descricao, justificativa, responsavel,
            prioridade, complexidade, data_inicio, data_entrega, status
        ))
        cursor.connection.commit()


def get_all_projetos():
    with instance_cursor() as cursor:
        cursor.execute("""
            SELECT
                p.id,
                p.nome_projeto,
                p.descricao,
                p.justificativa,
                COALESCE(u.nome, 'Sem responsável') AS responsavel,
                COALESCE(u.cargo, 'Sem cargo') AS cargo,
                p.prioridade,
                p.complexidade,
                COALESCE(TO_CHAR(p.data_inicio, 'DD/MM/YYYY'), '-') AS data_inicio,
                COALESCE(TO_CHAR(p.data_entrega, 'DD/MM/YYYY'), '-') AS data_entrega,
                p.status,
                TO_CHAR(p.data_criacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_criacao,
                TO_CHAR(p.data_atualizacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_atualizacao
            FROM projetos p
            LEFT JOIN users u ON p.responsavel = u.id
            ORDER BY p.data_criacao DESC;
        """)
        return cursor.fetchall()


def get_projeto_by_id(projeto_id):
    with instance_cursor() as cursor:
        cursor.execute("""
            SELECT
                p.id,
                p.nome_projeto,
                p.descricao,
                p.justificativa,
                p.responsavel,
                p.prioridade,
                p.complexidade,
                COALESCE(TO_CHAR(p.data_inicio, 'DD/MM/YYYY'), '-') AS data_inicio,
                COALESCE(TO_CHAR(p.data_entrega, 'DD/MM/YYYY'), '-') AS data_entrega,
                p.status,
                TO_CHAR(p.data_criacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_criacao,
                TO_CHAR(p.data_atualizacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_atualizacao
            FROM projetos p
            WHERE p.id = %s;
        """, (projeto_id,))
        return cursor.fetchone()


def add_team_member_to_project(projeto_id, usuario_id, cargo=None):
    if cargo is None:
        cargo = "Membro da Equipe"
    with instance_cursor() as cursor:
        query = """
            INSERT INTO equipes_projeto (
                projeto_id, usuario_id, cargo, data_adicao
            )
            VALUES (%s, %s, %s, NOW())
        """
        cursor.execute(query, (projeto_id, usuario_id, cargo))
        cursor.connection.commit()


def get_team_by_project_id_dict(projeto_id):
    with instance_cursor() as cursor:
        query = """
            SELECT DISTINCT ON (e.usuario_id)
                e.id,
                e.projeto_id,
                e.usuario_id,
                e.cargo,
                TO_CHAR(e.data_adicao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_adicao,
                u.nome AS usuario_nome
            FROM equipes_projeto e
            LEFT JOIN users u ON e.usuario_id = u.id
            WHERE e.projeto_id = %s
            ORDER BY e.usuario_id, e.data_adicao;
        """
        cursor.execute(query, (projeto_id,))
        rows = cursor.fetchall()
        membros = []
        for row in rows:
            membros.append({
                "id": row[0],
                "projeto_id": row[1],
                "usuario_id": row[2],
                "cargo": row[3],
                "data_adicao": row[4],
                "usuario_nome": row[5]
            })
        return membros


def get_tarefas_by_projeto(projeto_id):
    with instance_cursor() as cursor:
        query = """
            SELECT
                t.id,
                t.id_projeto,
                t.id_criador,
                t.titulo,
                t.descricao,
                COALESCE(TO_CHAR(t.data_inicio, 'DD/MM/YYYY'), '-') AS data_inicio,
                COALESCE(TO_CHAR(t.data_fim, 'DD/MM/YYYY'), '-') AS data_fim,
                t.status,
                TO_CHAR(t.data_criacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_criacao,
                TO_CHAR(t.data_atualizacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_atualizacao,
                t.esta_em_tempo_tracking,
                COALESCE(TO_CHAR(t.data_inicio_tracking AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI'), '-') AS data_inicio_tracking,
                t.tempo_gasto,
                t.prioridade,
                t.complexidade,
                COALESCE(string_agg(u.nome, ', '), 'Sem responsável') AS responsaveis
            FROM tarefas t
            LEFT JOIN tarefas_equipes te ON t.id = te.id_tarefa
            LEFT JOIN users u ON te.id_usuario = u.id
            WHERE t.id_projeto = %s
            GROUP BY
                t.id, t.id_projeto, t.id_criador, t.titulo, t.descricao, t.data_inicio, t.data_fim,
                t.status, t.data_criacao, t.data_atualizacao, t.esta_em_tempo_tracking, t.data_inicio_tracking,
                t.tempo_gasto, t.prioridade, t.complexidade
            ORDER BY t.data_criacao DESC;
        """
        cursor.execute(query, (projeto_id,))
        return cursor.fetchall()


def add_tarefa(projeto_id, titulo, descricao, id_criador, status, data_inicio, data_fim, prioridade=None, complexidade=None):
    with instance_cursor() as cursor:
        query = """
            INSERT INTO tarefas (
                id_projeto, titulo, descricao, id_criador, status,
                data_inicio, data_fim, prioridade, complexidade, data_criacao, data_atualizacao
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            RETURNING id;
        """
        cursor.execute(query, (
            projeto_id, titulo, descricao, id_criador, status,
            data_inicio, data_fim, prioridade, complexidade
        ))
        tarefa_id = cursor.fetchone()[0]
        cursor.connection.commit()
        return tarefa_id


def update_tarefa(tarefa_id, titulo, descricao, data_fim, prioridade=None, complexidade=None):
    with instance_cursor() as cursor:
        query = """
            UPDATE tarefas
            SET titulo = %s,
                descricao = %s,
                data_fim = %s,
                prioridade = %s,
                complexidade = %s,
                data_atualizacao = NOW()
            WHERE id = %s;
        """
        cursor.execute(query, (titulo, descricao, data_fim, prioridade, complexidade, tarefa_id))
        cursor.connection.commit()


def update_projeto(projeto_id, titulo, descricao, data_entrega, status):
    with instance_cursor() as cursor:
        query = """
            UPDATE projetos
            SET nome_projeto = %s,
                descricao = %s,
                data_entrega = %s,
                status = %s,
                data_atualizacao = NOW()
            WHERE id = %s;
        """
        cursor.execute(query, (titulo, descricao, data_entrega, status, projeto_id))
        cursor.connection.commit()


def update_tarefa_tracking(tarefa_id, tracking):
    with instance_cursor() as cursor:
        if tracking:
            query = """
                UPDATE tarefas
                SET esta_em_tempo_tracking = true,
                    data_inicio_tracking = NOW(),
                    status = 'em_andamento',
                    data_atualizacao = NOW()
                WHERE id = %s;
            """
        else:
            query = """
                UPDATE tarefas
                SET esta_em_tempo_tracking = false,
                    tempo_gasto = NOW() - data_inicio_tracking,
                    status = 'concluido',
                    data_atualizacao = NOW()
                WHERE id = %s;
            """
        cursor.execute(query, (tarefa_id,))
        cursor.connection.commit()


def add_membro_a_tarefa(id_tarefa, id_usuario):
    with instance_cursor() as cursor:
        query = """
            INSERT INTO tarefas_equipes (id_tarefa, id_usuario, papel, data_atribuicao)
            VALUES (%s, %s, %s, NOW());
        """
        cursor.execute(query, (id_tarefa, id_usuario, 'Membro'))
        cursor.connection.commit()


def get_user_tasks(user_id):
    with instance_cursor() as cursor:
        query = """
            SELECT
                t.id,
                t.id_projeto,
                t.id_criador,
                t.titulo,
                t.descricao,
                COALESCE(TO_CHAR(t.data_inicio, 'DD/MM/YYYY'), '-') AS data_inicio,
                COALESCE(TO_CHAR(t.data_fim, 'DD/MM/YYYY'), '-') AS data_fim,
                t.status,
                TO_CHAR(t.data_criacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_criacao,
                TO_CHAR(t.data_atualizacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_atualizacao,
                t.esta_em_tempo_tracking,
                COALESCE(TO_CHAR(t.data_inicio_tracking AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI'), '-') AS data_inicio_tracking,
                t.tempo_gasto,
                COALESCE(string_agg(u.nome, ', '), 'Sem responsável') AS responsaveis,
                COALESCE(p.complexidade, t.complexidade, 'Não definido') AS complexidade,
                COALESCE(p.prioridade, t.prioridade, 'Não definido') AS prioridade,
                (CASE COALESCE(p.prioridade, t.prioridade, 'Não definido')
                    WHEN 'Alta' THEN 3
                    WHEN 'Média' THEN 2
                    WHEN 'Media' THEN 2
                    WHEN 'Baixa' THEN 1
                    ELSE 0
                 END *
                 CASE COALESCE(p.complexidade, t.complexidade, 'Não definido')
                    WHEN 'Baixa' THEN 3
                    WHEN 'Média' THEN 2
                    WHEN 'Alta' THEN 1
                    ELSE 0
                 END) AS score
            FROM tarefas t
            LEFT JOIN projetos p ON t.id_projeto = p.id
            LEFT JOIN tarefas_equipes te ON t.id = te.id_tarefa
            LEFT JOIN users u ON te.id_usuario = u.id
            WHERE (te.id_usuario = %s OR t.id_criador = %s)
            GROUP BY
                t.id, p.complexidade, p.prioridade, t.id_projeto, t.id_criador,
                t.titulo, t.descricao, t.data_inicio, t.data_fim, t.status,
                t.data_criacao, t.data_atualizacao, t.esta_em_tempo_tracking,
                t.data_inicio_tracking, t.tempo_gasto, t.complexidade, t.prioridade
            ORDER BY score DESC, t.data_criacao DESC;
        """
        cursor.execute(query, (user_id, user_id))
        return cursor.fetchall()


def convert_date_to_iso(date_str):
    if not date_str or date_str.strip() == '-' or date_str.strip() == '':
        return None
    try:
        dt = datetime.strptime(date_str, "%d/%m/%Y")
        return dt.strftime("%Y-%m-%d")
    except Exception as e:
        return None


def get_projetos_usuario(user_id):
    with instance_cursor() as cursor:
        query = """
            SELECT
                p.id,
                p.nome_projeto,
                p.descricao,
                p.justificativa,
                COALESCE(u.nome, 'Sem responsável') AS responsavel,
                COALESCE(u.cargo, 'Sem cargo') AS cargo,
                p.prioridade,
                p.complexidade,
                COALESCE(TO_CHAR(p.data_inicio, 'DD/MM/YYYY'), '-') AS data_inicio,
                COALESCE(TO_CHAR(p.data_entrega, 'DD/MM/YYYY'), '-') AS data_entrega,
                p.status,
                TO_CHAR(p.data_criacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_criacao,
                TO_CHAR(p.data_atualizacao AT TIME ZONE 'America/Sao_Paulo', 'DD/MM/YYYY HH24:MI') AS data_atualizacao
            FROM projetos p
            LEFT JOIN users u ON p.responsavel = u.id
            WHERE p.responsavel = %s OR p.id IN (
                SELECT projeto_id FROM equipes_projeto WHERE usuario_id = %s
            )
            ORDER BY p.data_criacao DESC;
        """
        cursor.execute(query, (user_id, user_id))
        return cursor.fetchall()


def get_kpis_estrategicos():
    with instance_cursor() as cursor:
        # Tarefas concluídas no prazo do projeto
        cursor.execute("""
            SELECT
                COUNT(CASE WHEN t.data_fim <= p.data_entrega THEN 1 END)::float
                / COUNT(*) * 100 AS perc_no_prazo
            FROM tarefas t
            JOIN projetos p ON t.id_projeto = p.id
            WHERE t.status = 'concluido'
              AND p.data_entrega IS NOT NULL
        """)
        row = cursor.fetchone()
        percentual_no_prazo = row[0] if row and row[0] else 0

        # Velocidade média de conclusão (exemplo)
        cursor.execute("""
            SELECT AVG(t.data_fim - t.data_inicio)::float
            FROM tarefas t
            WHERE t.status = 'concluido'
              AND t.data_inicio IS NOT NULL
              AND t.data_fim IS NOT NULL
        """)
        vel_row = cursor.fetchone()
        velocidade = vel_row[0] if vel_row and vel_row[0] else 0

    return {
        "percentual_no_prazo": round(percentual_no_prazo, 2),
        "velocidade_media": round(velocidade, 2)
    }


def get_ranking_usuarios():
    """
    Retorna um ranking dos usuários baseado em
    quantas tarefas concluíram, tempo médio de conclusão, etc.
    Aqui apresento apenas quantidade de tarefas concluídas como exemplo.
    """
    with instance_cursor() as cursor:
        cursor.execute("""
            SELECT
                u.id,
                u.nome,
                COUNT(*) AS concluidas
            FROM tarefas t
            LEFT JOIN tarefas_equipes te ON t.id = te.id_tarefa
            LEFT JOIN users u ON te.id_usuario = u.id
            WHERE t.status = 'concluido'
            GROUP BY u.id, u.nome
            ORDER BY concluidas DESC
            LIMIT 5;
        """)
        rows = cursor.fetchall()

    ranking = []
    for row in rows:
        ranking.append({
            "user_id": row[0],
            "user_name": row[1],
            "tarefas_concluidas": row[2]
        })

    return ranking


def get_projetos_kpis():
    """
    Retorna dados básicos de projetos,
    como total de projetos, quantos abertos, concluídos, etc.
    (Alguns valores já são calculados diretamente na rota,
     mas podemos unificar aqui se desejar.)
    """
    with instance_cursor() as cursor:
        # total de projetos
        cursor.execute("SELECT COUNT(*) FROM projetos;")
        total_projetos = cursor.fetchone()[0]

        # concluidos
        cursor.execute("SELECT COUNT(*) FROM projetos WHERE status = 'concluido';")
        concluidos = cursor.fetchone()[0]

        # em_progresso
        cursor.execute("SELECT COUNT(*) FROM projetos WHERE status = 'em_progresso';")
        em_progresso = cursor.fetchone()[0]

        # atrasados
        cursor.execute("SELECT COUNT(*) FROM projetos WHERE status = 'atrasado';")
        atrasados = cursor.fetchone()[0]

    return {
        "total_projetos": total_projetos,
        "concluidos": concluidos,
        "em_progresso": em_progresso,
        "atrasados": atrasados
    }


def get_tarefas_por_prioridade():
    """
    Retorna uma lista de tuplas (prioridade, quantidade),
    agrupando todas as tarefas pelo campo 'prioridade'.
    """
    with instance_cursor() as cursor:
        query = """
            SELECT
                CASE
                    WHEN prioridade ILIKE 'alta' THEN 'Alta'
                    WHEN prioridade ILIKE 'média' THEN 'Média'
                    WHEN prioridade ILIKE 'media' THEN 'Média'
                    WHEN prioridade ILIKE 'baixa' THEN 'Baixa'
                    ELSE 'Não Definida'
                END AS prio_label,
                COUNT(*) AS total
            FROM tarefas
            GROUP BY 1
        """
        cursor.execute(query)
        rows = cursor.fetchall()  # Ex: [('Alta', 5), ('Média', 10), ...]
        return rows


def get_tendencias_mensais():
    """
    Retorna uma lista de dicionários com:
      mes: string (ex: "Jan/2025")
      projetos_concluidos: int
      tarefas_concluidas: int
    Considera os últimos 6 meses (pode ajustar).
    """

    with instance_cursor() as cursor:
        # 1) Projetos concluídos por mês (últimos 6 meses)
        query_proj = """
            SELECT
                TO_CHAR(date_trunc('month', data_atualizacao), 'Mon/YY') AS mes,
                COUNT(*) AS qtd
            FROM projetos
            WHERE status = 'concluido'
              AND data_atualizacao >= (NOW() - interval '6 months')
            GROUP BY 1
            ORDER BY date_trunc('month', data_atualizacao)
        """
        cursor.execute(query_proj)
        rows_proj = cursor.fetchall()  # Ex: [('Jan/25', 2), ('Feb/25', 3), ...]

        # 2) Tarefas concluídas por mês (últimos 6 meses)
        query_task = """
            SELECT
                TO_CHAR(date_trunc('month', data_fim::timestamp), 'Mon/YY') AS mes,
                COUNT(*) AS qtd
            FROM tarefas
            WHERE status = 'concluido'
              AND data_fim IS NOT NULL
              AND data_fim >= (NOW() - interval '6 months')
            GROUP BY 1
            ORDER BY date_trunc('month', data_fim::timestamp)
        """
        cursor.execute(query_task)
        rows_tasks = cursor.fetchall()

    # Converter em dicionários indexados por "mes" para juntar
    proj_dict = {r[0]: r[1] for r in rows_proj}   # {"Jan/25": 2, "Feb/25": 3, ...}
    task_dict = {r[0]: r[1] for r in rows_tasks}

    # Juntar todos os meses que apareceram (em projetos ou tarefas)
    meses_unicos = sorted(set(proj_dict.keys()).union(task_dict.keys()),
                          key=lambda x: x)  # Ordena no formato textual "Jan/25" etc.
    # Montar retorno
    resultado = []
    for mes in meses_unicos:
        resultado.append({
            "mes": mes,
            "projetos_concluidos": proj_dict.get(mes, 0),
            "tarefas_concluidas": task_dict.get(mes, 0),
        })

    return resultado


def get_kpis_estrategicos():
    with instance_cursor() as cursor:
        # 1) % de tarefas concluídas no prazo do projeto (data_fim <= data_entrega)
        cursor.execute("""
            SELECT
                COUNT(CASE WHEN t.data_fim <= p.data_entrega THEN 1 END)::float
                / COUNT(*) * 100 AS perc_no_prazo
            FROM tarefas t
            JOIN projetos p ON t.id_projeto = p.id
            WHERE t.status = 'concluido'
              AND p.data_entrega IS NOT NULL
        """)
        row = cursor.fetchone()
        percentual_no_prazo = row[0] if row and row[0] else 0

        # 2) Velocidade média de conclusão (exemplo)
        cursor.execute("""
            SELECT AVG(t.data_fim - t.data_inicio)::float
            FROM tarefas t
            WHERE t.status = 'concluido'
              AND t.data_inicio IS NOT NULL
              AND t.data_fim IS NOT NULL
        """)
        row_vel = cursor.fetchone()
        velocidade = row_vel[0] if row_vel and row_vel[0] else 0

    return {
        "percentual_no_prazo": round(percentual_no_prazo, 2),
        "velocidade_media": round(velocidade, 2)
    }


def get_tarefas_kpis():
    # Lógica ou um stub mínimo:
    return {
        "total_tarefas": 10,
        "total_concluidas": 5,
        "total_atrasadas": 2,
        "em_andamento": 3,
        "tempo_medio_conclusao": 4.5,
        "tempo_medio_em_andamento": 2.5
        }


def get_metasporvida_operadora():
    try:
        with instance_cursor() as cursor:
            query = """
            SELECT
                COALESCE(s.operadoranome, m.operadora) as "Operadora",
                COALESCE(TO_CHAR(s.vltotal, 'R$ FM999G999G999D00'), 'R$ 0,00') as "Valor Vendido",
                COALESCE(s.vidacount, 0) as "Vidas Vendidas",
                m.metas_vidas as "Meta de Vidas",
                CASE
                    WHEN s.vidacount IS NULL THEN '-100%'
                    ELSE ROUND(
                        ((s.vidacount::float - m.metas_vidas::float) / m.metas_vidas::float * 100)::numeric,
                        2
                    ) || '%'
                END as "Diferença da Meta",
                TO_CHAR(s.mesano, 'MM/YYYY') as "Mês/Ano"
            FROM
                meta_vidas_operadora m
            LEFT JOIN
                sumario_operadora s ON (
                    UPPER(s.operadoranome) = UPPER(m.operadora) OR
                    CASE
                        WHEN m.operadora = 'Notre Dame Intermédica' THEN UPPER(s.operadoranome) = 'GNDI'
                        WHEN m.operadora = 'Sulamérica' THEN UPPER(s.operadoranome) = 'SUL AMÉRICA'
                        WHEN m.operadora = 'Medsênior' THEN UPPER(s.operadoranome) = 'MEDSENIOR'
                        ELSE FALSE
                    END
                )
            ORDER BY
                "Operadora";
            """
            cursor.execute(query)
            colnames = [desc[0] for desc in cursor.description]
            results = cursor.fetchall()

            return [dict(zip(colnames, row)) for row in results]

    except Exception as e:
        log(f"Erro ao consultar metas por vida das operadoras: {str(e)}")
        return []


def update_or_insert_metas_vidas_assistente(data):
    try:
        with instance_cursor() as cursor:
            for item in data:
                # Primeiro verifica se o registro existe
                cursor.execute("""
                    SELECT id FROM meta_vidas_assistente_api
                    WHERE assistente = %s AND operadora = %s AND mesAno = %s
                """, (
                    item['assistente'],
                    item['operadora'],
                    item['mesAno']
                ))

                existing_record = cursor.fetchone()

                if existing_record:
                    # Se existe, atualiza
                    cursor.execute("""
                        UPDATE meta_vidas_assistente_api
                        SET metas_vidas = %s
                        WHERE id = %s
                    """, (item['metas_vidas'], existing_record[0]))
                else:
                    # Se não existe, insere
                    cursor.execute("""
                        INSERT INTO meta_vidas_assistente_api
                        (assistente, operadora, metas_vidas, mesAno)
                        VALUES (%s, %s, %s, %s)
                    """, (
                        item['assistente'],
                        item['operadora'],
                        item['metas_vidas'],
                        item['mesAno']
                    ))

            logging.info(f"Dados de metas por vida dos assistentes atualizados com sucesso: {len(data)} registros")
            return True

    except Exception as e:
        logging.error(f"Erro ao inserir/atualizar metas por vida dos assistentes: {str(e)}")
        return False


def format_mesano(date_str):
    try:
        # Converte a string de data para objeto datetime
        date_obj = datetime.strptime(date_str, '%a, %d, %b %Y')
        # Retorna no formato mm/aaaa
        return date_obj.strftime('%m/%Y')
    except Exception as e:
        logging.error(f"Erro ao converter data: {str(e)}")
        return date_str


def consulta_mesesanos_meta_vidas_assistente_api():
    try:
        with instance_cursor() as cursor:
            query = """
                SELECT DISTINCT TO_CHAR(mesano::date, 'MM/YYYY') as mesano,
                       mesano::date as data_ordem
                FROM meta_vidas_assistente_api
                ORDER BY data_ordem DESC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            return [row[0] for row in results]

    except Exception as e:
        logging.error(f"Erro ao consultar meses/anos da meta_vidas_assistente_api: {str(e)}")
        return []


def consulta_metas_vidas_comparativo(mesano):
    try:
        # Converte o mesano para o formato correto
        mesano_formatado = format_mesano(mesano)

        with instance_cursor() as cursor:
            # Primeiro verifica se existem dados na tabela meta_vidas_assistente_api
            check_query = "SELECT COUNT(*) FROM meta_vidas_assistente_api WHERE mesano = %s"
            cursor.execute(check_query, (mesano_formatado,))
            count = cursor.fetchone()[0]
            logging.info(f"Quantidade de registros encontrados para {mesano_formatado}: {count}")

            query = """
                SELECT
                    m.assistente,
                    m.operadora,
                    m.equipe,
                    m.metas_vidas AS meta_vidas,
                    COALESCE(ma.metas_vidas, 0) AS vidas_vendidas,
                    CASE
                        WHEN m.metas_vidas > 0 THEN
                            ROUND(((COALESCE(ma.metas_vidas, 0)::numeric - m.metas_vidas::numeric) / m.metas_vidas::numeric)::numeric, 2)
                        ELSE 0
                    END AS diferenca_percentual,
                    TO_CHAR(ma.mesano::date, 'MM/YYYY') AS mesano
                FROM meta_vidas_assistente m
                LEFT JOIN meta_vidas_assistente_api ma
                    ON m.assistente = ma.assistente
                    AND m.operadora = ma.operadora
                    AND ma.mesano = %s
            """

            cursor.execute(query, (mesano_formatado,))
            colnames = [desc[0] for desc in cursor.description]
            results = cursor.fetchall()

            logging.info(f"Query executada com sucesso. Retornando {len(results)} resultados")
            return [dict(zip(colnames, row)) for row in results]

    except Exception as e:
        logging.error(f"Erro ao consultar metas e vidas comparativo: {str(e)}")
        return []


def update_last_execution_time(status='success'):
    try:
        with instance_cursor() as cursor:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS scheduler_execution (
                    id SERIAL PRIMARY KEY,
                    last_execution TIMESTAMP,
                    status VARCHAR(50)
                )
            """)

            cursor.execute("""
                INSERT INTO scheduler_execution (last_execution, status)
                VALUES (NOW() - INTERVAL '3 hours', %s)
                ON CONFLICT (id) DO UPDATE
                SET last_execution = NOW() - INTERVAL '3 hours', status = %s
            """, (status, status))
            cursor.connection.commit()
    except Exception as e:
        logging.error(f'Erro ao atualizar horário de execução: {e}')
        raise

def get_last_execution():
    try:
        with instance_cursor() as cursor:
            cursor.execute("""
                SELECT
                    TO_CHAR(last_execution, 'DD/MM/YY - HH24:MI:SS') as data_formatada,
                    status
                FROM scheduler_execution
                ORDER BY last_execution DESC
                LIMIT 1
            """)
            result = cursor.fetchone()

            if result:
                return {
                    'data': result[0],
                    'status': result[1]
                }
            return None
    except Exception as e:
        logging.error(f'Erro ao consultar última execução: {e}')
        return None
