import base64
import subprocess
import time
from flask import Flask, after_this_request, jsonify
import json
from ssl import SSLError
import boto3
import zipfile
from botocore.exceptions import NoCredentialsError, ClientError
import tempfile
import logging
from urllib.error import HTTPError
from dotenv import load_dotenv
import requests
from dateutil import parser
import pandas as pd
import gc
import uuid
from io import BytesIO
from requests_oauthlib import OAuth2Session
from requests.auth import HTTPBasicAuth
from itsdangerous import URLSafeTimedSerializer as Serializer
from itsdangerous import URLSafeTimedSerializer
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import atexit
from datetime import datetime, timedelta
import msal
import smtplib
from math import ceil
from email.message import EmailMessage
import os
from flask import (
    Flask,
    render_template,
    request,
    redirect,
    url_for,
    flash,
    session,
    jsonify,
    current_app,
    send_file,
    send_from_directory,
)
from flask_socketio import So<PERSON><PERSON>, emit, disconnect, join_room, leave_room
from threading import Timer
from flask_mail import Mail, Message
from itsdangerous import URLSafeTimedSerializer as Serializer
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.datastructures import FileStorage
from werkzeug.datastructures import FileStorage
from werkzeug.datastructures import FileStorage
from werkzeug.utils import secure_filename
from pdf2image import convert_from_bytes, convert_from_path
import pytesseract
from PIL import Image
import PyPDF2
import pdfplumber
from pyzbar.pyzbar import decode as zbar_decode
from functions import (
    limpar_valor,
    formatar_telefone,
    gerar_saudacao,
    gerar_protocolo,
    record_log,
    formatar_cpf,
    formatar_tel,
    senha_valida,
    normalize_text,
    download_all_files_as_zip,
)
from dependencies import (
    add_registro,
    consulta_email,
    consulta_geral,
    confirm_user_email,
    consulta_cpf,
    atualizar_senha,
    atualizar_cadastro,
    consulta_pswd,
    consulta_usuarios,
    consulta_total_de_usuarios,
    atualizar_cadastro_master,
    buscar_evento,
    add_evento,
    listar_eventos,
    atualizar_evento,
    insert_comissoes,
    consulta_comissoes,
    consulta_grade,
    consulta_tabela,
    excluir_evento,
    excluir_user,
    add_informativo,
    listar_informativos,
    buscar_informativo,
    deletar_informativo,
    consulta_ranking,
    consulta_ranking_individual,
    consulta_meses_anos,
    update_or_insert_data,
    aniversariantes_mes,
    consulta_mesesanos_empresarial,
    consulta_empresarial,
    consulta_empresarial_individual,
    update_or_insert_empresarial,
    consulta_mesesanos_operadora,
    consulta_operadora,
    update_or_insert_operadora,
    consulta_mesesanos_unidades,
    consulta_unidades,
    consulta_unidades_usuario,
    consulta_comparativa,
    consulta_comparativa_individual,
    consulta_admin,
    update_sumario_op_ass,
    consulta_operadora_top6,
    consulta_ranking_op_ass,
    update_sumario_op_cor,
    consulta_ranking_op_cor,
    consulta_valores,
    consulta_meta,
    consulta_valores_anual,
    consulta_assistentes,
    consulta_nome_equipe,
    consulta_valores_superintendente,
    consulta_valores_anual_superintendente,
    update_sumario_assistente_anual,
    update_sumario_assistente_ases,
    update_sumario_corretor_ases,
    consulta_ranking_ases,
    consulta_ranking_ases_corretor,
    listar_setores,
    atualizar_setor,
    consulta_acessos,
    add_portal,
    consulta_portais_to_insert,
    consulta_portais,
    consulta_acessos_to_insert,
    add_acessos,
    atualizar_acesso,
    add_reembolsos,
    consulta_reembolso,
    consulta_reembolso_usuario,
    consulta_detalhes_reembolso,
    atualizar_status_reembolso,
    rejeicao_status_fastmoney,
    add_fastmoney,
    consulta_fastmoney,
    consulta_fastmoney_usuario,
    consulta_fastmoney_unidade,
    consulta_detalhes_fastmoney,
    atualizar_status_fastmoney,
    consulta_permissao,
    consulta_lista_permissoes,
    add_permissoes,
    excluir_permissoes,
    consulta_todos_usuarios,
    consulta_tabela_geral,
    consulta_tabela_grade,
    consulta_tabelas,
    consulta_filtros_comissoes,
    consulta_filtros_por_grade,
    consulta_tabela_op_grade,
    consulta_tabela_grade_mod,
    consulta_tabela_op,
    consulta_tabela_mod,
    consulta_canal_grade,
    add_ticket,
    add_ticket_file,
    add_formulario_pfadesao,
    add_formulario_porte1,
    add_beneficiario,
    delete_ticket_file,
    listar_todos_chamados,
    listar_chamados_por_setor,
    listar_chamados_brcall,
    listar_chamados_usina,
    listar_chamados_rj,
    listar_chamados_luanca,
    consulta_assistentes_luanca,
    atualizar_assistente_chamado,
    listar_chamados_por_user,
    listar_chamados_franquia,
    listar_chamados_rj_colaborador,
    consulta_detalhes_chamado,
    consulta_ticket_file,
    consulta_ticket_file,
    atualizar_status_chamado,
    atualizar_substatus_chamado,
    atualizar_propC_chamado,
    atualizar_formulario_pme,
    atualizar_formulario_pf,
    chat_ticket,
    atualizar_visualizacao_mensagens,
    listar_mensagens_chamado,
    atualizar_protocolo,
    atualizar_responsavel_chamado,
    atualizar_protocolo,
    atualizar_responsavel_chamado,
    reanalisar_fastmoney,
    add_acompanhamento,
    consulta_acompanhamento_geral,
    consulta_acompanhamento_assistente,
    consulta_acompanhamento_detalhada,
    add_boleto_corretor,
    fetch_boletos,
    fetch_boletos_usuario,
    buscar_boleto_por_codigo_barras,
    upload_formlpaccount,
    atualizar_comprovante_upload,
    add_processo_seletivo,
    consulta_processo_seletivo,
    consulta_processo_seletivo_detalhado,
    add_inscricao_seletivo,
    consulta_processos,
    consulta_candidaturas,
    add_boleto_corretor,
    fetch_boletos,
    fetch_boletos_usuario,
    atualizar_comprovante_upload,
    atualizar_status_boleto_db,
    add_fornecedor,
    get_all_fornecedores,
    get_fornecedor_id,
    add_funcionario,
    get_all_funcionario,
    get_funcionario_id,
    atualizar_funcionario,
    add_dados_bancarios,
    add_dados_funcionario,
    get_all_dados_bancarios,
    get_dados_bancarios_id,
    atualizar_dados_bancarios,
    delete_dados_bancarios,
    add_empresa,
    get_all_empresas,
    get_empresa_by_id,
    atualizar_empresa,
    add_conta,
    add_conta_funcionario,
    get_luanca_contas,
    get_all_contas_calendario,
    get_all_contas_calendario_luanca,
    get_contas_por_mes,
    get_pagamentos_por_mes,
    get_pagamentos_luanca,
    consulta_contas_recorrentes,
    get_contas_recorrentes_luanca,
    consulta_contas_recorrentes_id,
    get_conta_by_id,
    delete_conta,
    update_contas,
    atualizar_fornecedor,
    atualizar_pagamento,
    get_metas_gerais,
    update_meta,
    get_datas_globais,
    update_datas_globais,
    get_datas_globais,
    add_projeto,
    get_all_projetos,
    get_projeto_by_id,
    add_team_member_to_project,
    get_team_by_project_id_dict,
    get_tarefas_by_projeto,
    add_tarefa,
    update_tarefa_tracking,
    update_projeto,
    update_tarefa,
    add_membro_a_tarefa,
    get_user_tasks,
    convert_date_to_iso,
    get_projetos_usuario,
    get_tarefas_kpis,
    get_ranking_usuarios,
    get_kpis_estrategicos,
    get_tarefas_por_prioridade,
    get_tendencias_mensais,
    get_tarefas_kpis,
    get_metasporvida_operadora,
    update_or_insert_metas_vidas_assistente,
    consulta_metas_vidas_comparativo,
    get_metasporvida_operadora_metas,
    update_metasporvida_operadora_metas,
    get_metas_vidas_assistentes_metas,
    update_metas_vidas_assistentes_metas,
    update_last_execution_time,
    consulta_mesesanos_meta_vidas_assistente_api,
    get_last_execution,
    consulta_valores_coluna,
)
import os
import re
import warnings
import plotly.graph_objs as go
from dash import Dash, dcc, html
import math
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from requests.exceptions import ConnectionError, Timeout
import logging
from redis import Redis
import redis
from urllib.parse import urlparse
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_limiter.errors import RateLimitExceeded
from dateutil.relativedelta import relativedelta
import threading
import mimetypes
from functools import wraps

# Carregar variáveis de ambiente
load_dotenv()

app = Flask(__name__)
mail = Mail(app)
socketio = SocketIO(app)

# Configurações básicas
app.secret_key = os.getenv("SECRET_KEY")
app.config["MAIL_SALT"] = os.getenv("MAIL_SALT")

# Configurações do Flask-Mail
app.config["MAIL_SERVER"] = "smtps.uhserver.com"
app.config["MAIL_PORT"] = 465
app.config["MAIL_USE_SSL"] = True
app.config["MAIL_USERNAME"] = os.getenv("EMAIL_REMETENTE")
app.config["MAIL_PASSWORD"] = os.getenv("EMAIL_SENHA")
mail = Mail(app)

dash_app = Dash(__name__, server=app, url_base_pathname='/dash/')

# Configurações do GitHub
GITHUB_TOKEN = os.getenv("GITHUB_TOKEN")
REPO_OWNER = os.getenv("GITHUB_REPO_OWNER")
REPO_NAME = os.getenv("GITHUB_REPO_NAME")
BRANCH_NAME = os.getenv("GITHUB_BRANCH_NAME")


# Configurações OAuth para o Instagram
INSTAGRAM_CLIENT_ID = os.getenv("INSTAGRAM_CLIENT_ID")
INSTAGRAM_CLIENT_SECRET = os.getenv("INSTAGRAM_CLIENT_SECRET")
INSTAGRAM_REDIRECT_URI = os.getenv("INSTAGRAM_REDIRECT_URI")
INSTAGRAM_AUTHORIZATION_BASE_URL = "https://api.instagram.com/oauth/authorize"
INSTAGRAM_TOKEN_URL = "https://api.instagram.com/oauth/access_token"
INSTAGRAM_SCOPE = ["user_profile", "user_media"]
INSTAGRAM_ACCESS_TOKEN = os.getenv("INSTAGRAM_ACCESS_TOKEN")

# Configurações AWS S3
S3_BUCKET = os.getenv("S3_BUCKET")
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
SES_REGION = "sa-east-1"

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# Determinar o ambiente com base na variável FLASK_ENV
env = os.getenv('FLASK_ENV', 'local')
redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')

# Parse a URL do Redis
parsed_url = urlparse(redis_url)

# Configuração do Redis
if env == 'prod':
    redis_url = redis_url.replace("redis://", "rediss://")
    redis_url_with_ssl = f"{redis_url}?ssl_cert_reqs=none"
    redis_client = redis.StrictRedis.from_url(redis_url_with_ssl, decode_responses=True)
    storage_uri = redis_url_with_ssl
elif env == 'qa':
    redis_client = redis.StrictRedis(
        host=parsed_url.hostname,
        port=parsed_url.port,
        password=parsed_url.password,
        ssl=True,
        ssl_cert_reqs=None,
        decode_responses=True
    )
    storage_uri = f"rediss://{parsed_url.hostname}:{parsed_url.port}?ssl_cert_reqs=none"
else:
    redis_client = redis.StrictRedis(
        host=parsed_url.hostname,
        port=parsed_url.port,
        decode_responses=True
    )
    storage_uri = f"redis://{parsed_url.hostname}:{parsed_url.port}"

limiter = Limiter(
    get_remote_address,
    app=app,
    storage_uri=storage_uri
)


def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Define o ambiente usando a variável FLASK_ENV
        env = os.getenv('FLASK_ENV', 'local')
        if env in ['prod', 'qa']:
            if "user_id" not in session:
                return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function


# Função de upload das imagens no github.
def upload_image_to_github(image_path, commit_message):
    with open(image_path, "rb") as image_file:
        encoded_image = base64.b64encode(image_file.read()).decode("utf-8")

    file_name = os.path.basename(image_path)
    url = f"https://api.github.com/repos/{REPO_OWNER}/{REPO_NAME}/contents/{file_name}"

    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json"
    }

    data = {
        "message": commit_message,
        "content": encoded_image,
        "branch": BRANCH_NAME
    }

    for attempt in range(3):  # Tentar até 3 vezes
        try:
            response = requests.put(url, json=data, headers=headers)
            response.raise_for_status()  # Levanta um erro HTTP para respostas com status de erro
            if response.status_code == 201:
                print("Imagem enviada com sucesso!")
                return response.json()['content']['download_url']
        except (SSLError, ConnectionError, HTTPError) as e:
            print(f"Tentativa {attempt + 1} falhou: {e}")
            if attempt == 2:  # Última tentativa falhou
                raise
    return None


@app.route("/instagram/login")
def instagram_login():
    """
    Rota para iniciar o login com o Instagram.

    Esta função cria uma sessão OAuth2 com o Instagram usando o client_id,
    redirect_uri e scope definidos. Em seguida, ela obtém a URL de autorização
    e redireciona o usuário para a página de login do Instagram.

    - Armazena o estado da sessão OAuth no objeto de sessão Flask.
    - Redireciona o usuário para a URL de autorização do Instagram.
    """
    instagram = OAuth2Session(
        INSTAGRAM_CLIENT_ID, redirect_uri=INSTAGRAM_REDIRECT_URI, scope=INSTAGRAM_SCOPE
    )
    authorization_url, state = instagram.authorization_url(
        INSTAGRAM_AUTHORIZATION_BASE_URL
    )
    session["oauth_state_instagram"] = state
    return redirect(authorization_url)


@app.route("/instagram/callback")
def instagram_callback():
    """
    Rota de callback para o Instagram.

    Após o usuário autorizar o aplicativo no Instagram, ele é redirecionado
    para esta rota. A função troca o código de autorização por um token de
    acesso, que é então armazenado na sessão Flask.

    - Recupera o estado da sessão OAuth.
    - Troca o código de autorização pelo token de acesso.
    - Armazena o token de acesso no objeto de sessão Flask.
    - Redireciona o usuário para a rota do feed do Instagram.
    """
    instagram = OAuth2Session(
        INSTAGRAM_CLIENT_ID,
        state=session["oauth_state_instagram"],
        redirect_uri=INSTAGRAM_REDIRECT_URI,
    )
    token = instagram.fetch_token(
        INSTAGRAM_TOKEN_URL,
        client_secret=INSTAGRAM_CLIENT_SECRET,
        authorization_response=request.url,
    )
    session["oauth_token_instagram"] = token
    return redirect(url_for("instagram_feed"))


@app.route("/instagram/feed")
def instagram_feed():
    """
    Rota para exibir o feed do Instagram.

    Esta função usa o token de acesso armazenado na sessão Flask para fazer
    solicitações à API do Instagram. Primeiro, ela obtém as informações do
    perfil do usuário, e em seguida, obtém os posts do usuário.

    - Usa o token de acesso para autenticar as solicitações.
    - Obtém e processa as informações do perfil do usuário.
    - Obtém e processa os posts do usuário.
    - Renderiza a página do feed do Instagram com as informações obtidas.
    """
    instagram = OAuth2Session(
        INSTAGRAM_CLIENT_ID, token=session["oauth_token_instagram"]
    )
    profile_response = instagram.get(
        "https://graph.instagram.com/me?fields=id,username"
    )
    profile_info = profile_response.json()
    media_response = instagram.get(
        "https://graph.instagram.com/me/media?fields=id,caption,media_url"
    )
    posts = media_response.json()["data"]
    return render_template("instagram_feed.html", profile=profile_info, posts=posts)


@app.route("/")
def index():
    if "user_id" in session:
        return redirect(url_for("home"))
    return render_template("login.html")


@app.route("/userActive")
def user_active():
    user_name = session.get("user_name")
    return render_template("user_active.html", user_name=user_name)


@app.route("/get_windows_username")
def get_windows_username():
    # Simulação de captura do nome de usuário do Windows
    import getpass
    username = getpass.getuser()
    return jsonify(username=username)


@app.errorhandler(RateLimitExceeded)
def handle_ratelimit_exceeded(e):
    retry_after = e.description.split(" ")[-1]  # Extrai o tempo de espera da descrição
    return jsonify({"success": False, "message": f"Você excedeu o número de tentativas. Por favor, tente novamente em alguns minutos!"}), 429


@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":

        # Verifica se os campos obrigatórios estão presentes
        if "email" not in request.form or "password" not in request.form:
            logging.warning("Email or password missing in the form.")
            flash("Campos de email ou senha ausentes.", "warning")
            return redirect(url_for("login"))

        # Coleta os dados do formulário
        email = request.form["email"]
        password = request.form["password"]
        windows_username = request.form.get("windows_username", "Unknown")
        ip_address = request.headers.get('X-Forwarded-For', request.remote_addr)

        logging.info(f"IP do Usuário: {ip_address}, Usuário do Windows: {windows_username}")

        user = consulta_email(email)
        if user:
            logging.info(f"User found: {user}")

            email_confirmed = user[0][4]
            userActive = user[0][6]

            if check_password_hash(user[0][3], password):
                if not userActive:
                    session["user_name"] = user[0][1]
                    return jsonify({"success": False, "redirect_url": url_for("user_active")})
                elif email_confirmed and userActive:
                    session["user_id"] = user[0][0]
                    session["user_name"] = user[0][1]
                    session["user_type"] = user[0][5]
                    session["user_active"] = user[0][6]
                    session["setor_id"] = user[0][7]
                    session["profile_image_url"] = user[0][8]

                    logging.info(f"User {user[0][1]} logged in with setor_id: {user[0][7]}")

                    return jsonify({"success": True, "redirect_url": url_for("home")})
                else:
                    session["user_email"] = email
                    return (
                        jsonify({
                            "success": False,
                            "redirect_url": url_for("inactive"),
                            "message": "Sua conta ainda não foi ativada. Por favor, verifique seu e-mail.",
                        }),
                        401,
                    )
            else:
                logging.warning(f"Invalid password for email: {email}")
                return jsonify({"success": False, "message": "Senha inválida."}), 401
        else:
            logging.warning(f"Email {email} not found.")
            return jsonify({"success": False, "message": "E-mail não encontrado."}), 404

    return render_template("login.html")


@app.route("/simular_usuario/<int:user_id>")
def simular_usuario(user_id):
    # Loga o ID que chegou na rota
    logging.info(f"Iniciando simulação para user_id => {user_id}")

    # Garante que quem chama está autenticado
    if "user_id" not in session:
        flash("Por favor, faça o login para continuar.", "warning")
        return redirect(url_for("login"))

    # Só admins (user_type == 7) podem simular
    if session.get("user_type") != 7:
        logging.info(f"Usuário {session.get('user_id')} não tem permissão para simular.")
        flash("Você não tem permissão para simular outros usuários.", "warning")
        return redirect(url_for("index"))

    # Busca os dados do usuário alvo
    target = consulta_geral(user_id)
    logging.info(f"Resultado da consulta_geral => {target}")
    if not target:
        flash("Usuário não encontrado.", "danger")
        return redirect(url_for("index"))

    # Guarda o admin original para poder voltar depois
    session["admin_id"] = session["user_id"]

    # Preenche a sessão com TODOS os campos que o login original faria
    session["user_id"]           = target[0]   # id do usuário
    session["user_type"]         = target[1]   # tipo de usuário
    session["user_name"]         = target[2]   # nome completo
    session["user_active"]       = target[6]   # ativo/inativo
    session["profile_image_url"] = target[10]  # URL da foto de perfil
    session["setor_id"]          = target[13]   # setor_id do usuário

    logging.info(f"Simulação realizada com sucesso. Nova sessão: {session}")
    flash(f"Você está visualizando o sistema como {target[2]}", "info")
    return redirect(url_for("index"))


@app.route("/voltar_admin")
def voltar_admin():
    if "admin_id" in session:
        # Recupera os dados do admin original
        admin_data = consulta_geral(session["admin_id"])

        # Restaura a sessão do admin
        session["user_id"] = admin_data[0]
        session["user_name"] = admin_data[2]
        session["user_type"] = admin_data[1]

        # Remove o ID do admin da sessão
        del session["admin_id"]

        flash("Você voltou para sua conta de administrador")
        return redirect(url_for("index"))
    else:
        return redirect(url_for("index"))


@app.route("/api/aniversariantes", methods=["GET"])
def get_aniversariantes():
    try:
        aniversariantes = aniversariantes_mes()
        return jsonify(aniversariantes)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/homepage", methods=["GET"])
def home():
    if "user_id" not in session:
        return redirect(url_for("index"))

    user_type = session.get("user_type")
    setor_id = session.get("setor_id")
    logging.info(f'Setor: {setor_id}')

    data = listar_informativos(10, 0)

    # Se user_type for 5 e setor_id for None, mostra o modal e lista os setores
    show_modal = user_type == 5 and setor_id is None
    setores = listar_setores() if show_modal else []

    access_token = INSTAGRAM_ACCESS_TOKEN

    url = "https://graph.instagram.com/me/media"
    params = {
        "fields": "id,caption,media_url,permalink,media_type,timestamp",
        "access_token": access_token,
        "limit": 7,
    }
    media_response = requests.get(url, params=params)

    if media_response.status_code != 200:
        return render_template(
            "homepage.html",
            user_name=session.get("user_name"),
            active_page="home",
            instagram_posts=[],
            data=data,
            aniversariantes=[],
            show_modal=show_modal,
            setores=setores
        )

    try:
        response_json = media_response.json()
        posts = response_json.get("data", [])
        posts = sorted(posts, key=lambda x: x["timestamp"], reverse=True)
    except Exception as e:
        logging.error(f"Error parsing Instagram response: {e}")
        return render_template(
            "homepage.html",
            user_name=session.get("user_name"),
            active_page="home",
            instagram_posts=[],
            data=data,
            aniversariantes=[],
            show_modal=show_modal,
            setores=setores
        )

    return render_template(
        "homepage.html",
        user_name=session.get("user_name"),
        active_page="home",
        instagram_posts=posts,
        data=data,
        aniversariantes=[],
        show_modal=show_modal,
        setores=setores
    )


@app.route("/atualizar_setor", methods=["POST"])
def atualizar_setor_route():
    if "user_id" not in session:
        return redirect(url_for("index"))

    user_id = session["user_id"]
    setor_id = request.form.get("setor_id")

    app.logger.info(f"Received user_id: {user_id}, setor_id: {setor_id}")

    if not setor_id:
        app.logger.error("setor_id não fornecido")
        return jsonify({"success": False, "message": "ID do setor não fornecido"}), 400

    if atualizar_setor(user_id, setor_id):
        # Logout do usuário após a atualização
        session.pop("user_id", None)
        session.pop("user_type", None)
        session.pop("setor_id", None)
        return jsonify({"success": True, "message": "Setor atualizado com sucesso. Por favor, faça login novamente."})
    else:
        app.logger.error(f"Erro ao atualizar o setor para user_id {user_id} com setor_id {setor_id}")
        return jsonify({"success": False, "message": "Erro ao atualizar o setor"}), 400


@app.route("/organizacao-brazil")
def brazil():
    if "user_id" not in session:
        return redirect(url_for("index"))
    return render_template("brazil.html", active_page="brazil")


@app.route("/calendar")
def calendar():
    if "user_id" not in session:
        return redirect(url_for("index"))
    return render_template("calendar.html", active_page="calendar")


@app.route("/logout")
def logout():
    session.clear()
    return redirect(url_for("index"))


def upload_image_to_s3(file_path, bucket_name):
    s3_client = boto3.client('s3',
                             aws_access_key_id=AWS_ACCESS_KEY_ID,
                             aws_secret_access_key=AWS_SECRET_ACCESS_KEY)
    file_name = os.path.basename(file_path)
    try:
        logging.debug(f"Uploading {file_name} to bucket {bucket_name}")
        s3_client.upload_file(file_path, bucket_name, file_name)
        url = f"https://{bucket_name}.s3.amazonaws.com/{file_name}"
        logging.info(f"Upload successful: {url}")
        return url
    except NoCredentialsError:
        logging.error("Credentials not available")
        return None
    except ClientError as e:
        logging.error(f"Error uploading file: {str(e)}")
        return None


@app.route("/register", methods=["GET", "POST"])
def register():
    if request.method == "POST":
        logging.debug(f"Request data: {request.data}")
        logging.debug(f"Request form: {request.form}")
        logging.debug(f"Request files: {request.files}")

        if not request.form and not request.files:
            logging.error("Nenhum dado fornecido.")
            return jsonify({"error": "Nenhum dado fornecido."}), 400

        # Convert form data to a dictionary
        data = request.form.to_dict()

        tipo_usuario = data.get("tipo_usuario")
        nome = data.get("nome")
        data_nascimento = data.get("data_nascimento")
        cpf = data.get("cpf")
        email = data.get("email")
        senha = data.get("senha")
        confirm_senha = data.get("confirm_senha")
        telefone = data.get("telefone")
        unidade = data.get("unidade_id")
        equipe = data.get("equipe_id")

        clear_cpf = re.sub(r"\D", "", cpf)
        clear_tel = re.sub(r"\D", "", telefone)

        logging.debug(f"Data received: {data}")

        if not nome or not cpf or not email or not senha:
            logging.error("Todos os campos são obrigatórios.")
            return jsonify({"error": "Todos os campos são obrigatórios."}), 400
        elif not senha_valida(senha):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "A nova senha não atende aos critérios de segurança estabelecidos. Por favor, preencha os campos corretamente antes de continuar com a troca de senha.",
                    }
                ),
                400,
            )
        elif senha != confirm_senha:
            logging.error("As senhas não conferem.")
            return jsonify({"error": "As senhas não conferem."}), 400

        elif consulta_email(email):
            logging.error("E-mail já cadastrado.")
            return jsonify({"error": "E-mail já cadastrado."}), 400

        elif consulta_cpf(cpf):
            logging.error("CPF já cadastrado.")
            return jsonify({"error": "CPF já cadastrado."}), 400

        elif tipo_usuario not in ["1", "2", "3", "4", "5", "6", "7"]:
            logging.error(f"Erro de validação no tipo de usuário: {tipo_usuario}")
            return (
                jsonify({"success": False, "message": "Tipo de usuário inválido."}),
                400,
            )

        elif unidade not in ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]:
            logging.error(f"Erro de validação na unidade: {unidade}")
            return jsonify({"success": False, "message": "Unidade inválida."}), 400

        if tipo_usuario in ["3", "4"]:
            if equipe not in [
                "1",
                "2",
                "3",
                "4",
                "5",
                "6",
                "7",
                "8",
                "9",
                "10",
                "11",
                "12",
                "13",
            ]:
                logging.error(f"Erro de validação na equipe: {equipe}")
                return jsonify({"success": False, "message": "Equipe inválida."}), 400
        else:
            equipe = None

        # Upload da imagem de perfil para o S3
        profile_image_url = None
        if 'profile_image' in request.files:
            imagem = request.files.get("profile_image")
            temp_dir = tempfile.mkdtemp()
            file_path = os.path.join(temp_dir, imagem.filename)
            imagem.save(file_path)
            try:
                profile_image_url = upload_image_to_s3(file_path, S3_BUCKET)
                logging.debug(f"Profile image URL: {profile_image_url}")
            except Exception as e:
                logging.error(f"Erro ao fazer upload da imagem: {str(e)}")
            finally:
                os.remove(file_path)
                os.rmdir(temp_dir)

        senha_hash = generate_password_hash(senha, method="pbkdf2:sha256")
        try:
            add_registro(
                tipo_usuario,
                nome,
                data_nascimento,
                clear_cpf,
                email,
                senha_hash,
                False,
                clear_tel,
                unidade,
                equipe,
                ativo=True,
                profile_image_url=profile_image_url,
            )
            session["user_email"] = email
            send_confirmation_email(email)
            logging.info("Registro efetuado com sucesso!")
            return (
                jsonify(
                    {
                        "success": True,
                        "message": "Registro efetuado com sucesso! Por favor, confira seu e-mail para ativar sua conta.",
                    }
                ),
                200,
            )
        except Exception as e:
            logging.error(f"Erro ao registrar usuário: {str(e)}")
            return jsonify({"error": "Erro ao registrar usuário."}), 500

    return render_template("register.html")


# Função para deletar imagem do S3
def delete_image_from_s3(image_url, bucket_name):
    s3_client = boto3.client('s3',
                             aws_access_key_id=AWS_ACCESS_KEY_ID,
                             aws_secret_access_key=AWS_SECRET_ACCESS_KEY)
    file_name = os.path.basename(image_url)
    try:
        logging.debug(f"Deleting {file_name} from bucket {bucket_name}")
        s3_client.delete_object(Bucket=bucket_name, Key=file_name)
        logging.info(f"Deletado com Sucesso: {file_name}")
    except NoCredentialsError:
        logging.error("Credentials not available")
    except ClientError as e:
        logging.error(f"Error deleting file: {str(e)}")


@app.route("/alteracao_cadastral", methods=["POST"])
def alteracao_cadastral():
    if "user_id" not in session:
        return jsonify({"message": "Você precisa estar logado para alterar seus dados cadastrais."}), 403

    user_id = session["user_id"]
    novo_nome = request.form.get("nome")
    novo_email = request.form.get("email")
    telefone = request.form.get("telefone")
    novo_telefone = re.sub(r"\D", "", telefone)

    dados_usuario = consulta_geral(user_id)
    if not dados_usuario:
        return jsonify({"message": "Usuário não encontrado."}), 404

    profile_image_url = dados_usuario[10]

    if request.files.get("profile_image"):
        imagem = request.files.get("profile_image")
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, imagem.filename)
        imagem.save(file_path)
        try:
            # Deletar a foto antiga se existir
            if profile_image_url:
                delete_image_from_s3(profile_image_url, S3_BUCKET)
            # Fazer o upload da nova imagem
            profile_image_url = upload_image_to_s3(file_path, S3_BUCKET)
        finally:
            os.remove(file_path)
            os.rmdir(temp_dir)

    try:
        atualizar_cadastro(user_id, novo_nome, novo_email, novo_telefone, profile_image_url)
        return jsonify({"message": "Dados atualizados com sucesso!"}), 200
    except Exception as e:
        print(e)  # Para debugging
        return jsonify({"message": "Erro na solicitação."}), 500


@app.route("/usuarios")
def usuarios():
    if "user_id" not in session:
        flash("Por favor, faça o login para continuar.")
        return redirect(url_for("index"))

    user_id = session["user_id"]
    dados_usuario = consulta_geral(user_id)

    if dados_usuario:
        usuario = {
            "id": dados_usuario[0],
            "nome": dados_usuario[2],
            "data_nascimento": dados_usuario[3].strftime("%d/%m/%Y") if dados_usuario[3] else "",
            "cpf": dados_usuario[4],
            "email": dados_usuario[5],
            "telefone": dados_usuario[7],
            "profile_image_url": dados_usuario[10]
        }
        return render_template("usuarios.html", usuario=usuario)
    else:
        flash("Usuário não encontrado.")
        return redirect(url_for("index"))


def formatar_cpf(cpf):
    return (
        f"{cpf[:3]}.{cpf[3:6]}.{cpf[6:9]}-{cpf[9:]}" if cpf and len(cpf) == 11 else cpf
    )


def formatar_tel(telefone):
    telefone = str(telefone)  # Converte o telefone para string
    if telefone and len(telefone) == 10:
        return f"({telefone[:2]}) {telefone[2:6]}-{telefone[6:]}"
    elif telefone and len(telefone) == 11:
        return f"({telefone[:2]}) {telefone[2:7]}-{telefone[7:]}"
    else:
        return telefone


def senha_valida(senha):
    if len(senha) < 6 or len(senha) > 64:
        return False
    if not re.search("[a-zA-Z]", senha):
        return False
    if not re.search("[0-9]", senha):
        return False
    if not re.search("[!@#$%&*]", senha):
        return False
    return True


@app.route("/change_password", methods=["GET", "POST"])
def change_password():
    if request.method == "POST":
        if "user_id" not in session:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Por favor, faça o login para continuar.",
                    }
                ),
                401,
            )

        user_id = session["user_id"]
        current_password = request.form.get("current-password")
        new_password = request.form.get("new-password")
        confirm_password = request.form.get("confirm-password")

        if not current_password or not new_password or not confirm_password:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Campos obrigatórios estão em branco. Por favor, preencha antes de continuar com a troca de senha.",
                    }
                ),
                400,
            )

        user_info = consulta_pswd(user_id)
        if not user_info or not check_password_hash(user_info[1], current_password):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Senha atual inválida. Por favor, revise as informações inseridas e tente novamente.",
                    }
                ),
                400,
            )

        if new_password != confirm_password:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Nova senha e confirmação não correspondem. Por favor, preencha os campos corretamente antes de continuar com a troca de senha.",
                    }
                ),
                400,
            )

        if not senha_valida(new_password):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "A nova senha não atende aos critérios de segurança estabelecidos. Por favor, preencha os campos corretamente antes de continuar com a troca de senha.",
                    }
                ),
                400,
            )

        try:
            nova_senha_hash = generate_password_hash(
                new_password, method="pbkdf2:sha256"
            )
            atualizar_senha(user_id, nova_senha_hash)
            return jsonify(
                {"success": True, "message": "Senha atualizada com sucesso!"}
            )
        except Exception as e:
            print(e)  # Para debugging
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Ocorreu um erro ao atualizar a senha. Por favor, tente novamente.",
                    }
                ),
                500,
            )

    return render_template("password.html", active_page="change_password")


@app.route("/inactive")
def inactive():
    return render_template("inactive.html")


@app.route("/confirm_email")
def show_confirm_email():
    return render_template("confirm_email.html")


def generate_confirmation_token(email):
    serializer = Serializer(current_app.config["SECRET_KEY"], salt="email-confirm")
    return serializer.dumps(email, salt=current_app.config["MAIL_SALT"])


def confirm_token(token, expiration=3600):
    serializer = Serializer(current_app.config["SECRET_KEY"], salt="email-confirm")
    try:
        email = serializer.loads(
            token, salt=current_app.config["MAIL_SALT"], max_age=expiration
        )
    except:
        return False
    return email


def send_confirmation_email(user_email):
    remetente = "<EMAIL>"  # Deve ser um domínio ou e-mail verificado no SES
    token = generate_confirmation_token(user_email)
    confirm_url = url_for("confirm_email", token=token, _external=True)

    # Gera o corpo do e-mail
    corpo_email = (
        "Bem-vindo!\n\n"
        "Obrigado por se cadastrar. Por favor, siga no link para ativar sua conta:\n"
        f"{confirm_url}\n\n"
        "Saudações,\nEquipe Brazil Health"
    )

    # Criando o cliente SES
    ses_client = boto3.client(
        "ses",
        region_name=SES_REGION,
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    )

    try:
        # Enviando o e-mail via SES
        response = ses_client.send_email(
            Source=remetente,
            Destination={
                "ToAddresses": [user_email],
            },
            Message={
                "Subject": {
                    "Data": "Brazil Health - Valide seu e-mail"
                },
                "Body": {
                    "Text": {
                        "Data": corpo_email
                    }
                },
            }
        )
        logging.info(f"E-mail de confirmação enviado para {user_email}. Response: {response}")
        return True
    except ClientError as e:
        logging.error(f"Erro ao enviar e-mail de confirmação para {user_email}: {e.response['Error']['Message']}")
        return False


@app.route("/confirm/<token>", methods=["GET", "POST"])
def confirm_email(token):
    try:
        email = confirm_token(token)
    except:
        flash("O link de confirmação é inválido ou expirou.")
        return redirect(url_for("index"))

    user_info = consulta_email(email)
    if user_info:
        user = user_info[0]
        if not user[4]:
            confirm_user_email(user[0])
            session.pop("user_email", None)
            flash("Sua conta foi ativada! Agora você pode fazer login.")
            return redirect(url_for("index"))
        else:
            flash("Conta já ativada.")
            return redirect(url_for("index"))
    else:
        flash("E-mail não encontrado.")
        return redirect(url_for("index"))


@app.route("/resend_confirmation_email")
def resend_confirmation_email():
    user_email = session.get("user_email")
    if not user_email:
        return (
            jsonify(
                {"success": False, "message": "Nenhum e-mail encontrado na sessão."}
            ),
            401,
        )

    send_confirmation_email(user_email)
    session.pop("user_email", None)
    return jsonify(
        {"success": True, "message": "E-mail de confirmação reenviado com sucesso."}
    )


@app.route("/admin")
def admin():
    por_pagina = 50
    pagina = request.args.get("pagina", 1, type=int)

    # Parâmetros de filtro e ordenação
    coluna_ordenacao = request.args.get("ordenar_por", "id")
    direcao_ordenacao = request.args.get("direcao", "desc")
    filtro_coluna = request.args.get("filtro_coluna", "")
    filtro_valor = request.args.get("filtro_valor", "")

    # Consulta com filtros e ordenação
    total_de_usuarios = consulta_total_de_usuarios(filtro_coluna, filtro_valor)
    total_de_paginas = ceil(total_de_usuarios / por_pagina)
    usuarios = consulta_usuarios(pagina, por_pagina, coluna_ordenacao, direcao_ordenacao, filtro_coluna, filtro_valor)

    map_perfil = {
        1: "Master",
        2: "Sócio",
        3: "Superintendente",
        4: "Supervisor",
        5: "Funcionário",
        6: "Franqueado",
        7: "Dev",
    }

    usuarios_formatados = []
    for usuario in usuarios:
        usuarios_formatados.append(
            {
                "id": usuario[0],
                "tipo_usuario": map_perfil.get(usuario[1], "Desconhecido"),
                "nome": usuario[2],
                "data_nascimento": usuario[3].strftime("%d/%m/%Y")
                if usuario[3]
                else "",
                "cpf": formatar_cpf(usuario[4]),
                "email": usuario[5],
                "email_confirmed": "Sim" if usuario[6] else "Não",
                "telefone": formatar_tel(usuario[7]),
            }
        )

    return render_template(
        "admin.html",
        usuarios=usuarios_formatados,
        pagina_atual=pagina,
        total_de_paginas=total_de_paginas,
        ordenar_por=coluna_ordenacao,
        direcao=direcao_ordenacao,
        filtro_coluna=filtro_coluna,
        filtro_valor=filtro_valor
    )


@app.route('/export_usuarios')
def export_usuarios():
    todos_usuarios = consulta_admin()  # Modifique para a sua função de consulta

    map_perfil = {
        1: "Master",
        2: "Sócio",
        3: "Superintendente",
        4: "Supervisor",
        5: "Funcionário",
        6: "Franqueado",
        7: "Dev",
    }

    usuarios_formatados = []
    for usuario in todos_usuarios:
        usuarios_formatados.append(
            {
                "ID": usuario[0],
                "Perfil": map_perfil.get(usuario[1], "Desconhecido"),
                "Nome": usuario[2],
                "Data de Nascimento": usuario[3].strftime("%d/%m/%Y") if usuario[3] else "",
                "CPF": formatar_cpf(usuario[4]),
                "E-mail": usuario[5],
                "E-mail Confirmado?": "Sim" if usuario[6] else "Não",
                "Telefone": formatar_tel(usuario[7]),
            }
        )

    df = pd.DataFrame(usuarios_formatados)

    # Salva o DataFrame para um objeto BytesIO
    output = BytesIO()
    writer = pd.ExcelWriter(output, engine='openpyxl')
    df.to_excel(writer, index=False, sheet_name='Usuarios')
    writer.close()  # Use close() em vez de save()
    output.seek(0)

    # Envia o arquivo para o cliente
    return send_file(output, download_name="usuarios_cadastrados.xlsx", as_attachment=True)


@app.route("/atualizar_usuario", methods=["POST"])
def atualizar_usuario():
    print(request.form)
    usuario_id = request.form.get("user_id")
    novo_nome = request.form.get("nome")
    nova_data_nascimento = request.form.get("data_nascimento")
    novo_cpf = re.sub(r"\D", "", request.form.get("cpf"))
    novo_email = request.form.get("email")
    novo_tipo_usuario = request.form.get("tipo_usuario")
    novo_unidade_id = request.form.get("unidade_id")
    novo_equipe_id = request.form.get("equipe_id")
    if novo_equipe_id == "":
        novo_equipe_id = None
    telefone = request.form.get("telefone")
    novo_telefone = re.sub(r"\D", "", telefone)
    ativo = request.form.get("ativo") == "on"
    nova_senha = request.form.get("senha")

    if request.files.get("profile_image"):
        imagem = request.files.get("profile_image")
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, imagem.filename)
        imagem.save(file_path)
        try:
            profile_image_url = upload_image_to_s3(file_path, S3_BUCKET)
        finally:
            os.remove(file_path)
            os.rmdir(temp_dir)
    else:
        profile_image_url = None

    # Validações
    if not re.match(r"^[a-zA-Z\sáéíóúâêîôûãõçÁÉÍÓÚÂÊÎÔÛÃÕÇ]+$", novo_nome):
        print(f"Erro de validação no nome: {novo_nome}")
        return jsonify({"success": False, "message": "Nome inválido."}), 400

    if not re.match(r"\d{4}-\d{2}-\d{2}", nova_data_nascimento):
        print(f"Erro de validação na data de nascimento: {nova_data_nascimento}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": "Formato de data inválido. Use YYYY-MM-DD.",
                }
            ),
            400,
        )

    if not re.match(r"^\d{11}$", novo_cpf):
        print(f"Erro de validação no CPF: {novo_cpf}")
        return jsonify({"success": False, "message": "CPF inválido."}), 400

    if not re.match(r"[^@]+@[^@]+\.[^@]+", novo_email):
        print(f"Erro de validação no e-mail: {novo_email}")
        return jsonify({"success": False, "message": "E-mail inválido."}), 400

    if novo_tipo_usuario not in ["1", "2", "3", "4", "5", "6", "7"]:
        print(f"Erro de validação no tipo de usuário: {novo_tipo_usuario}")
        return jsonify({"success": False, "message": "Tipo de usuário inválido."}), 400

    if novo_unidade_id not in ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]:
        print(f"Erro de validação na unidade: {novo_unidade_id}")
        return jsonify({"success": False, "message": "Unidade inválida."}), 400

    if novo_equipe_id not in [
        None,
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "10",
        "11",
        "12",
        "13",
        "14",
        "15",
    ]:
        print(f"Erro de validação na equipe: {novo_equipe_id}")
        return jsonify({"success": False, "message": "Equipe inválida."}), 400

    # Validação e Criptografia da nova senha, se fornecida
    nova_senha_hash = None
    if nova_senha:
        if not senha_valida(nova_senha):
            return jsonify({"success": False, "message": "A nova senha não atende aos critérios de segurança estabelecidos."}), 400
        nova_senha_hash = generate_password_hash(nova_senha, method="pbkdf2:sha256")

    try:
        atualizar_cadastro_master(
            usuario_id,
            novo_nome,
            nova_data_nascimento,
            novo_cpf,
            novo_email,
            novo_tipo_usuario,
            novo_telefone,
            novo_unidade_id,
            novo_equipe_id,
            ativo,
            profile_image_url,
            nova_senha_hash
        )
        return jsonify({"success": True, "message": "Usuário atualizado com sucesso!"})
    except Exception as e:
        print(f"Erro ao atualizar o usuário: {e}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": "Erro ao atualizar o usuário.",
                    "error": str(e),
                }
            ),
            500,
        )


@app.route("/change_user/<int:user_id>")
def change_user(user_id):
    user_data = consulta_geral(user_id)
    if user_data:
        user_dict = {
            "id": user_data[0],
            "tipo_usuario": user_data[1],
            "nome": user_data[2],
            "data_nascimento": user_data[3].strftime("%Y-%m-%d") if user_data[3] else "",
            "cpf": formatar_cpf(user_data[4]),
            "email": user_data[5],
            "ativo": user_data[6],  # Corrigido o índice para o campo ativo
            "telefone": formatar_tel(str(user_data[7])),
            "unidade_id": user_data[8],
            "equipe_id": user_data[9],
        }
        print(user_dict)
        return render_template("change_user.html", user=user_dict)
    else:
        return "Usuário não encontrado", 404


@app.route("/excluir_user/<int:user_id>", methods=["DELETE"])
def excluir_user_route(user_id):
    if "user_id" not in session:
        flash("Por favor, faça o login para continuar.")
        return redirect(url_for("index")), 401

    try:
        excluir_user(user_id)
        return jsonify({"message": "Usuário excluído com sucesso!"})
    except Exception as e:
        return jsonify({"error": "Não foi possível excluir o usuário"}), 500


@app.route("/adicionar_evento", methods=["POST"])
def adicionar_evento_route():
    data = request.get_json()
    app.logger.info(f"Dados recebidos: {data}")  # Log de debug
    if not data:
        return jsonify({"error": "No data provided"}), 400

    usuario_id = session.get("user_id")
    if not usuario_id:
        return jsonify({"error": "User not logged in"}), 401

    title = data.get("title")
    sala_id = data.get("sala_id")
    data_inicio_iso = data.get("data_inicio")
    data_fim_iso = data.get("data_fim")
    allDay = data.get("allDay", False)

    try:
        if not allDay:
            data_inicio = parser.parse(data_inicio_iso).strftime("%Y-%m-%d %H:%M:%S")
            data_fim = parser.parse(data_fim_iso).strftime("%Y-%m-%d %H:%M:%S")
        else:
            data_inicio = (
                parser.parse(data_inicio_iso).date().strftime("%Y-%m-%d") + " 00:00:00"
            )
            data_fim = (
                parser.parse(data_fim_iso).date().strftime("%Y-%m-%d") + " 23:59:59"
            )

        add_evento(title, usuario_id, sala_id, data_inicio, data_fim, allDay)
        app.logger.info("Evento adicionado com sucesso.")  # Log de sucesso
        return jsonify({"message": "Evento adicionado com sucesso!"}), 201
    except ValueError as e:
        app.logger.error(f"Conflito de horário: {str(e)}", exc_info=True)
        return jsonify({"error": str(e)}), 409
    except Exception as e:
        app.logger.error("Erro ao adicionar evento: " + str(e), exc_info=True)
        return jsonify({"error": "Não foi possível adicionar o evento."}), 500


@app.route("/listar_eventos")
def listar_eventos_route():
    sala_id = request.args.get("sala_id")
    eventos = listar_eventos(sala_id) if sala_id else []
    print(eventos)
    return jsonify(eventos)


@app.route("/buscar_evento/<int:evento_id>")
def buscar_evento_route(evento_id):
    evento = buscar_evento(evento_id)
    if evento:
        return jsonify(evento)
    else:
        return jsonify({"error": "Evento não encontrado"}), 404


@app.route("/atualizar_evento/<int:evento_id>", methods=["POST", "PUT"])
def atualizar_evento_route(evento_id):
    data = request.get_json()
    if not data:
        return jsonify({"error": "Dados não fornecidos"}), 400

    title = data.get("title")
    data_inicio_iso = data.get("data_inicio")
    data_fim_iso = data.get("data_fim")
    allDay = data.get("allDay", False)

    try:
        if not allDay:
            data_inicio = parser.parse(data_inicio_iso).strftime("%Y-%m-%d %H:%M:%S")
            data_fim = parser.parse(data_fim_iso).strftime("%Y-%m-%d %H:%M:%S")
        else:
            data_inicio = (
                parser.parse(data_inicio_iso).date().strftime("%Y-%m-%d") + " 00:00:00"
            )
            data_fim = (
                parser.parse(data_fim_iso).date().strftime("%Y-%m-%d") + " 23:59:59"
            )

        atualizar_evento(evento_id, title, data_inicio, data_fim, allDay)
        return jsonify({"message": "Evento atualizado com sucesso!"})
    except Exception as e:
        return (
            jsonify(
                {"error": "Não foi possível atualizar o evento", "details": str(e)}
            ),
            500,
        )


@app.route("/excluir_evento/<int:evento_id>", methods=["DELETE"])
def excluir_evento_route(evento_id):
    if "user_id" not in session:
        flash("Por favor, faça o login para continuar.")
        return redirect(url_for("index")), 401

    try:
        excluir_evento(evento_id)
        return jsonify({"message": "Evento excluído com sucesso!"})
    except Exception as e:
        return jsonify({"error": "Não foi possível excluir o evento"}), 500


@app.route("/cadastro-corretor")
def cadastro_corretor():
    return render_template("cadastro_corretor.html", active_page="cadastro-corretor")


@app.errorhandler(404)
def page_not_found(e):
    return render_template("error_page.html", error="Página não encontrada"), 404


@app.errorhandler(500)
def internal_server_error(e):
    return render_template("error_page.html", error="Erro interno do servidor"), 500


@app.route("/informativos")
def informativos():
    page = request.args.get("page", 1, type=int)
    data = listar_informativos(7, (page - 1) * 7)
    return render_template("homepage.html", posts=data)


@app.route("/informativo/<int:id>")
def detalhe_informativo(id):
    post = buscar_informativo(id)
    if post:
        return render_template("detalhes_informativo.html", post=post)
    else:
        return "Informativo não encontrado", 404


@app.route("/informativo/novo", methods=["GET", "POST"])
def novo_informativo():
    if request.method == "POST":
        titulo = request.form["titulo"]
        conteudo = request.form["conteudo"]
        imagem = request.files.get("imagem")

        if imagem:
            temp_dir = tempfile.mkdtemp()
            file_path = os.path.join(temp_dir, imagem.filename)
            imagem.save(file_path)
            try:
                url_imagem = upload_image_to_github(file_path, 'Adicionando nova imagem')
            finally:
                os.remove(file_path)
                os.rmdir(temp_dir)

            if url_imagem:
                add_informativo(titulo, conteudo, url_imagem, session.get("user_id"))
                flash("Informativo publicado com sucesso!", "success")
                return redirect(url_for("home"))
            else:
                flash("Erro ao enviar imagem para o GitHub", "danger")
                return redirect(url_for("novo_informativo"))
        else:
            add_informativo(titulo, conteudo, None, session.get("user_id"))
            flash("Informativo publicado com sucesso sem imagem!", "success")
            return redirect(url_for("home"))

    return render_template("criar_informativo.html")


@app.route("/excluir_informativo/<int:id>", methods=["DELETE"])
def excluir_informativo(id):
    if "user_id" not in session:
        flash("Por favor, faça o login para continuar.")
        return redirect(url_for("index"))

    try:
        if deletar_informativo(id):
            return jsonify({"message": "Informativo excluído com sucesso!"}), 200
        else:
            return jsonify({"error": "Informativo não encontrado"}), 404
    except Exception as e:
        return jsonify({"error": "Não foi possível excluir o informativo"}), 500


serializer = URLSafeTimedSerializer(app.secret_key)


def send_reset_email(user_email):
    token = serializer.dumps(user_email, salt=app.config["MAIL_SALT"])
    msg = Message(
        "Redefinição de Senha",
        sender=os.getenv("EMAIL_REMETENTE"),
        recipients=[user_email],
    )
    msg.body = f"""Para redefinir sua senha, visite o seguinte link:
{url_for('reset_token', token=token, _external=True)}

Se você não solicitou esta mudança, ignore este e-mail.
"""
    mail.send(msg)


@app.route("/forgot-password", methods=["GET", "POST"])
def forgot_password():
    if request.method == "POST":
        email = request.form["email"]
        send_reset_email(email)
        flash("Um e-mail com instruções para redefinir sua senha foi enviado.", "info")
        return redirect(url_for("login"))
    return render_template("forgot_password.html")


@app.route("/reset-password/<token>", methods=["GET", "POST"])
def reset_token(token):
    try:
        email = serializer.loads(
            token, salt=app.config["MAIL_SALT"], max_age=3600
        )  # Token válido por 1 hora
    except:
        flash("O link para redefinição de senha é inválido ou expirou.", "warning")
        return redirect(url_for("forgot_password"))

    if request.method == "POST":
        new_password = request.form["new-password"]
        confirm_password = request.form["confirm-password"]

        if new_password != confirm_password:
            flash("As senhas não coincidem.", "warning")
            return redirect(url_for("reset_token", token=token))

        nova_senha_hash = generate_password_hash(new_password, method="pbkdf2:sha256")

        try:
            user = consulta_email(email)
            if not user:
                flash("Usuário não encontrado.", "danger")
                return redirect(url_for("forgot_password"))

            user_id = user[0][0]
            atualizar_senha(user_id, nova_senha_hash)
            flash("Sua senha foi atualizada com sucesso!", "success")
            return redirect(url_for("login"))
        except Exception as e:
            print(e)  # Para debugging
            flash(
                "Ocorreu um erro ao atualizar a senha. Por favor, tente novamente.",
                "danger",
            )
            return redirect(url_for("reset_token", token=token))

    return render_template("reset_token.html", token=token)


@app.route("/franqueado")
def franqueado():
    return render_template("franqueado.html", active_page="franqueado")


@app.route("/rh")
def rh():
    return render_template("rh.html", active_page="rh")


@app.route("/faq")
def faq():
    return render_template("faq.html", active_page="rh")


@app.route("/manual")
def manual():
    return render_template("manual_colab.html", active_page="rh")


@app.route("/pdf/<filename>")
def serve_pdf(filename):
    return send_from_directory(os.path.join(app.root_path, "static/pdf"), filename)


@app.route("/ranking-comparativo-anual")
def ranking_comparativo_anual():
    if "user_id" in session:
        return render_template("ranking-comp-anual.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


@app.route("/gerenciais")
def gerenciais():
    if "user_id" in session:
        return render_template("gerenciais.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))

@app.route("/ases")
def ases():
    if "user_id" in session:
        return render_template("ases.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


EXCLUDED_USERS = [
    "BRAZIL HEALTH CONSULTORIA DE BENEFICIOS E CORRETORA DE SEGUROS LTDA ME",
    "ERICK DEGOMAR",
    "CELSO RICARDO LORENÇATO",
    "SYLMARA CAVALHEIRO ANDRE (ASCHE BRH)",
    "LUCAS AMAZONAS - GRUPO FUTURO",
    "ASCHE BRH SANTOS - COMERCIAL",
    "SUPERVISOR TEMPORARIO",
    "MARCELLO IGNACIO PUERTA (ASCHE)",
    "PATRICIA FERREIRA DE ARAUJO (ASCHE SANTOS)",
    "ANDRESSA DE SOUZA BORGES - ME (ASCHE SANTOS)"
]


EXCLUDED_UNIDADE = [
    "OUTRO",
]


ASSISTENTE_MAP = {
    "LEANDRO DE LIMA GOMES": "LEANDRO DE LIMA GOMES (ASSESSORIA)",
}


# Variáveis globais para datas
dt_inicio_global = None
dt_final_global = None

def carrega_datas_globais():
    global dt_inicio_global
    global dt_final_global

    dados = get_datas_globais()
    logging.info(f"Datas globais: {dados}")
    if dados and "data_inicio" in dados and "data_fim" in dados:
        data_inicio = dados["data_inicio"]
        data_fim = dados["data_fim"]

        dt_inicio_global = data_inicio.strftime('%m/%d/%Y')
        dt_final_global = data_fim.strftime('%m/%d/%Y')

        logging.info(f"Datas globais carregadas: {dt_inicio_global} - {dt_final_global}")
    else:
        dt_inicio_global = None
        dt_final_global = None


@app.route("/api/set_dates", methods=["POST"])
def set_dates():
    global dt_inicio_global, dt_final_global
    data = request.json
    dt_inicio_global = data.get("dt_inicio")
    dt_final_global = data.get("dt_final")

    if not dt_inicio_global or not dt_final_global:
        return jsonify({"error": "dt_inicio and dt_final are required"}), 400

    return jsonify({"message": "Dates updated successfully"}), 200


# Ranking Assistente
@app.route("/ranking")
def ranking():
    if "user_id" in session:
        return render_template("ranking.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


@app.route("/api/ranking")
def get_ranking():
    mes_ano = request.args.get("mesAno")
    if mes_ano:
        data = consulta_ranking(mes_ano)
        data = [item for item in data if item["assistente"] not in EXCLUDED_USERS]
    else:
        data = []
    return jsonify(data)


@app.route("/api/meses_anos")
def get_meses_anos():
    meses_anos = consulta_meses_anos()
    return jsonify(meses_anos)


def fetch_and_update_data(dt_inicio=None, dt_final=None):
    if dt_inicio is None or dt_final is None:
        dt_inicio = dt_inicio_global
        dt_final = dt_final_global

    filtered_data = fetch_api_data_and_extract_specific_info(dt_inicio, dt_final)
    if filtered_data:
        update_or_insert_data(filtered_data)


def fetch_api_data_and_extract_specific_info(dt_inicio, dt_final):
    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    params = {
        "DtInicio": dt_inicio,
        "DtFinal": dt_final,
        "ProducaoDataTipo": "Producao",
        "ProducaoMultinota": "Todas",
        "SumarioGroupBy": "Assistente",
        "SumarioOptions": [
            "Producao",
            "Empresarial",
        ],
        "SumarioStatus": [
            "PreCadastro",
            "Implantada",
            "AguardandoImplantacao",
            "AcompanharImplantacao",
            "AguardandoRegularizacao",
            "Inadimplente",
            "AguardandoOperadora",
        ],
        "Pagina": 1,
        "RegistrosPorPagina": 100,
    }
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }

    try:
        response = requests.get(url, headers=headers, params=params)

        if response.status_code == 200:
            full_data = response.json()

            filtered_data = [
                {
                    "assistente": ASSISTENTE_MAP.get(item.get("assistente"), item.get("assistente")),
                    "vlTotal": item.get("vlTotal"),
                    "vlContrato": item.get("vlContrato"),
                    "vlVida": item.get("vlVida"),
                    "vlReceber": item.get("vlReceber"),
                    "vlPagar": item.get("vlPagar"),
                    "vlLucro": item.get("vlLucro"),
                    "vlLucroPercentual": item.get("vlLucroPercentual"),
                    "vlLucroTotalPercentual": item.get("vlLucroTotalPercentual"),
                    "corretorCount": item.get("corretorCount"),
                    "contratoCount": item.get("contratoCount"),
                    "vidaCount": item.get("vidaCount"),
                    "contratoPorCorretor": item.get("contratoPorCorretor"),
                    "mesAno": f"{dt_inicio.split('/')[2]}-{dt_inicio.split('/')[0]}-01",  # Ajustar para formato DATE
                }
                for item in full_data["result"]["collection"]
            ]

            filtered_data_sorted = sorted(
                filtered_data, key=lambda x: x["vlTotal"], reverse=True
            )

            return filtered_data_sorted
        elif response.status_code == 204:
            pass  # No content returned from API.
        else:
            pass  # Error in API request.
    except Exception as e:
        pass  # Error occurred while making the API request.

    return []


@app.route("/api/update_data", methods=["POST"])
def update_data():
    data = request.json
    dt_inicio = data.get("dt_inicio")
    dt_final = data.get("dt_final")

    if not dt_inicio or not dt_final:
        return jsonify({"error": "dt_inicio and dt_final are required"}), 400

    fetch_and_update_data(dt_inicio, dt_final)
    return jsonify({"message": "Data updated successfully"}), 200


# Ranking Assistente - Individual
@app.route("/api/ranking-individual")
def get_ranking_individual():
    # Obter o ID do usuário da sessão
    user_id = session.get("user_id")
    if not user_id:
        return jsonify({"error": "Usuário não está logado ou ID de usuário não encontrado na sessão."}), 401

    # Obter o nome do assistente associado ao usuário
    user_data = consulta_geral(user_id)
    if not user_data:
        return jsonify({"error": "Dados do usuário não encontrados."}), 404

    assistente_nome = str(user_data[11])
    logging.info(f"Assistente: {assistente_nome}, User ID: {user_id}")

    # Obter o parâmetro mesAno da URL
    mes_ano = request.args.get("mesAno")
    print(f"MesAno: {mes_ano}")
    if mes_ano:
        data = consulta_ranking_individual(mes_ano, assistente_nome)
        print(f"Dados retornados: {data}")
    else:
        data = []
        print("MesAno não fornecido.")

    return jsonify(data)


@app.route("/ranking-individual")
def ranking_individual():
    return render_template("ranking-individual.html", active_page="relatorio-gerenciais")


# Ranking Assistente - Individual Empresárial
@app.route("/api/ranking-empresarial-individual")
def get_ranking_empresarial_individual():
    # Obter o ID do usuário da sessão
    user_id = session.get("user_id")
    if not user_id:
        return jsonify({"error": "Usuário não está logado ou ID de usuário não encontrado na sessão."}), 401

    # Obter o nome do assistente associado ao usuário
    user_data = consulta_geral(user_id)
    if not user_data:
        return jsonify({"error": "Dados do usuário não encontrados."}), 404

    assistente_nome = str(user_data[11])
    logging.info(f"Assistente: {assistente_nome}, User ID: {user_id}")

    # Obter o parâmetro mesAno da URL
    mes_ano = request.args.get("mesAno")
    if mes_ano:
        data = consulta_empresarial_individual(mes_ano, assistente_nome)
    else:
        data = []
    return jsonify(data)


@app.route("/ranking-individual-empresarial")
def ranking_individual_empresarial():
    return render_template("ranking-individual-empresarial.html", active_page="relatorio-gerenciais")


# Ranking Comparativo Mensal - Individual
@app.route("/api/consulta_comp_mes_individual")
def consulta_comp_mes_individual():
    # Obter o ID do usuário da sessão
    user_id = session.get("user_id")
    if not user_id:
        return jsonify({"error": "Usuário não está logado ou ID de usuário não encontrado na sessão."}), 401

    # Obter o nome do assistente associado ao usuário
    user_data = consulta_geral(user_id)
    if not user_data:
        return jsonify({"error": "Dados do usuário não encontrados."}), 404

    assistente_nome = str(user_data[11])
    logging.info(f"Assistente: {assistente_nome}, User ID: {user_id}")

    mes_ano_anterior = request.args.get('mesAnoAnterior')
    mes_ano_atual = request.args.get('mesAnoAtual')

    if mes_ano_anterior and mes_ano_atual:
        resultado = consulta_comparativa_individual(mes_ano_anterior, mes_ano_atual, assistente_nome)
    else:
        resultado = []

    return jsonify(resultado)


@app.route("/ranking-comp-mes-individual")
def ranking_comparativo_individual():
    return render_template("ranking-comp-mes-individual.html", active_page="relatorio-gerenciais")


@app.route("/ranking-comp-anual-individual")
def ranking_comparativo_anual_individual():
    return render_template("ranking-comp-anual-individual.html", active_page="relatorio-gerenciais")


# Função para gerar intervalos de datas de 90 dias
def generate_date_ranges(start_date, end_date, delta_days=90):
    current_date = start_date
    while current_date < end_date:
        yield current_date, min(current_date + timedelta(days=delta_days - 1), end_date)
        current_date += timedelta(days=delta_days)


# Função para agregar dados
def aggregate_data(data, aggregated_data):
    for item in data:
        assistente = item["assistente"]
        if assistente in aggregated_data:
            aggregated_data[assistente]["vlTotal"] += item["vlTotal"]
            aggregated_data[assistente]["vlContrato"] += item["vlContrato"]
            aggregated_data[assistente]["vlVida"] += item["vlVida"]
            aggregated_data[assistente]["vlReceber"] += item["vlReceber"]
            aggregated_data[assistente]["vlPagar"] += item["vlPagar"]
            aggregated_data[assistente]["vlLucro"] += item["vlLucro"]
            aggregated_data[assistente]["corretorCount"] += item["corretorCount"]
            aggregated_data[assistente]["contratoCount"] += item["contratoCount"]
            aggregated_data[assistente]["vidaCount"] += item["vidaCount"]
        else:
            aggregated_data[assistente] = item.copy()  # Usa copy para evitar mutação do item original


@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=10), retry=retry_if_exception_type((requests.ConnectionError, requests.Timeout)))
def fetch_data_with_retry(url, headers, params):
    response = requests.get(url, headers=headers, params=params)
    response.raise_for_status()  # Levanta uma exceção para códigos de status HTTP de erro

    # Verificar se a resposta é JSON válida e não vazia
    try:
        data = response.json()
        if not data:
            raise ValueError("Empty JSON response")
        return data
    except ValueError as e:
        logging.error(f"Invalid JSON response: {e}")
        raise


# Função principal para buscar e agregar dados de 90 em 90 dias
def fetch_api_ranking_anual():
    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }
    aggregated_data = {}

    import datetime

    start_date = datetime.datetime(2025, 1, 2)
    end_date = datetime.datetime(2025, 12, 31)

    # Trave a data final na data atual se ela for maior que hoje
    today = datetime.datetime.now()
    if end_date > today:
        end_date = today

    for start, end in generate_date_ranges(start_date, end_date):
        params = {
            "DtInicio": start.strftime("%m/%d/%Y"),
            "DtFinal": end.strftime("%m/%d/%Y"),
            "ProducaoDataTipo": "Producao",
            "ProducaoMultinota": "Todas",
            "SumarioGroupBy": "Assistente",
            "SumarioOptions": ["Producao", "Empresarial"],
            "SumarioStatus": [
                "PreCadastro",
                "Implantada",
                "AguardandoImplantacao",
                "AcompanharImplantacao",
                "AguardandoRegularizacao",
                "Inadimplente",
                "AguardandoOperadora",
            ],
            "Pagina": 1,
            "RegistrosPorPagina": 100,
        }
        try:
            response = fetch_data_with_retry(url, headers, params)
            data = response.get("result", {}).get("collection", [])
            if not data:
                logging.info(f"No data returned for range {start} to {end}.")
                continue  # Continua para o próximo intervalo
            aggregate_data(data, aggregated_data)
            logging.info(f"Successfully fetched and aggregated data for range {start} to {end}.")
        except requests.HTTPError as http_err:
            logging.error(f"Failed to fetch data for range {start} to {end}: {http_err}")
        except Exception as e:
            logging.error(f"An error occurred: {e}")

    # Converte os dados agregados em uma lista
    final_data = [
        {
            "assistente": item["assistente"],
            "vlTotal": item["vlTotal"],
            "vlContrato": item["vlContrato"],
            "vlVida": item["vlVida"],
            "vlReceber": item["vlReceber"],
            "vlPagar": item["vlPagar"],
            "vlLucro": item["vlLucro"],
            "vlLucroPercentual": item.get("vlLucroPercentual", None),
            "vlLucroTotalPercentual": item.get("vlLucroTotalPercentual", None),
            "corretorCount": item["corretorCount"],
            "contratoCount": item["contratoCount"],
            "vidaCount": item["vidaCount"],
            "contratoPorCorretor": item.get("contratoPorCorretor", None),
        }
        for item in aggregated_data.values()
    ]

    update_sumario_assistente_anual(final_data)


# Ranking ASES por Assistente
def fetch_api_ranking_ases():

    import datetime

    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }
    aggregated_data = {}

    start_date = datetime.datetime(2023, 12, 20)
    end_date = datetime.datetime(2024, 11, 20)

    # Trave a data final na data atual se ela for maior que hoje
    today = datetime.datetime.now()
    if end_date > today:
        end_date = today

    for start, end in generate_date_ranges(start_date, end_date):
        params = {
            "DtInicio": start.strftime("%m/%d/%Y"),
            "DtFinal": end.strftime("%m/%d/%Y"),
            "ProducaoDataTipo": "Producao",
            "ProducaoMultinota": "Todas",
            "SumarioGroupBy": "Assistente",
            "SumarioOptions": ["Producao", "Empresarial"],
            "SumarioStatus": [
                "PreCadastro",
                "Implantada",
                "AguardandoImplantacao",
                "AcompanharImplantacao",
                "AguardandoRegularizacao",
                "Inadimplente",
                "AguardandoOperadora",
            ],
            "Pagina": 1,
            "RegistrosPorPagina": 100,
        }
        try:
            response = fetch_data_with_retry(url, headers, params)
            data = response.get("result", {}).get("collection", [])
            if not data:
                logging.info(f"No data returned for range {start} to {end}.")
                continue  # Continua para o próximo intervalo
            aggregate_data(data, aggregated_data)
            logging.info(f"Successfully fetched and aggregated data for range {start} to {end}.")
        except requests.HTTPError as http_err:
            logging.error(f"Failed to fetch data for range {start} to {end}: {http_err}")
        except Exception as e:
            logging.error(f"An error occurred: {e}")

    # Converte os dados agregados em uma lista
    final_data = [
        {
            "assistente": item["assistente"],
            "vlTotal": item["vlTotal"],
            "vlContrato": item["vlContrato"],
            "vlVida": item["vlVida"],
            "vlReceber": item["vlReceber"],
            "vlPagar": item["vlPagar"],
            "vlLucro": item["vlLucro"],
            "vlLucroPercentual": item.get("vlLucroPercentual", None),
            "vlLucroTotalPercentual": item.get("vlLucroTotalPercentual", None),
            "corretorCount": item["corretorCount"],
            "contratoCount": item["contratoCount"],
            "vidaCount": item["vidaCount"],
            "contratoPorCorretor": item.get("contratoPorCorretor", None),
        }
        for item in aggregated_data.values()
    ]

    update_sumario_assistente_ases(final_data)


@app.route("/api/ranking_ases")
def get_ranking_ases():
    data = consulta_ranking_ases()
    data = [item for item in data if item["assistente"] not in EXCLUDED_USERS]
    return jsonify(data)


@app.route("/ranking_ases")
def ranking_ases():
    if "user_id" in session:
        return render_template("ranking-ases-assistente.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


# Ranking ASES por Corretor
def fetch_api_ranking_ases_corretor():
    import datetime

    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }

    start_date = datetime.datetime(2023, 12, 20)
    end_date = datetime.datetime(2024, 11, 20)

    today = datetime.datetime.now()
    if end_date > today:
        end_date = today

    all_data = []
    unique_records = set()

    for start, end in generate_date_ranges(start_date, end_date):
        params = {
            "DtInicio": start.strftime("%m/%d/%Y"),
            "DtFinal": end.strftime("%m/%d/%Y"),
            "ProducaoDataTipo": "Producao",
            "ProducaoMultinota": "Todas",
            "SumarioGroupBy": ["Assistente", "Corretor"],
            "SumarioOptions": ["Producao", "Empresarial"],
            "SumarioStatus": [
                "PreCadastro",
                "Implantada",
                "AguardandoImplantacao",
                "AcompanharImplantacao",
                "AguardandoRegularizacao",
                "Inadimplente",
                "AguardandoOperadora",
            ],
            "Pagina": 1,
            "RegistrosPorPagina": 100,
        }

        try:
            response = fetch_data_with_retry(url, headers, params)
            pagina_info = response.get("result", {}).get("paginaInfo", {})
            logging.info(pagina_info)
            total_paginas = pagina_info.get("totalPaginas", 1)
            total_registros = pagina_info.get("totalRegistros", 0)

            for pagina in range(1, total_paginas + 1):
                params["Pagina"] = pagina
                retry_attempts = 0
                max_retries = 3
                success = False

                while retry_attempts < max_retries and not success:
                    try:
                        response = fetch_data_with_retry(url, headers, params)
                        data = response.get("result", {}).get("collection", [])

                        for item in data:
                            item_str = json.dumps(item, sort_keys=True)
                            if item_str not in unique_records:
                                unique_records.add(item_str)
                                all_data.append(item)

                        logging.info(f"Successfully fetched data for range {start} to {end}, page {pagina}.")
                        time.sleep(10)
                        success = True
                    except requests.HTTPError as http_err:
                        logging.error(f"Failed to fetch data for range {start} to {end}, page {pagina}: {http_err}")
                        retry_attempts += 1
                        if retry_attempts < max_retries:
                            logging.info(f"Retrying page {pagina} (attempt {retry_attempts + 1}/{max_retries})...")
                            time.sleep(5)
                    except Exception as e:
                        logging.error(f"An error occurred: {e}")
                        retry_attempts += 1
                        if retry_attempts < max_retries:
                            logging.info(f"Retrying page {pagina} (attempt {retry_attempts + 1}/{max_retries})...")
                            time.sleep(5)

            if start == datetime.datetime(2024, 3, 19) and end == datetime.datetime(2024, 6, 16):
                logging.info(f"Specific log entry for range {start} to {end}, page {pagina}.")

        except requests.HTTPError as http_err:
            logging.error(f"Failed to fetch data for range {start} to {end}: {http_err}")
        except Exception as e:
            logging.error(f"An error occurred: {e}")

    final_data = [
        {
            "assistente": item["assistente"],
            "corretor": item["corretor"],
            "vlTotal": item.get("vlTotal"),
            "vlContrato": item.get("vlContrato"),
            "vlVida": item.get("vlVida"),
            "vlReceber": item.get("vlReceber"),
            "vlPagar": item.get("vlPagar"),
            "vlLucro": item.get("vlLucro"),
            "vlLucroPercentual": item.get("vlLucroPercentual"),
            "vlLucroTotalPercentual": item.get("vlLucroTotalPercentual"),
            "corretorCount": item.get("corretorCount"),
            "contratoCount": item.get("contratoCount"),
            "vidaCount": item.get("vidaCount"),
            "contratoPorCorretor": item.get("contratoPorCorretor"),
        }
        for item in all_data
    ]

    update_sumario_corretor_ases(final_data)


@app.route("/api/ranking_ases_corretores")
def get_ranking_ases_corretor():
    data = consulta_ranking_ases_corretor()
    data = [item for item in data if item["assistente"] not in EXCLUDED_USERS]
    return jsonify(data)


@app.route("/ranking_ases_corretores")
def ranking_ases_corretor():
    if "user_id" in session:
        return render_template("ranking-ases-cor.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


# Ranking Empresarial
@app.route("/ranking-empresarial")
def ranking_empresarial():
    if "user_id" in session:
        return render_template("ranking-empresarial.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


@app.route("/api/ranking_empresarial")
def get_ranking_empresarial():
    mes_ano = request.args.get("mesAno")
    if mes_ano:
        data = consulta_empresarial(mes_ano)
        data = [item for item in data if item["assistente"] not in EXCLUDED_USERS]
    else:
        data = []
    return jsonify(data)


@app.route("/api/meses_anos_empresarial")
def get_meses_anos_empresarial():
    meses_anos = consulta_mesesanos_empresarial()
    return jsonify(meses_anos)

def fetch_and_update_data_empresarial(dt_inicio=None, dt_final=None):
    if dt_inicio is None or dt_final is None:
        dt_inicio = dt_inicio_global
        dt_final = dt_final_global

    filtered_data = fetch_api_data_and_extract_specific_info_empresarial(dt_inicio, dt_final)
    if filtered_data:
        update_or_insert_empresarial(filtered_data)


def fetch_api_data_and_extract_specific_info_empresarial(dt_inicio=None, dt_final=None):
    if dt_inicio is None or dt_final is None:
        dt_inicio = dt_inicio_global
        dt_final = dt_final_global

    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    params = {
        "DtInicio": dt_inicio,
        "DtFinal": dt_final,
        "ProducaoDataTipo": "Producao",
        "ProducaoMultinota": "Todas",
        "SumarioGroupBy": "Assistente",
        "SumarioOptions": [
            "Empresarial",
        ],
        "SumarioStatus": [
            "PreCadastro",
            "Implantada",
            "AguardandoImplantacao",
            "AcompanharImplantacao",
            "AguardandoRegularizacao",
            "Inadimplente",
            "AguardandoOperadora",
        ],
        "Pagina": 1,
        "RegistrosPorPagina": 100,
    }
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }

    try:
        response = requests.get(url, headers=headers, params=params)

        if response.status_code == 200:
            full_data = response.json()

            filtered_data = [
                {
                    "assistente": ASSISTENTE_MAP.get(item.get("assistente"), item.get("assistente")),
                    "vlTotal": item.get("vlTotal"),
                    "vlContrato": item.get("vlContrato"),
                    "vlVida": item.get("vlVida"),
                    "vlReceber": item.get("vlReceber"),
                    "vlPagar": item.get("vlPagar"),
                    "vlLucro": item.get("vlLucro"),
                    "vlLucroPercentual": item.get("vlLucroPercentual"),
                    "vlLucroTotalPercentual": item.get("vlLucroTotalPercentual"),
                    "corretorCount": item.get("corretorCount"),
                    "contratoCount": item.get("contratoCount"),
                    "vidaCount": item.get("vidaCount"),
                    "contratoPorCorretor": item.get("contratoPorCorretor"),
                    "mesAno": f"{dt_inicio.split('/')[2]}-{dt_inicio.split('/')[0]}-01",  # Ajustar para formato DATE
                }
                for item in full_data["result"]["collection"]
            ]

            filtered_data_sorted = sorted(
                filtered_data, key=lambda x: x["vlTotal"], reverse=True
            )

            return filtered_data_sorted
        elif response.status_code == 204:
            pass  # No content returned from API.
        else:
            pass  # Error in API request.
    except Exception as e:
        pass  # Error occurred while making the API request.

    return []


@app.route("/api/update_data_empresarial", methods=["POST"])
def update_data_empresarial():
    data = request.json
    dt_inicio = data.get("dt_inicio")
    dt_final = data.get("dt_final")

    if not dt_inicio or not dt_final:
        return jsonify({"error": "dt_inicio and dt_final are required"}), 400

    filtered_data = fetch_api_data_and_extract_specific_info_empresarial(dt_inicio, dt_final)
    if filtered_data:
        update_or_insert_empresarial(filtered_data)

    return jsonify({"message": "Data updated successfully"}), 200


# Ranking Operadora
@app.route("/ranking-operadora")
def ranking_operadora():
    if "user_id" in session:
        return render_template("ranking-operadora.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


@app.route("/api/ranking_operadora")
def get_ranking_operadora():
    mes_ano = request.args.get("mesAno")
    if mes_ano:
        data = consulta_operadora(mes_ano)
    else:
        data = []
    return jsonify(data)


@app.route("/api/meses_anos_operadora")
def get_meses_anos_operadora():
    meses_anos = consulta_mesesanos_operadora()
    return jsonify(meses_anos)


def fetch_and_update_data_operadora(dt_inicio=None, dt_final=None):
    if dt_inicio is None or dt_final is None:
        dt_inicio = dt_inicio_global
        dt_final = dt_final_global

    filtered_data = fetch_api_data_and_extract_specific_info_operadora(dt_inicio, dt_final)
    if filtered_data:
        update_or_insert_operadora(filtered_data)


def fetch_api_data_and_extract_specific_info_operadora(dt_inicio=None, dt_final=None):
    # Se dt_inicio ou dt_final não forem fornecidos, use os valores globais
    if dt_inicio is None or dt_final is None:
        dt_inicio = dt_inicio_global
        dt_final = dt_final_global

    # Carrega o arquivo operadoras.json como JSON
    operadoras_file_path = os.path.join('static', 'data', 'operadoras.json')

    try:
        with open(operadoras_file_path, 'r', encoding='utf-8') as f:
            operadoras_data = json.load(f)
    except json.JSONDecodeError as e:
        logging.error(f"Erro ao carregar o JSON: {e}")
        return []

    if isinstance(operadoras_data, dict) and "Sheet1" in operadoras_data:
        operadoras_dict = {op['idOperadora']: op['operadoraNome'] for op in operadoras_data['Sheet1']}
    else:
        logging.error("Formato do arquivo 'operadoras.json' está incorreto ou chave 'Sheet1' não encontrada.")
        return []

    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    params = {
        "DtInicio": dt_inicio,
        "DtFinal": dt_final,
        "ProducaoDataTipo": "Producao",
        "ProducaoMultinota": "Todas",
        "SumarioGroupBy": "Operadora",
        "SumarioOptions": [
            "Producao",
            "Empresarial",
        ],
        "SumarioStatus": [
            "PreCadastro",
            "Implantada",
            "AguardandoImplantacao",
            "AcompanharImplantacao",
            "AguardandoRegularizacao",
            "Inadimplente",
            "AguardandoOperadora",
        ],
        "Pagina": 1,
        "RegistrosPorPagina": 50,
    }
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        logging.info(f"Request URL: {response.url}")

        if response.status_code == 200:
            full_data = response.json()
            logging.info(f"Dados retornados: {len(full_data['result']['collection'])} itens.")

            filtered_data = [
                {
                    "operadoraNome": operadoras_dict.get(item.get("idOperadora"), "Desconhecida"),
                    "vlTotal": item.get("vlTotal"),
                    "vlContrato": item.get("vlContrato"),
                    "vlVida": item.get("vlVida"),
                    "vlReceber": item.get("vlReceber"),
                    "vlPagar": item.get("vlPagar"),
                    "vlLucro": item.get("vlLucro"),
                    "vlLucroPercentual": item.get("vlLucroPercentual"),
                    "vlLucroTotalPercentual": item.get("vlLucroTotalPercentual"),
                    "corretorCount": item.get("corretorCount"),
                    "contratoCount": item.get("contratoCount"),
                    "vidaCount": item.get("vidaCount"),
                    "contratoPorCorretor": item.get("contratoPorCorretor"),
                    "mesAno": f"{dt_inicio.split('/')[2]}-{dt_inicio.split('/')[0]}-01",  # Ajustar para formato DATE
                }
                for item in full_data["result"]["collection"]
            ]

            filtered_data_sorted = sorted(
                filtered_data, key=lambda x: x["vlTotal"], reverse=True
            )

            return filtered_data_sorted
        elif response.status_code == 204:
            logging.info("Nenhum conteúdo retornado pela API.")
        else:
            logging.error(f"Erro na resposta da API: {response.status_code}")

    except Exception as e:
        logging.error(f"Erro ocorrido ao fazer a requisição à API: {e}")

    return []


@app.route("/api/update_data_operadora", methods=["POST"])
def update_data_operadora():
    data = request.json
    dt_inicio = data.get("dt_inicio")
    dt_final = data.get("dt_final")

    if not dt_inicio or not dt_final:
        return jsonify({"error": "dt_inicio and dt_final are required"}), 400

    filtered_data = fetch_api_data_and_extract_specific_info_operadora(dt_inicio, dt_final)
    if filtered_data:
        update_or_insert_operadora(filtered_data)

    return jsonify({"message": "Data updated successfully"}), 200


# Ranking Unidades
@app.route("/ranking-unidades")
@login_required
def ranking_unidades():
    return render_template("ranking-unidades.html", active_page="relatorio-gerenciais")


@app.route("/api/ranking_unidades")
@login_required
def get_ranking_unidades():
    mes_ano = request.args.get("mesAno")

    user_id = session.get("user_id")
    tipo_usuario = session.get("user_type")

    dados_usuario = consulta_geral(user_id)
    unidade_id = dados_usuario[8]

    try:
        if tipo_usuario in [1, 7]:
            data = consulta_unidades(mes_ano)
            data = [item for item in data if item["assistente_grupo"] not in EXCLUDED_UNIDADE]
        elif tipo_usuario in [2]:
            data = consulta_unidades_usuario(mes_ano, unidade_id)
            data = [item for item in data if item["assistente_grupo"] not in EXCLUDED_UNIDADE]
        else:
            data = []
        return jsonify(data)
    except Exception as e:
        logging.error(f"Erro ao obter dados dos rankings das unidades: {e}")
        return jsonify({"error": "Erro ao obter dados dos rankings das unidades"}), 500


@app.route("/api/meses_anos_unidades")
def get_meses_anos_unidades():
    meses_anos = consulta_mesesanos_unidades()
    return jsonify(meses_anos)


# Ranking Comparativo Mensal
@app.route("/ranking-comparativo-mes")
def ranking_comparativo_mes():
    if "user_id" in session:
        return render_template("ranking-comp-mes.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


@app.route("/api/consultar_comparativo_mes")
def get_consultar_comparativo_mes():
    mes_ano_anterior = request.args.get('mesAnoAnterior')
    mes_ano_atual = request.args.get('mesAnoAtual')

    if mes_ano_anterior and mes_ano_atual:
        resultado = consulta_comparativa(mes_ano_anterior, mes_ano_atual)
        resultado = [item for item in resultado if item["assistente"] not in EXCLUDED_USERS]
    else:
        resultado = []

    return jsonify(resultado)


# Ranking Operadora x Assistente
@app.route("/ranking-op-x-ass")
def ranking_op_ass():
    if "user_id" in session:
        return render_template("ranking-op-x-ass.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


def fetch_operadoras_data(dt_inicio, dt_final):
    # Carrega o arquivo operadoras.json como JSON
    operadoras_file_path = os.path.join('static', 'data', 'operadoras.json')

    try:
        with open(operadoras_file_path, 'r', encoding='utf-8') as f:
            operadoras_data = json.load(f)
    except json.JSONDecodeError as e:
        logging.error(f"Erro ao carregar o JSON: {e}")
        return

    logging.info(f"Tipo de 'operadoras_data': {type(operadoras_data)}")
    logging.info(f"Conteúdo de 'operadoras_data': {operadoras_data}")

    if isinstance(operadoras_data, dict) and "Sheet1" in operadoras_data:
        operadoras_dict = {op['idOperadora']: op['operadoraNome'] for op in operadoras_data['Sheet1']}
    else:
        logging.error("Formato do arquivo 'operadoras.json' está incorreto ou chave 'Sheet1' não encontrada.")
        return

    top_operadoras = get_top_6_operadoras()
    logging.info(f"Top Operadoras: {top_operadoras}")

    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    params = {
        "DtInicio": dt_inicio,
        "DtFinal": dt_final,
        "ProducaoDataTipo": "Producao",
        "ProducaoMultinota": "Todas",
        "SumarioGroupBy": ["Assistente", "Operadora"],
        "SumarioOptions": ["Producao", "Empresarial"],
        "SumarioStatus": [
            "PreCadastro",
            "Implantada",
            "AguardandoImplantacao",
            "AcompanharImplantacao",
            "AguardandoRegularizacao",
            "Inadimplente",
            "AguardandoOperadora",
        ],
        "Pagina": 1,
        "RegistrosPorPagina": 100,
    }
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }

    all_data = []

    while True:
        try:
            response = requests.get(url, headers=headers, params=params)
            logging.info(f"Request URL: {response.url}")
            if response.status_code == 200:
                page_data = response.json()["result"]["collection"]
                if not page_data:
                    logging.info("Nenhum dado retornado. Finalizando coleta.")
                    break

                for item in page_data:
                    id_operadora = item.get("idOperadora")
                    item["operadoraNome"] = operadoras_dict.get(id_operadora, "Desconhecida")

                    if item.get("operadoraNome") in top_operadoras:
                        all_data.append(
                            {
                                "assistente": item.get("assistente"),
                                "operadoraNome": item.get("operadoraNome"),
                                "vlTotal": item.get("vlTotal"),
                            }
                        )
                logging.info(f"Página {params['Pagina']} coletada com {len(page_data)} itens.")
                params["Pagina"] += 1
            elif response.status_code == 204:
                logging.info("Nenhum conteúdo. Finalizando coleta.")
                break
            else:
                logging.error(f"Erro na resposta: {response.status_code}")
                break
        except Exception as err:
            logging.error(f"Erro ocorrido: {err}")
            break

    unique_data = {f"{item['assistente']}-{item['operadoraNome']}": item for item in all_data}.values()

    all_data_sorted = sorted(
        unique_data, key=lambda x: (x["operadoraNome"], -x["vlTotal"])
    )

    logging.info(f"Dados coletados e ordenados: {all_data_sorted}")

    # Atualiza a tabela no banco de dados
    update_sumario_op_ass(all_data_sorted)
    logging.info("Dados atualizados no banco de dados.")

    return all_data_sorted


@app.route("/api/ranking_op_ass")
def get_ranking_op_ass():
    data = consulta_ranking_op_ass()
    filtered_data = [item for item in data if item["assistente"] not in EXCLUDED_USERS]

    # Agrupar por operadora e somar valores totais para assistentes duplicados
    operadora_groups = {}
    for item in filtered_data:
        operadora = item["operadoranome"]
        assistente = item["assistente"]
        if operadora not in operadora_groups:
            operadora_groups[operadora] = {}

        # Se o assistente já existe, soma o valor
        if assistente in operadora_groups[operadora]:
            operadora_groups[operadora][assistente]["vltotal"] = str(
                float(operadora_groups[operadora][assistente]["vltotal"]) + float(item["vltotal"])
            )
        else:
            operadora_groups[operadora][assistente] = item

    # Converter o dicionário de assistentes em lista e ordenar
    for operadora in operadora_groups:
        assistentes_list = list(operadora_groups[operadora].values())
        assistentes_list.sort(key=lambda x: float(x["vltotal"]), reverse=True)
        operadora_groups[operadora] = assistentes_list[:10]

    # Calcular a soma total para cada operadora e ordenar operadoras pelo total geral
    operadora_totals = {
        operadora: sum(float(item["vltotal"]) for item in items)
        for operadora, items in operadora_groups.items()
    }
    sorted_operadoras = sorted(operadora_groups.keys(), key=lambda operadora: operadora_totals[operadora], reverse=True)

    # Ordenar o dicionário
    sorted_operadora_groups = {operadora: operadora_groups[operadora] for operadora in sorted_operadoras}

    return jsonify(sorted_operadora_groups)


def fetch_and_update_data_op_ass(dt_inicio=None, dt_final=None):
    if dt_inicio is None or dt_final is None:
        dt_inicio = dt_inicio_global
        dt_final = dt_final_global

    filtered_data = fetch_operadoras_data(dt_inicio, dt_final)
    if filtered_data:
        update_sumario_op_ass(filtered_data)


@app.route("/api/update_operadoras_data", methods=["POST"])
def update_operadoras_data():
    data = request.json
    dt_inicio = data.get("dt_inicio")
    dt_final = data.get("dt_final")

    if not dt_inicio or not dt_final:
        return jsonify({"error": "dt_inicio and dt_final are required"}), 400

    fetch_and_update_data_op_ass(dt_inicio, dt_final)
    return jsonify({"message": "Data updated successfully"}), 200


# Ranking Operadora x Corretor
@app.route("/ranking-op-x-cor")
def ranking_op_cor():
    if "user_id" in session:
        return render_template("ranking-op-x-cor.html", active_page="relatorio-gerenciais")
    else:
        return redirect(url_for("index"))


def get_top_6_operadoras():
    global dt_inicio_global
    mes_ano = f"{dt_inicio_global.split('/')[0]}/{dt_inicio_global.split('/')[2]}"
    operadoras_ranking = consulta_operadora_top6(mes_ano)
    return [op["operadora"] for op in operadoras_ranking]


def fetch_operadoras_cor_data(dt_inicio, dt_final):
    # Carrega o arquivo operadoras.json como JSON
    operadoras_file_path = os.path.join('static', 'data', 'operadoras.json')

    try:
        with open(operadoras_file_path, 'r', encoding='utf-8') as f:
            operadoras_data = json.load(f)
    except json.JSONDecodeError as e:
        logging.error(f"Erro ao carregar o JSON: {e}")
        return

    logging.info(f"Tipo de 'operadoras_data': {type(operadoras_data)}")
    logging.info(f"Conteúdo de 'operadoras_data': {operadoras_data}")

    if isinstance(operadoras_data, dict) and "Sheet1" in operadoras_data:
        operadoras_dict = {op['idOperadora']: op['operadoraNome'] for op in operadoras_data['Sheet1']}
    else:
        logging.error("Formato do arquivo 'operadoras.json' está incorreto ou chave 'Sheet1' não encontrada.")
        return

    top_operadoras = get_top_6_operadoras()
    logging.info(f"Top Operadoras: {top_operadoras}")

    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    params = {
        "DtInicio": dt_inicio,
        "DtFinal": dt_final,
        "ProducaoDataTipo": "Producao",
        "ProducaoMultinota": "Todas",
        "SumarioGroupBy": ["Assistente", "Corretor", "Operadora"],
        "SumarioOptions": ["Producao", "Empresarial"],
        "SumarioStatus": [
            "PreCadastro",
            "Implantada",
            "AguardandoImplantacao",
            "AcompanharImplantacao",
            "AguardandoRegularizacao",
            "Inadimplente",
            "AguardandoOperadora",
        ],
        "Pagina": 1,
        "RegistrosPorPagina": 100,
    }
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }

    all_data = []

    # Coleta os dados de todas as páginas
    while True:
        try:
            response = requests.get(url, headers=headers, params=params)
            logging.info(f"Request URL: {response.url}")
            if response.status_code == 200:
                page_data = response.json()["result"]["collection"]
                if not page_data:
                    logging.info("Nenhum dado retornado. Finalizando coleta.")
                    break

                # Filtra e adiciona os dados das operadoras específicas
                for item in page_data:
                    id_operadora = item.get("idOperadora")
                    item["operadoraNome"] = operadoras_dict.get(id_operadora, "Desconhecida")

                    if item.get("operadoraNome") in top_operadoras:
                        all_data.append(
                            {
                                "assistente": item.get("assistente"),
                                "corretor": item.get("corretor"),
                                "operadoraNome": item.get("operadoraNome"),
                                "vlTotal": item.get("vlTotal"),
                            }
                        )
                logging.info(f"Página {params['Pagina']} coletada com {len(page_data)} itens.")
                params["Pagina"] += 1
            elif response.status_code == 204:
                logging.info("Nenhum conteúdo. Finalizando coleta.")
                break
            elif response.status_code == 404:
                logging.error(f"Erro 404: URL não encontrada: {response.url} - Parâmetros: {params}")
                break
            else:
                logging.error(f"Erro na resposta: {response.status_code}")
                break
        except Exception as err:
            logging.error(f"Erro ocorrido: {err}")
            break

    # Ordena os dados coletados
    all_data_sorted = sorted(
        all_data, key=lambda x: (x["operadoraNome"], -x["vlTotal"])
    )

    # Atualiza a tabela no banco de dados
    update_sumario_op_cor(all_data_sorted)
    logging.info("Dados atualizados no banco de dados.")

    return all_data_sorted


@app.route("/api/ranking_op_cor")
def get_ranking_op_cor():
    data = consulta_ranking_op_cor()
    filtered_data = [item for item in data if item["assistente"] not in EXCLUDED_USERS]

    # Agrupar por operadora e corretor, somando os valores totais
    operadora_groups = {}
    for item in filtered_data:
        operadora = item["operadoranome"]
        corretor = item["corretor"]

        if operadora not in operadora_groups:
            operadora_groups[operadora] = {}

        # Se o corretor já existe para esta operadora, soma o valor
        if corretor in operadora_groups[operadora]:
            operadora_groups[operadora][corretor]["vltotal"] = str(
                float(operadora_groups[operadora][corretor]["vltotal"]) + float(item["vltotal"])
            )
        else:
            operadora_groups[operadora][corretor] = {
                "corretor": corretor,
                "assistente": item["assistente"],
                "vltotal": item["vltotal"],
                "operadoranome": operadora
            }

    # Converter o dicionário de corretores em lista e ordenar
    for operadora in operadora_groups:
        corretores_list = list(operadora_groups[operadora].values())
        corretores_list.sort(key=lambda x: float(x["vltotal"]), reverse=True)
        operadora_groups[operadora] = corretores_list[:10]  # Pegar apenas os top 10 corretores

    # Calcular a soma total para cada operadora e ordenar operadoras pelo total geral
    operadora_totals = {
        operadora: sum(float(item["vltotal"]) for item in items)
        for operadora, items in operadora_groups.items()
    }
    sorted_operadoras = sorted(operadora_groups.keys(), key=lambda operadora: operadora_totals[operadora], reverse=True)

    # Ordenar o dicionário
    sorted_operadora_groups = {operadora: operadora_groups[operadora] for operadora in sorted_operadoras}

    return jsonify(sorted_operadora_groups)


def fetch_and_update_data_op_cor(dt_inicio=None, dt_final=None):
    if dt_inicio is None or dt_final is None:
        dt_inicio = dt_inicio_global
        dt_final = dt_final_global

    filtered_data = fetch_operadoras_cor_data(dt_inicio, dt_final)
    if filtered_data:
        update_sumario_op_cor(filtered_data)


@app.route("/api/update_operadoras_corretor_data", methods=["POST"])
def update_operadoras_corretor_data():
    data = request.json
    dt_inicio = data.get("dt_inicio")
    dt_final = data.get("dt_final")

    if not dt_inicio or not dt_final:
        return jsonify({"error": "dt_inicio and dt_final are required"}), 400

    fetch_and_update_data_op_cor(dt_inicio, dt_final)
    return jsonify({"message": "Data updated successfully"}), 200


# Incluir o fetch para poder atualizar pelo CronTrigger
def fetch_and_update_all_data():
    carrega_datas_globais(),
    fetch_api_ranking_ases(),
    fetch_api_ranking_ases_corretor(),
    fetch_and_update_data(),
    fetch_and_update_data_empresarial(),
    fetch_and_update_data_operadora(),
    fetch_and_update_data_op_ass(),
    fetch_and_update_data_op_cor(),
    fetch_api_ranking_anual(),
    fetch_api_metas_vidas_assistentes(),


if env == 'prod':
    # Configurar o agendador de tarefas para executar a partir das 8h da manhã e a cada 2 horas
    scheduler = BackgroundScheduler()
    trigger = CronTrigger(hour="7-22/1")  # Executa a cada 1 horas das 07:00 às 22:00

    def job():
        try:
            fetch_and_update_all_data()
            update_last_execution_time('success')
        except Exception as e:
            logging.error(f"Erro na execução do job: {str(e)}")
            update_last_execution_time('error')

    scheduler.add_job(func=job, trigger=trigger)
    scheduler.start()
    # Assegure-se de encerrar o agendador quando o aplicativo for fechado
    atexit.register(lambda: scheduler.shutdown())
    logging.info("Scheduler started.")
else:
    logging.info("Executando em ambiente de desenvolvimento. Scheduler not started.")


def create_gauge_chart(value, max_value, assistente):
    max_value = float(max_value)

    if value < max_value:
        threshold_color = "red"
    else:
        threshold_color = "green"

    fig = go.Figure(go.Indicator(
        mode="gauge+number+delta",
        value=value,
        number={'prefix': "R$ ", 'valueformat': ",.2f"},
        delta={'reference': max_value, 'relative': True, 'position': "top", 'valueformat': ".2%"},
        gauge={
            'axis': {
                'range': [0, max_value],
                'tickmode': 'linear',
                'tick0': 0,
                'dtick': max_value / 5  # Ajuste o número de ticks conforme necessário
            },
            'bar': {'thickness': 0},  # Faz a barra invisível
            'steps': [
                {'range': [0, max_value * 0.2], 'color': "#FF7575"},
                {'range': [max_value * 0.2, max_value * 0.65], 'color': "#FF9B57"},
                {'range': [max_value * 0.65, max_value * 0.85], 'color': "#509FF6"},
                {'range': [max_value * 0.85, max_value], 'color': "#2552A3"}
            ],
            'threshold': {
                'line': {'color': threshold_color, 'width': 8},
                'thickness': 0.8,
                'value': value
            }
        },
        title={'text': assistente}
    ))

    return fig


# Definindo um layout inicial para evitar NoLayoutException
dash_app.layout = html.Div([
    html.H1("Loading...")
])


@app.route('/metas-individuais')
@login_required
def meta_individual():

    carrega_datas_globais()

    user_id = session.get("user_id")
    if not user_id:
        return "Usuário não está logado ou ID de usuário não encontrado na sessão.", 401

    # Conversão de string para datetime
    from datetime import datetime

    data = datetime.strptime(dt_inicio_global, "%m/%d/%Y")
    mesano = data.strftime("%Y-%m-01")
    ano = data.strftime("%Y")

    # Obtendo o nome do assistente e o id do canal usando a função consulta_geral
    user_data = consulta_geral(user_id)
    if user_data:
        assistente = user_data[11]  # Pega o campo assistente
        canal_id = user_data[12]    # Pega o campo canal_id
        logging.info(f"Assistente: {assistente}, Canal ID: {canal_id}")

        value = consulta_valores(assistente, mesano)
        value_anual = consulta_valores_anual(assistente, ano)

        # Certificando que value e value_anual são números
        try:
            value = float(value) if value is not None else 0
        except ValueError:
            value = 0  # Se não puder converter para número, define como 0

        try:
            value_anual = float(value_anual) if value_anual is not None else 0
        except ValueError:
            value_anual = 0  # Se não puder converter para número, define como 0

        max_value = consulta_meta(canal_id)  # Consulta a meta baseada no id do canal
        if max_value is None:
            print(f"Meta não encontrada para o canal_id: {canal_id}")
            max_value = 0
            max_value_anual = 0
            mensagem_mensal = html.Span("Meta não encontrada para o canal especificado.", className='mensagem-vermelha')
            mensagem_anual = html.Span("Meta não encontrada para o canal especificado.", className='mensagem-vermelha')
        else:
            max_value = float(max_value)
            max_value_anual = max_value * 12

            # Calcula quanto falta para atingir a meta
            if value < max_value:
                faltam_para_meta = max_value - value
                mensagem_mensal = html.Span(f'Faltam R${faltam_para_meta:,.2f} para atingir a meta mensal.', className='mensagem-vermelha')
            else:
                superou_meta = value - max_value
                mensagem_mensal = html.Span(f'Parabéns! Você superou em R${superou_meta:,.2f} sua meta mensal!', className='mensagem-verde')

            if value_anual < max_value_anual:
                faltam_para_meta_anual = max_value_anual - value_anual
                mensagem_anual = html.Span(f'Faltam R${faltam_para_meta_anual:,.2f} para atingir a meta anual.', className='mensagem-vermelha')
            else:
                superou_meta_anual = value_anual - max_value_anual
                mensagem_anual = html.Span(f'Parabéns! Você superou em R${superou_meta_anual:,.2f} sua meta anual!', className='mensagem-verde')

        # Criar gráficos de gauge
        fig_mensal = create_gauge_chart(value, max_value, assistente)
        fig_anual = create_gauge_chart(value_anual, max_value_anual, assistente)

    else:
        assistente = None
        canal_id = None
        value = 0
        value_anual = 0
        max_value = 0
        max_value_anual = 0
        fig_mensal = create_empty_gauge_chart()
        fig_anual = create_empty_gauge_chart()
        mensagem_mensal = html.Span("Dados do usuário não encontrados.", className='mensagem-vermelha')
        mensagem_anual = html.Span("Dados do usuário não encontrados.", className='mensagem-vermelha')

    # Atualiza o layout do Dash
    dash_app.layout = html.Div([
        dcc.Interval(id='interval-component', interval=60*1000, n_intervals=0),  # Atualiza a cada minuto
        html.Div([
            html.Div([
                dcc.Graph(id='gauge-chart-mensal', figure=fig_mensal),
                html.Div(id='mensagem-mensal', children=mensagem_mensal, style={'fontSize': 20, 'margin': '20px'})
            ], style={'display': 'flex', 'flex-direction': 'column', 'align-items': 'center', 'margin': '20px'}),
            html.Div([
                dcc.Graph(id='gauge-chart-anual', figure=fig_anual),
                html.Div(id='mensagem-anual', children=mensagem_anual, style={'fontSize': 20, 'margin': '20px'})
            ], style={'display': 'flex', 'flex-direction': 'column', 'align-items': 'center', 'margin': '20px'})
        ], style={'display': 'flex', 'flex-direction': 'row', 'align-items': 'flex-start'})
    ])

    return render_template("metas.html", active_page="relatorio-gerenciais")


# Função para criar um gráfico gauge vazio
def create_empty_gauge_chart():
    fig = go.Figure(go.Indicator(
        mode="gauge",
        gauge={'axis': {'range': [0, 1]}, 'bar': {'color': "lightgray"}}
    ))
    return fig


# Meta superintendente
@app.route('/metas-superintendente')
def metas_superintendente():

    carrega_datas_globais()

    user_id = session.get("user_id")
    if not user_id:
        return "Usuário não está logado ou ID de usuário não encontrado na sessão.", 401

    # Conversão de string para datetime
    from datetime import datetime

    data = datetime.strptime(dt_inicio_global, "%m/%d/%Y")
    mesano = data.strftime("%Y-%m-01")
    ano = data.strftime("%Y")

    # Obtendo o equipe_id do superintendente e todos os assistentes
    user_data = consulta_geral(user_id)
    if user_data:
        equipe_id = user_data[9]
        assistentes = consulta_assistentes(equipe_id)

        if assistentes:
            assistente_nomes = [assistente[1] for assistente in assistentes]  # Acessar pelo índice correto
            nome_equipe = consulta_nome_equipe(equipe_id)

            # Consultar valores mensais e anuais dos assistentes
            valor_mensal_superintendente = consulta_valores_superintendente(assistente_nomes, mesano) or 0
            valor_anual_superintendente = consulta_valores_anual_superintendente(assistente_nomes, ano) or 0

            # Consultar metas dos assistentes
            metas_assistentes = [consulta_meta(assistente[2]) or 0 for assistente in assistentes]  # Acessar pelo índice correto e substituir None por 0
            meta_superintendente = sum(metas_assistentes)
            meta_anual_superintendente = meta_superintendente * 12

            # Mensagens de metas mensais e anuais
            if valor_mensal_superintendente < meta_superintendente:
                faltam_para_meta_mensal = meta_superintendente - valor_mensal_superintendente
                mensagem_mensal_superintendente = html.Span(f'Faltam R${faltam_para_meta_mensal:,.2f} para atingir a meta mensal.', className='mensagem-vermelha')
            else:
                superou_meta_mensal = valor_mensal_superintendente - meta_superintendente
                mensagem_mensal_superintendente = html.Span(f'Parabéns! Você superou em R${superou_meta_mensal:,.2f} sua meta mensal!', className='mensagem-verde')

            if valor_anual_superintendente < meta_anual_superintendente:
                faltam_para_meta_anual = meta_anual_superintendente - valor_anual_superintendente
                mensagem_anual_superintendente = html.Span(f'Faltam R${faltam_para_meta_anual:,.2f} para atingir a meta anual.', className='mensagem-vermelha')
            else:
                superou_meta_anual = valor_anual_superintendente - meta_anual_superintendente
                mensagem_anual_superintendente = html.Span(f'Parabéns! Você superou em R${superou_meta_anual:,.2f} sua meta anual!', className='mensagem-verde')

            # Gerar gráficos de gauge para o superintendente
            fig_mensal_superintendente = create_gauge_chart(valor_mensal_superintendente, meta_superintendente, nome_equipe)
            fig_anual_superintendente = create_gauge_chart(valor_anual_superintendente, meta_anual_superintendente, nome_equipe)

            # Gerar gráficos de gauge para cada assistente
            figuras_assistentes = []
            for assistente in assistentes:
                valor_mensal = consulta_valores(assistente[1], mesano) or 0  # Garantir que seja numérico
                valor_anual = consulta_valores_anual(assistente[1], ano) or 0  # Garantir que seja numérico
                meta_mensal = consulta_meta(assistente[2]) or 0  # Substituir None por 0
                meta_anual = meta_mensal * 12

                if valor_mensal > 0 or valor_anual > 0 or meta_mensal > 0:
                    # Mensagens de metas mensais e anuais para assistentes
                    if valor_mensal < meta_mensal:
                        faltam_para_meta_mensal = meta_mensal - valor_mensal
                        mensagem_mensal_assistente = html.Span(f'Faltam R${faltam_para_meta_mensal:,.2f} para atingir a meta mensal.', className='mensagem-vermelha')
                    else:
                        superou_meta_mensal = valor_mensal - meta_mensal
                        mensagem_mensal_assistente = html.Span(f'Parabéns! Você superou em R${superou_meta_mensal:,.2f} sua meta mensal!', className='mensagem-verde')

                    if valor_anual < meta_anual:
                        faltam_para_meta_anual = meta_anual - valor_anual
                        mensagem_anual_assistente = html.Span(f'Faltam R${faltam_para_meta_anual:,.2f} para atingir a meta anual.', className='mensagem-vermelha')
                    else:
                        superou_meta_anual = valor_anual - meta_anual
                        mensagem_anual_assistente = html.Span(f'Parabéns! Você superou em R${superou_meta_anual:,.2f} sua meta anual!', className='mensagem-verde')

                    figura_mensal = create_gauge_chart(valor_mensal, meta_mensal, assistente[1])
                    figura_anual = create_gauge_chart(valor_anual, meta_anual, assistente[1])
                    figuras_assistentes.append({
                        'assistente': assistente[1],
                        'figura_mensal': figura_mensal,
                        'figura_anual': figura_anual,
                        'mensagem_mensal': mensagem_mensal_assistente,
                        'mensagem_anual': mensagem_anual_assistente
                    })

            # Atualiza o layout do Dash
            dash_app.layout = html.Div([
                dcc.Interval(id='interval-component', interval=60*1000, n_intervals=0),
                html.Div([
                    html.Div([
                        dcc.Graph(id='gauge-chart-mensal-superintendente', figure=fig_mensal_superintendente),
                        html.Div(id='mensagem-mensal-superintendente', children=mensagem_mensal_superintendente, style={'fontSize': 20, 'margin': '20px'})
                    ], style={'display': 'flex', 'flex-direction': 'column', 'align-items': 'center', 'margin': '20px'}),
                    html.Div([
                        dcc.Graph(id='gauge-chart-anual-superintendente', figure=fig_anual_superintendente),
                        html.Div(id='mensagem-anual-superintendente', children=mensagem_anual_superintendente, style={'fontSize': 20, 'margin': '20px'})
                    ], style={'display': 'flex', 'flex-direction': 'column', 'align-items': 'center', 'margin': '20px'}),
                ], style={'display': 'flex', 'flex-direction': 'row', 'align-items': 'center', 'margin': '20px'}),
                html.Div([
                    *[
                        html.Div([
                            html.Div([
                                dcc.Graph(id=f'gauge-chart-mensal-{figura["assistente"]}', figure=figura['figura_mensal']),
                                html.Div(id=f'mensagem-mensal-{figura["assistente"]}', children=figura['mensagem_mensal'], style={'fontSize': 20, 'margin': '20px'}),
                            ], style={'display': 'flex', 'flex-direction': 'column', 'align-items': 'center', 'margin': '20px'}),
                            html.Div([
                                dcc.Graph(id=f'gauge-chart-anual-{figura["assistente"]}', figure=figura['figura_anual']),
                                html.Div(id=f'mensagem-anual-{figura["assistente"]}', children=figura['mensagem_anual'], style={'fontSize': 20, 'margin': '20px'}),
                            ], style={'display': 'flex', 'flex-direction': 'column', 'align-items': 'center', 'margin': '20px'}),
                        ], style={'display': 'flex', 'flex-direction': 'row', 'align-items': 'center', 'margin': '20px'})
                        for figura in figuras_assistentes
                    ]
                ], style={'display': 'flex', 'flex-direction': 'column', 'align-items': 'flex-start'})
            ])

    return render_template("metas-equipes.html", active_page="relatorio-gerenciais")


@app.route("/unidades")
def unidades():
    return render_template("unidades.html")


@app.route("/franqueado-calendar")
def franqueado_calendar():
    return render_template("franqueado_calendar.html")


@app.route("/breve")
def breve():
    return render_template("breve.html")


@app.route('/ramais')
def ramais():
    return render_template('ramais.html')


@app.route('/gestao')
def gestao():
    if "user_id" in session:
        user_id = session.get("user_id")
        user_type = session.get("user_type")

        reembolso_permission = 2
        has_permission_reembolso = consulta_permissao(user_id, reembolso_permission)

        fastmoney_permission = 3
        has_permission_fastmoney = consulta_permissao(user_id, fastmoney_permission)

        comissao_permission = 4
        has_permission_comissao = consulta_permissao(user_id, comissao_permission)

        gestao_boleto_permission = 5
        has_permission_boleto = consulta_permissao(user_id, gestao_boleto_permission)

        return render_template(
            'gestao.html',
            active_page="gestao",
            has_permission_reembolso=has_permission_reembolso,
            has_permission_fastmoney=has_permission_fastmoney,
            has_permission_comissao=has_permission_comissao,
            has_permission_boleto=has_permission_boleto,
            user_type=user_type
        )
    else:
        return redirect(url_for("index"))


@app.route("/enviar_sugestao")
def enviar_sugestao():
    if "user_id" not in session:
        flash("Por favor, faça o login para continuar.")
        return redirect(url_for("index"))

    user_id = session["user_id"]
    dados_usuario = consulta_geral(user_id)

    if dados_usuario:
        usuario = {
            "id": dados_usuario[0],
            "nome": dados_usuario[2],
            "email": dados_usuario[5]
        }
        return render_template("sugestoes.html", usuario=usuario, active_page="rh")
    else:
        flash("Usuário não encontrado.")
        return redirect(url_for("index"))


def send_sugestao_email(nome, setor, user_email, assunto, descricao):
    remetente = os.getenv("EMAIL_REMETENTE")
    senha = os.getenv("EMAIL_SENHA")
    destinatario = "<EMAIL>"

    corpo_email = (
        f"Usuário: {nome}\n"
        f"Setor: {setor}\n"
        f"E-mail de solicitação: {user_email}\n"
        f"Descrição da Sugestão: {descricao}\n"
    )

    msg = EmailMessage()
    msg["From"] = remetente
    msg["To"] = destinatario
    msg["Subject"] = assunto
    msg.set_content(corpo_email)

    with smtplib.SMTP_SSL("smtps.uhserver.com", 465) as smtp:
        smtp.login(remetente, senha)
        smtp.send_message(msg)


@app.route("/submit_sugestao", methods=["POST"])
def submit_sugestao():
    if "user_id" not in session:
        return jsonify({'success': False, 'message': 'Usuário não autenticado.'})

    nome = request.form['nome']
    setor = request.form['setor']
    email = request.form['email']
    assunto = request.form['assunto']
    descricao = request.form['descricao']

    try:
        send_sugestao_email(nome, setor, email, assunto, descricao)
        return jsonify({'success': True, 'message': 'Sugestão enviada com sucesso!'})
    except Exception as e:
        print(f"Erro ao enviar e-mail: {e}")
        return jsonify({'success': False, 'message': 'Erro ao enviar a sugestão. Por favor, tente novamente.'})


# Função para carregar os dados do JSON
def carregar_dados():
    with open('empresas.json', 'r', encoding='utf-8') as file:
        return json.load(file)


@app.route('/portal-documentos')
def portal_documentos():
    return render_template('documentos_empresas.html', active_page="gestao")


@app.route('/render-documentos/<empresa_nome>')
def render_documentos(empresa_nome):
    empresas = carregar_dados()

    # Procurando a empresa pelo nome
    empresa = empresas.get(empresa_nome)

    if empresa:
        # Renderiza o template com a nova classe 'documentos-container-gestao'
        return render_template('render_documentos.html', empresa_nome=empresa_nome, documentos=empresa['documentos'], active_page="gestao")
    else:
        return "Empresa não encontrada", 404


@app.route('/api/documentos/<empresa_nome>')
def api_documentos(empresa_nome):
    empresas = carregar_dados()
    empresa = empresas.get(empresa_nome)
    if empresa:
        return jsonify(empresa['documentos'])
    else:
        return jsonify({"error": "Empresa não encontrada"}), 404


@app.route('/acessos', methods=['GET'])
def acessos_operadoras():
    if "user_id" in session:
        user_id = session.get("user_id")
        user_type = session.get("user_type")
        setor_id = session.get("setor_id")

        # Define a permissão necessária
        required_permission = 1

        try:
            # Consulta a permissão do usuário
            has_permission = consulta_permissao(user_id, required_permission)
            logging.info(f"Tem permissão: {has_permission}")

            # Carrega a lista de acessos com base no tipo de usuário
            if user_type == 7:
                listar_acessos = consulta_acessos()
            else:
                listar_acessos = consulta_acessos(setor_id)

            # Formata os acessos para renderizar no front-end
            acessos_formatado = [
                {
                    "id": acessos[0],
                    "nome": acessos[1],
                    "link": acessos[2],
                    "login": acessos[3],
                    "senha": acessos[4],
                    "unidade": acessos[5],
                    "codigo": acessos[6],
                    "outros": acessos[7],
                    "modalidade": acessos[8],
                    "portal_id": acessos[9],
                }
                for acessos in listar_acessos
            ]

            # Passa a permissão para o template junto com os acessos
            return render_template(
                "acessos.html",
                active_page="gestao",
                listar_acessos=acessos_formatado,
                has_permission=has_permission
            )
        except Exception as e:
            logging.error(f"Erro ao processar acessos: {e}")
            return redirect(url_for("index"))
    else:
        return redirect(url_for("index"))


@app.route('/atualizar_acesso', methods=['POST'])
def atualizar_acesso_route():
    if "user_id" in session:
        acesso_id = request.form.get('acesso_id')
        portal_id = request.form.get('portal_id')
        novo_nome_portal = request.form.get('novo_nome_portal')
        novo_link_portal = request.form.get('novo_link_portal')
        novo_login = request.form.get('novo_login')
        nova_senha = request.form.get('nova_senha')

        # Aqui tratamos os campos opcionais para substituir '--' por None
        nova_unidade = request.form.get('nova_unidade')
        novo_codigo_acesso = request.form.get('novo_codigo_acesso')
        novos_outros = request.form.get('novos_outros')
        nova_modalidade = request.form.get('nova_modalidade')

        # Substitui '--' por None
        nova_unidade = None if nova_unidade == '--' else nova_unidade
        novo_codigo_acesso = None if novo_codigo_acesso == '--' else novo_codigo_acesso
        novos_outros = None if novos_outros == '--' else novos_outros
        nova_modalidade = None if nova_modalidade == '--' else nova_modalidade

        user_id = session.get("user_id")
        required_permissions = 1

        has_permission = consulta_permissao(user_id, required_permissions)
        user_type = session.get("user_type")

        # Verifica se o usuário tem permissão ou é do tipo correto
        if has_permission or user_type in [1, 7]:
            # Verifica se todos os campos obrigatórios estão presentes
            if all([acesso_id, portal_id, novo_nome_portal, novo_link_portal, novo_login, nova_senha]):
                try:
                    # Passa todos os parâmetros para a função 'atualizar_acesso'
                    atualizar_acesso(
                        acesso_id, portal_id, novo_nome_portal, novo_link_portal,
                        novo_login, nova_senha, nova_unidade, novo_codigo_acesso,
                        novos_outros, nova_modalidade
                    )
                    return redirect(url_for('acessos_operadoras'))
                except Exception as e:
                    logging.error(f"Erro ao atualizar acesso: {e}")
                    return redirect(url_for('acessos_operadoras'))
            else:
                logging.error("Campos obrigatórios não fornecidos.")
                return redirect(url_for('acessos_operadoras', error="Campos obrigatórios ausentes."))
        else:
            return redirect(url_for('acessos_operadoras', error="Permissão insuficiente."))
    else:
        return redirect(url_for('index'))


@app.route('/add_portal', methods=['GET', 'POST'])
def insert_portal():
    if "user_id" in session:
        if request.method == 'GET':
            nome = request.args.get('nome').strip()
            link = request.args.get('link').strip()

            if not nome or not link:
                return jsonify({"error": "Nome e link são obrigatórios"}), 400

            verificar_portal = consulta_portais_to_insert(nome, link)
            return jsonify({"portals": verificar_portal}), 200

        if request.method == 'POST':
            data = request.get_json()
            nome = data.get('nome').strip()
            link = data.get('link').strip()

            if not nome or not link:
                return jsonify({"error": "Nome e link são obrigatórios"}), 400

            verificar_portal = consulta_portais_to_insert(nome, link)
            if verificar_portal:
                return jsonify({"error": "Portal já cadastrado no banco de dados, verifique os dados ou cadastre o acesso!"}), 400

            try:
                add_portal(nome, link)
                return jsonify({"message": "Portal cadastrado com sucesso!"}), 201
            except Exception as e:
                return jsonify({"error": str(e)}), 500
    else:
        return redirect(url_for("index"))


@app.route('/add_acessos', methods=['GET', 'POST'])
def insert_acessos():
    if "user_id" in session:
        listar_portais = consulta_portais()

    if request.method == 'GET':
        return jsonify({"portais": listar_portais}), 200

    if request.method == 'POST':
        data = request.get_json()
        setor_id = session.get("setor_id")
        portal_id = data.get('portal_id')
        login = data.get('login')
        senha = data.get('senha')
        unidade = data.get('unidade') or None
        codigo_acesso = data.get('codigo_acesso') or None
        outros = data.get('outros') or None
        modalidade = data.get('modalidade') or None

        if not login or not senha:
            return jsonify({"error": "Login e senha são obrigatórios"}), 400

        verificar_acesso = consulta_acessos_to_insert(setor_id, portal_id, login, senha)
        if verificar_acesso:
            return jsonify({"error": "Acesso já cadastrado no banco de dados, verifique os dados ou cadastre um novo acesso!"}), 400

        try:
            add_acessos(setor_id, portal_id, login, senha, unidade, codigo_acesso, outros, modalidade)
            return jsonify({"message": "Acesso cadastrado com sucesso!"}), 201
        except Exception as e:
            return jsonify({"error": str(e)}), 500
    else:
        return redirect(url_for("index"))


data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'data')
os.makedirs(data_dir, exist_ok=True)

def remove_old_temp_files(directory):
    for filename in os.listdir(directory):
        if filename.startswith("temp_excel_file_") and filename.endswith(".xlsm"):
            try:
                os.remove(os.path.join(directory, filename))
                logging.info(f"Arquivo temporário antigo {filename} removido com sucesso.")
            except Exception as e:
                logging.error(f"Erro ao remover o arquivo temporário antigo {filename}: {e}")


def clean_and_structure_excel(file, ordem):
    try:
        import pythoncom
        import win32com.client as win32
    except ImportError:
        raise RuntimeError("As bibliotecas `pywin32` e `pythoncom` não estão disponíveis neste ambiente.")

    # Inicializar o COM
    pythoncom.CoInitialize()

    # Remover arquivos temporários antigos
    remove_old_temp_files(data_dir)

    # Gerar um nome único para o arquivo temporário com identificador menor
    temp_file_name = f"temp_excel_file_{str(uuid.uuid4())[:5]}.xlsm"
    temp_file_path = os.path.join(data_dir, temp_file_name)
    file.save(temp_file_path)

    excel = None
    workbook = None

    try:
        # Abrir o Excel e executar a macro
        excel = win32.Dispatch('Excel.Application')
        excel.Visible = False  # False, para não exibir o Excel
        excel.DisplayAlerts = False

        try:
            workbook = excel.Workbooks.Open(temp_file_path)
            excel.Application.Run(f'{workbook.Name}!LimparPlanilha')

            # Excluir a aba "Base"
            for sheet in workbook.Sheets:
                logging.info(f"Encontrada aba: {sheet.Name}")
                if sheet.Name == "Base":
                    sheet.Delete()
                    logging.info("Aba 'Base' excluída.")
                    break

            # Salvar e fechar o arquivo Excel após executar a macro
            workbook.Save()
            workbook.Close(SaveChanges=True)
            workbook = None
            excel.Application.Quit()
            excel = None
            gc.collect()
        except Exception as e:
            logging.error(f"Erro ao abrir ou manipular o arquivo Excel: {e}")
            if workbook:
                workbook.Close(False)
            if excel:
                excel.Application.Quit()
            return None

        # Ler o arquivo Excel modificado
        try:
            xls = pd.ExcelFile(temp_file_path)
            logging.info(f"Planilhas disponíveis após exclusão: {xls.sheet_names}")
            if 'Grade' in xls.sheet_names:
                df = pd.read_excel(xls, 'Grade')
                logging.info(f"Planilha 'Grade' lida com sucesso.")
            else:
                logging.error(f"Planilha 'Grade' não encontrada no arquivo Excel.")
                return None
        except Exception as e:
            logging.error(f"Erro ao ler o arquivo Excel modificado: {e}")
            return None
    finally:
        # Desinicializar o COM e fechar qualquer instância de Excel aberta
        pythoncom.CoUninitialize()

        # Pausar para garantir que o arquivo seja liberado pelo sistema
        time.sleep(2)

        # Tentar remover o arquivo temporário
        try:
            os.remove(temp_file_path)
            logging.info(f"Arquivo temporário {temp_file_path} removido com sucesso.")
        except PermissionError:
            logging.error(f"Erro ao remover o arquivo: {temp_file_path} ainda está em uso.")
        except Exception as e:
            logging.error(f"Erro inesperado ao remover o arquivo: {e}")

    # Reestruturar os dados ignorando a primeira linha (cabeçalho)
    data = []
    for i in range(0, len(df)):
        # Ignorar linhas onde a coluna "Operadora" está vazia
        if pd.isnull(df.iloc[i, 0]):
            continue

        # Converter os valores da coluna "Totais" para float e dividir por 100
        totais_str = df.iloc[i, 5]
        if pd.notnull(totais_str):
            if isinstance(totais_str, str):
                totais = float(totais_str.replace(',', '.')) / 100
            else:
                totais = float(totais_str) / 100
        else:
            totais = None

        row_data = {
            'Operadora': df.iloc[i, 0],
            'Modalidade': df.iloc[i, 1],
            'Grupo': df.iloc[i, 2] if pd.notnull(df.iloc[i, 2]) else None,
            'Grade': ordem,
            'Comissionaveis': str(df.iloc[i, 4]),
            'Totais': totais,
        }

        # Adicionar as parcelas, substituindo valores vazios por None e convertendo
        for j in range(6, 36):
            parcela_key = f'parcela_{j-5}'
            valor_parcela = df.iloc[i, j]
            if pd.notnull(valor_parcela) and valor_parcela != "-":
                if isinstance(valor_parcela, str):
                    valor_parcela = float(valor_parcela.replace(',', '.')) / 100
                else:
                    valor_parcela = float(valor_parcela) / 100
            else:
                valor_parcela = None
            row_data[parcela_key] = valor_parcela

        data.append(row_data)

    # Excluir linhas que contêm "Total" na coluna "Comissionaveis" se ordem não for "total"
    if ordem.lower() != 'total':
        data = [row for row in data if 'Total' not in row.get('Comissionaveis', '')]

    return data


@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        ordem = request.form.get('ordem')
        file = request.files['file']
        logging.info(f'Ordem: {ordem}')
        logging.info(f'Arquivo recebido para upload.')

        if file and ordem:
            data = clean_and_structure_excel(file, ordem)

            if data is None:
                return jsonify({"error": "Não foi possível ler a planilha especificada."})

            # Definir o caminho para salvar o JSON
            json_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'data', 'output_data.json')

            # Salvar o JSON no diretório principal
            with open(json_file_path, 'w') as json_file:
                json.dump(data, json_file, indent=4)

            # Inserir os dados na base de dados
            try:
                insert_comissoes(data)
            except Exception as e:
                return jsonify({"error": f"Erro ao inserir dados no banco: {e}"})

            return jsonify({
                "data": data,
                "json_file_path": json_file_path
            })

    return render_template('upload.html')


@app.route('/upload-test', methods=['POST'])
def upload_test():
    if request.method == 'POST':
        # Carrega os dados de um arquivo JSON, simulando o resultado do processamento do Excel
        json_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'data', 'output_data.json')

        try:
            with open(json_file_path, 'r') as json_file:
                data = json.load(json_file)

            # Simula a inserção dos dados no banco
            try:
                insert_comissoes(data)
                return jsonify({
                    "message": "Dados inseridos com sucesso!",
                    "data": data
                }), 200
            except Exception as e:
                logging.error(f"Erro ao inserir dados: {e}")
                return jsonify({"error": f"Erro ao inserir dados no banco: {e}"}), 500

        except FileNotFoundError:
            return jsonify({"error": "Arquivo JSON não encontrado."}), 404
        except json.JSONDecodeError:
            return jsonify({"error": "Erro ao decodificar o JSON."}), 400

    return jsonify({"error": "Método não permitido."}), 405


@app.route('/upload-tabela-comissoes')
def gestao_tabela_comissoes():
    if "user_id" in session:
        try:
            return render_template('upload-tabela.html', active_page="gestao")
        except Exception as e:
            logging.error(f"Error: {e}")
            return str(e)
    else:
        return redirect(url_for("index"))


@app.route('/tabela-de-comissoes', methods=['GET'])
def tabela_comissoes():
    if "user_id" in session:
        try:

            listar_comissoes = consulta_comissoes()

            comissoes_formatada = [
                {
                    "id": comissoes[0],
                    "operadora": comissoes[1],
                    "modalidade": comissoes[2],
                    "grupo": comissoes[3],
                }
                for comissoes in listar_comissoes
            ]

            return render_template(
                "tabela_comissoes.html",
                active_page="gestao",
                listar_comissoes=comissoes_formatada
            )
        except Exception as e:
            logging.error(f"Erro ao processar: {e}")
            return redirect(url_for("index"))
    else:
        return redirect(url_for("index"))


@app.route('/grade-comissoes', methods=['GET'])
def grade_comissoes():
    grupo = request.args.get('grupo')
    operadora = request.args.get('operadora')
    modalidade = request.args.get('modalidade')

    try:
        grades = consulta_grade(grupo, operadora, modalidade)
        if not grades:
            grades = ['total']  # Garante que ao menos a grade "total" esteja disponível
            logging.info(f"Grades: {grades}")
        return jsonify({"grades": grades})
    except Exception as e:
        logging.error(f"Erro ao consultar as grades de comissões: {e}")
        return jsonify({"error": "Erro ao consultar as grades de comissões"}), 500


@app.route('/consulta_tabela', methods=['GET'])
def consulta_tabela_route():
    grupo = request.args.get('grupo')
    operadora = request.args.get('operadora')
    modalidade = request.args.get('modalidade')
    grade1 = request.args.get('grade1', 'total')
    grade2 = request.args.get('grade2', 'total')

    try:
        tabela = consulta_tabela(grupo, operadora, modalidade, grade1, grade2)
        logging.info(f"Tabelas: {tabela}")
        return jsonify(tabela)
    except Exception as e:
        logging.error(f"Erro ao consultar a tabela de comissões: {e}")
        return jsonify({"error": "Erro ao consultar a tabela de comissões"}), 500


@app.route('/politicas-beneficios-rh', methods=['GET', 'POST'])
def politicas_beneficios_rh():
    if request.method == 'POST':
        if 'limpar' in request.form:
            return redirect(url_for('politicas_beneficios_rh'))
        salario = float(request.form['salario'].replace('R$', '').replace('.', '').replace(',', '.').strip())
        meses_trabalhados = int(request.form['meses_trabalhados'])
        um_terco_salario = salario / 3
        valor_ferias = salario + um_terco_salario
        valor_proporcional = (valor_ferias / 12) * meses_trabalhados
        return render_template('politicas_beneficios_rh.html', valor_ferias=valor_ferias, valor_proporcional=valor_proporcional)
    return render_template('politicas_beneficios_rh.html', active_page="gestao")


@app.route('/gestao_ramais')
def gestao_ramais():
    return render_template('gestao_ramais.html', active_page="gestao")


@app.route('/gestao_integracao')
def gestao_integracao():
    return render_template('gestao_integracoes.html', active_page="gestao")


@app.route('/colaborador')
def colaborador():
    if "user_id" in session:
        user_id = session.get("user_id")
        user_type = session.get("user_type")

        reembolso_permission = 2
        has_permission_reembolso = consulta_permissao(user_id, reembolso_permission)

        fastmoney_permission = 3
        has_permission_fastmoney = consulta_permissao(user_id, fastmoney_permission)

        comissao_permission = 4
        has_permission_comissao = consulta_permissao(user_id, comissao_permission)

        return render_template(
            'colaborador.html',
            active_page="colaborador",
            has_permission_reembolso=has_permission_reembolso,
            has_permission_fastmoney=has_permission_fastmoney,
            has_permission_comissao=has_permission_comissao,
            user_type=user_type
        )
    else:
        return redirect(url_for("index"))


@app.route('/meus-beneficios', methods=['GET', 'POST'])
def meus_beneficios():
    if request.method == 'POST':
        salario = float(request.form['salario'])
        meses_trabalhados = int(request.form['meses_trabalhados'])
        um_terco_salario = salario / 3
        valor_ferias = salario + um_terco_salario
        valor_proporcional = (valor_ferias / 12) * meses_trabalhados

        # Retorna apenas o HTML do resultado, que será inserido na página já carregada
        return render_template('partials/resultado_calculo_ferias.html', valor_ferias=valor_ferias, valor_proporcional=valor_proporcional)

    return render_template('beneficios_colaborador.html', active_page="colaborador")

def calcular_inss(salario_bruto):
    """Calcula o INSS com base nas faixas de contribuição."""
    if salario_bruto <= 1412.00:
        inss = salario_bruto * 0.075
    elif salario_bruto <= 2666.68:
        inss = 1412.00 * 0.075 + (salario_bruto - 1412.00) * 0.09
    elif salario_bruto <= 4000.03:
        inss = 1412.00 * 0.075 + (2666.68 - 1412.00) * 0.09 + (salario_bruto - 2666.68) * 0.12
    else:
        inss = 1412.00 * 0.075 + (2666.68 - 1412.00) * 0.09 + (4000.03 - 2666.68) * 0.12 + (salario_bruto - 4000.03) * 0.14
    return inss


@app.route('/calcular-ferias', methods=['POST'])
def calcular_ferias():
    try:
        from datetime import datetime
        import logging


        logging.info("Início do cálculo de férias")

        # Captura o salário e dias de férias
        salario_str = request.form['salario']
        salario = float(salario_str.replace('R$', '').replace('.', '').replace(',', '.').strip())
        dias_ferias = int(request.form['dias_ferias'])
        logging.info(f"Salário: {salario}, Dias de Férias: {dias_ferias}")

        # Captura e processa a data de admissão
        data_admissao_str = request.form['data_admissao']
        logging.info(f"Data de Admissão (string): {data_admissao_str}")

        data_admissao = datetime.strptime(data_admissao_str, '%Y-%m-%d')
        logging.info(f"Data de Admissão (datetime): {data_admissao}")

        # Data atual
        data_atual = datetime.now()
        logging.info(f"Data Atual: {data_atual}")

        # Cálculo de anos trabalhados
        anos_trabalhados = data_atual.year - data_admissao.year
        logging.info(f"Anos Trabalhados: {anos_trabalhados}")

        # Cálculo das férias com base nos dias
        valor_ferias = (salario / 30) * dias_ferias
        logging.info(f"Valor das Férias: {valor_ferias}")

        # Adicional de 1/3 sobre as férias
        adicional_terco = valor_ferias / 3
        logging.info(f"Adicional de 1/3: {adicional_terco}")

        # Verifica se o colaborador optou pelo adiantamento do 13º salário
        receber_13 = 'receber_13' in request.form
        logging.info(f"Receber 13º junto com as férias: {receber_13}")

        if receber_13:
            valor_13_proporcional = (salario / 2)
            logging.info(f"13º Salário Proporcional: {valor_13_proporcional}")
        else:
            valor_13_proporcional = 0
            logging.info("13º Salário Proporcional não solicitado")

        # Calcula o INSS sobre o salário base
        inss = calcular_inss(valor_ferias + adicional_terco)
        logging.info(f"INSS: {inss}")

        # Valor líquido
        valor_liquido = valor_ferias + adicional_terco + valor_13_proporcional - inss
        logging.info(f"Valor Líquido: {valor_liquido}")

        return jsonify({
            'valor_ferias': f"R$ {valor_ferias:.2f}",
            'adicional_terco': f"R$ {adicional_terco:.2f}",
            'valor_13_proporcional': f"R$ {valor_13_proporcional:.2f}" if receber_13 else "Não solicitado",
            'inss': f"R$ {inss:.2f}",
            'valor_liquido': f"R$ {valor_liquido:.2f}",
            'anos_trabalhados': f"{anos_trabalhados} anos"
        })
    except Exception as e:
        logging.error(f"Erro ao calcular férias: {e}")
        return jsonify({'error': 'Erro ao calcular férias.'}), 500


@app.route('/ramais-colaborador')
def ramais_colaborador():
    return render_template('ramais_colaborador.html', active_page="colaborador")


@app.route('/acessos-colaborador', methods=['GET'])
def acessos_colaborador():
    if "user_id" in session:
        user_type = session.get("user_type")
        setor_id = session.get("setor_id")

        try:
            if user_type == 7:
                listar_acessos = consulta_acessos()
            else:
                listar_acessos = consulta_acessos(setor_id)

            acessos_formatado = [
                {
                    "id": acessos[0],
                    "nome": acessos[1],
                    "link": acessos[2],
                    "login": acessos[3],
                    "senha": acessos[4],
                    "unidade": acessos[5],
                    "codigo": acessos[6],
                    "outros": acessos[7],
                    "modalidade": acessos[8],
                }
                for acessos in listar_acessos
            ]


            return render_template(
                "acessos_colaborador.html",
                active_page="colaborador",
                listar_acessos=acessos_formatado
            )
        except Exception as e:
            logging.error(f"Erro ao processar acessos: {e}")
            return redirect(url_for("index"))
    else:
        return redirect(url_for("index"))

@app.route('/atualizar_acesso_colaborador', methods=['POST'])
def atualizar_acesso_colaborador():
    if "user_id" in session:
        acesso_id = request.form.get('acesso_id')
        nova_senha = request.form.get('nova_senha')

        if acesso_id and nova_senha:
            try:
                atualizar_acesso(acesso_id, nova_senha)
                return redirect(url_for('acessos_colaborador'))
            except Exception as e:
                logging.error(f"Erro ao atualizar acesso: {e}")
                return redirect(url_for('acessos_colaborador'))
        else:
            logging.error("ID do acesso ou nova senha não fornecidos.")
            return redirect(url_for('acessos_colaborador'))
    else:
        return redirect(url_for('index'))

@app.route('/add_portal_colaborador', methods=['GET', 'POST'])
def insert_portal_colaborador():
    if "user_id" in session:
        if request.method == 'GET':
            nome = request.args.get('nome').strip()
            link = request.args.get('link').strip()

            if not nome or not link:
                return jsonify({"error": "Nome e link são obrigatórios"}), 400

            verificar_portal = consulta_portais_to_insert(nome, link)
            return jsonify({"portals": verificar_portal}), 200

        if request.method == 'POST':
            data = request.get_json()
            nome = data.get('nome').strip()
            link = data.get('link').strip()

            if not nome or not link:
                return jsonify({"error": "Nome e link são obrigatórios"}), 400

            verificar_portal = consulta_portais_to_insert(nome, link)
            if verificar_portal:
                return jsonify({"error": "Portal já cadastrado no banco de dados, verifique os dados ou cadastre o acesso!"}), 400

            try:
                add_portal(nome, link)
                return jsonify({"message": "Portal cadastrado com sucesso!"}), 201
            except Exception as e:
                return jsonify({"error": str(e)}), 500
    else:
        return redirect(url_for("index"))


@app.route('/permissoes')
def permissoes():
    if "user_id" in session:
        return render_template('permissoes.html')
    else:
        return redirect(url_for("index"))


@app.route("/gestao-permissoes/<int:permissao_id>")
def gestao_permissoes(permissao_id):
    if "user_id" in session:

        # Dicionário de mapeamento de permissões
        permissoes_nomes = {
            1: "Alteração de Acessos",
            2: "Reembolso",
            3: "Fastmoney",
            4: "Gestão Tabela de Comissão"
        }

        permissoes = consulta_lista_permissoes(permissao_id)
        logging.info(f'Permissões: {permissoes}')

        # Verifica se retornou algum dado
        if not permissoes:
            permissoes = []  # Retorna uma lista vazia

        # Formata cada linha retornada pela consulta e aplica o mapeamento de nomes
        permissoes_formatado = [
            {
                "id": permissao[0],
                "user_id": permissao[1],
                "permissao_id": permissao[2],
                "user_name": permissao[3],
                "nome_permissao": permissoes_nomes.get(permissao[2], "Permissão Desconhecida")  # Mapeia o nome
            }
            for permissao in permissoes
        ]

        logging.info(f'Permissões Formatada: {permissoes_formatado}')

        return jsonify({'info': permissoes_formatado})

    else:
        return redirect(url_for("index"))


@app.route('/listar-usuarios', methods=['GET'])
def listar_usuarios():
    if "user_id" in session:
        try:
            usuarios = consulta_todos_usuarios()
            usuarios_formatados = [
                {
                    "user_id": usuario[0],
                    "user_name": usuario[1]
                }
            for usuario in usuarios]
            return jsonify({'usuarios': usuarios_formatados}), 200
        except Exception as e:
            logging.error(f'Erro ao listar usuários: {e}')
            return jsonify({'error': 'Erro ao listar usuários'}), 500
    else:
        return redirect(url_for("index"))

def formatar_cpf(cpf):
    """Formata um CPF para o padrão XXX.XXX.XXX-XX"""
    if not cpf:
        return ""
    cpf = str(cpf).strip()
    if len(cpf) != 11:
        return cpf
    return f"{cpf[:3]}.{cpf[3:6]}.{cpf[6:9]}-{cpf[9:]}"

def formatar_tel(telefone):
    """Formata um telefone para o padrão (XX) XXXXX-XXXX ou (XX) XXXX-XXXX"""
    if not telefone:
        return ""
    telefone = str(telefone).strip().replace("-", "").replace("(", "").replace(")", "").replace(" ", "")
    if len(telefone) == 11:  # Celular
        return f"({telefone[:2]}) {telefone[2:7]}-{telefone[7:]}"
    elif len(telefone) == 10:  # Fixo
        return f"({telefone[:2]}) {telefone[2:6]}-{telefone[6:]}"
    return telefone

@app.route('/api/admin/valores-coluna', methods=['GET'])
def valores_coluna_admin():
    if "user_id" not in session:
        return jsonify({"error": "Não autorizado"}), 401

    coluna = request.args.get('coluna', '')
    if not coluna:
        return jsonify({"error": "Parâmetro 'coluna' é obrigatório"}), 400

    try:
        # Mapear nomes de colunas amigáveis para nomes reais no banco
        colunas_map = {
            "id": "id",
            "tipo_usuario": "tipo_usuario",
            "nome": "nome",
            "data_nascimento": "data_nascimento",
            "cpf": "cpf",
            "email": "email",
            "email_confirmed": "email_confirmed",
            "telefone": "telefone"
        }

        coluna_db = colunas_map.get(coluna)
        if not coluna_db:
            return jsonify({"error": f"Coluna '{coluna}' não reconhecida"}), 400

        valores = consulta_valores_coluna(coluna_db)

        # Formatar valores especiais
        if coluna == "tipo_usuario":
            map_perfil = {
                1: "Master",
                2: "Sócio",
                3: "Superintendente",
                4: "Supervisor",
                5: "Funcionário",
                6: "Franqueado",
                7: "Dev",
            }
            valores = [map_perfil.get(v, "Desconhecido") for v in valores]
        elif coluna == "data_nascimento":
            valores = [v.strftime("%d/%m/%Y") if v else "" for v in valores]
        elif coluna == "cpf":
            valores = [formatar_cpf(v) for v in valores]
        elif coluna == "telefone":
            valores = [formatar_tel(v) for v in valores]
        elif coluna == "email_confirmed":
            valores = ["Sim" if v else "Não" for v in valores]

        return jsonify({"valores": valores}), 200
    except Exception as e:
        logging.error(f'Erro ao buscar valores da coluna: {e}')
        return jsonify({'error': f'Erro ao buscar valores da coluna: {str(e)}'}), 500


@app.route('/buscar-usuario/<int:user_id>', methods=['GET'])
def buscar_usuario(user_id):
    if "user_id" in session:
        try:
            usuario = consulta_geral(user_id)
            if usuario:
                return jsonify({'user_name': usuario[2]}), 200
            else:
                return jsonify({'error': 'Usuário não encontrado'}), 404
        except Exception as e:
            logging.error(f'Erro ao buscar usuário: {e}')
            return jsonify({'error': 'Erro ao buscar usuário'}), 500
    else:
        return redirect(url_for("index"))


@app.route('/adicionar-permissao', methods=['POST'])
def add_permissao():
    if "user_id" in session:
        try:
            user_id = request.form.get('user_id')
            permissao_id = request.form.get('permissao_id')

            if not user_id or not permissao_id:
                return jsonify({'Success': False, 'message': 'ID do usuário ou permissão não informado.'}), 400
            else:
                add_permissoes(user_id, permissao_id)

                return jsonify({'Success': True, 'message': 'Permissão adicionada com sucesso.'}), 200

        except Exception as e:
            logging.error(f'Erro ao adicionar a permissão: {e}')
            return jsonify({'Success': False, 'message': 'Erro interno do servidor ao adicionar a permissão.'}), 500

    else:
        return redirect(url_for("index"))


@app.route('/usuarios/<int:user_id>/permissoes/<int:permissao_id>', methods=['DELETE'])
def remover_permissao(user_id, permissao_id):
    if "user_id" in session:
        try:
            excluir_permissoes(user_id, permissao_id)

            return jsonify({'Success': True, 'message': 'Permissão removida com sucesso.'}), 200

        except Exception as e:
            logging.error(f'Erro ao remover a permissão: {e}')
            return jsonify({'Success': False, 'message': 'Erro interno do servidor ao remover a permissão.'}), 500

    else:
        return redirect(url_for("index"))


@app.route('/add_acessos_colaborador', methods=['GET', 'POST'])
def insert_acessos_colaborador():
    if "user_id" in session:
        listar_portais = consulta_portais()
        logging.info(f'Portais: {listar_portais}')

    if request.method == 'GET':
        return jsonify({"portais": listar_portais}), 200

    if request.method == 'POST':
        data = request.get_json()
        setor_id = session.get("setor_id")
        portal_id = data.get('portal_id')
        login = data.get('login')
        senha = data.get('senha')
        unidade = data.get('unidade') or None
        codigo_acesso = data.get('codigo_acesso') or None
        outros = data.get('outros') or None

        if not login or not senha:
            return jsonify({"error": "Login e senha são obrigatórios"}), 400

        verificar_acesso = consulta_acessos_to_insert(login, senha)
        if verificar_acesso:
            return jsonify({"error": "Acesso já cadastrado no banco de dados, verifique os dados ou cadastre um novo acesso!"}), 400

        try:
            add_acessos(setor_id, portal_id, login, senha, unidade, codigo_acesso, outros)
            return jsonify({"message": "Acesso cadastrado com sucesso!"}), 201
        except Exception as e:
            return jsonify({"error": str(e)}), 500
    else:
        return redirect(url_for("index"))


@app.route('/sugestoes_colaborador')
def mostrar_sugestoes_colaborador():
    if "user_id" not in session:
        flash("Por favor, faça o login para continuar.")
        return redirect(url_for("index"))

    user_id = session["user_id"]
    dados_usuario = consulta_geral(user_id)

    if dados_usuario:
        usuario = {
            "id": dados_usuario[0],
            "nome": dados_usuario[2],
            "email": dados_usuario[5]
        }
        return render_template("sugestoes_colaborador.html", usuario=usuario, active_page="colaborador")
    else:
        flash("Usuário não encontrado.")
        return redirect(url_for("index"))

def send_sugestao_colaborador_email(nome, setor, user_email, assunto, descricao):
    remetente = os.getenv("EMAIL_REMETENTE")
    senha = os.getenv("EMAIL_SENHA")
    destinatario = "<EMAIL>"

    corpo_email = (
        f"Usuário: {nome}\n"
        f"Setor: {setor}\n"
        f"E-mail de solicitação: {user_email}\n"
        f"Descrição da Sugestão: {descricao}\n"
    )

    msg = EmailMessage()
    msg["From"] = remetente
    msg["To"] = destinatario
    msg["Subject"] = assunto
    msg.set_content(corpo_email)

    with smtplib.SMTP_SSL("smtps.uhserver.com", 465) as smtp:
        smtp.login(remetente, senha)
        smtp.send_message(msg)


@app.route("/submit_sugestao_colaborador", methods=["POST"])
def submit_sugestao_colaborador():
    if "user_id" not in session:
        return jsonify({'success': False, 'message': 'Usuário não autenticado.'})

    nome = request.form['nome']
    setor = request.form['setor']
    email = request.form['email']
    assunto = request.form['assunto']
    descricao = request.form['descricao']

    try:
        send_sugestao_colaborador_email(nome, setor, email, assunto, descricao)
        return jsonify({'success': True, 'message': 'Sugestão enviada com sucesso!'})
    except Exception as e:
        print(f"Erro ao enviar e-mail: {e}")
        return jsonify({'success': False, 'message': 'Erro ao enviar a sugestão. Por favor, tente novamente.'})


@app.route('/gestao-de-reembolso')
def gestao_reembolsos():
    if "user_id" in session:
        user_id = session["user_id"]
        dados_usuario = consulta_geral(user_id)

        tipo_usuario = dados_usuario[1]
        if tipo_usuario in [1, 7]:
            reembolsos = consulta_reembolso()
        else:
            reembolsos = consulta_reembolso_usuario(user_id)

        return render_template('gestao-reembolsos.html', active_page="gestao", reembolsos=reembolsos)
    else:
        return redirect(url_for("index"))


@app.route('/detalhes-reembolso/<int:reembolso_id>', methods=['GET'])
def detalhes_reembolso(reembolso_id):
    reembolso = consulta_detalhes_reembolso(reembolso_id)
    if reembolso:
        reembolso_dict = {
            "id": reembolso[0],
            "nome": reembolso[1],
            "departamento": reembolso[2],
            "email": reembolso[3],
            "telefone": reembolso[4],
            "data_despesa": reembolso[5].strftime('%Y-%m-%d') if reembolso[5] else None,
            "tipo_despesa": reembolso[6],
            "valor_despesa": reembolso[7],
            "descricao": reembolso[8],
            "link_anexo": reembolso[9],
            "status": reembolso[10],
            "user_id": reembolso[11],
            "user_aprovador": reembolso[12],
            "data_aprovacao": reembolso[13].strftime('%Y-%m-%d %H:%M') if reembolso[13] else None,
            "nome_aprovador": reembolso[14],
            "data_solicitacao": reembolso[15].strftime('%Y-%m-%d %H:%M') if reembolso[15] else None
        }
        return jsonify(reembolso_dict)
    else:
        return jsonify({"error": "Reembolso não encontrado"}), 404


@app.route('/update-status-reembolso/<int:reembolso_id>', methods=['POST'])
def update_status_reembolso(reembolso_id):
    novo_status = request.json.get('status')
    user_aprovador = session.get("user_id")

    if novo_status not in ['Aprovado', 'Rejeitado', 'Pendente']:
        return jsonify({"error": "Status inválido"}), 400

    sucesso = atualizar_status_reembolso(reembolso_id, novo_status, user_aprovador)

    if sucesso:
        return jsonify({"success": True, "message": "Status atualizado com sucesso!"}), 200
    else:
        return jsonify({"error": "Erro ao atualizar status"}), 500


@app.route('/reembolso-colaborador', methods=['GET', 'POST'])
def reembolso_colaborador():
    if "user_id" not in session:
        return jsonify({'Success': False, 'message': 'Por favor, faça o login para continuar.'}), 401

    user_id = session["user_id"]
    dados_usuario = consulta_geral(user_id)

    if not dados_usuario:
        return jsonify({'Success': False, 'message': 'Usuário não encontrado.'}), 404

    usuario = {
        "nome": dados_usuario[2],
        "email": dados_usuario[5],
        "telefone": dados_usuario[7],
        "departamento": dados_usuario[13]
    }

    if request.method == 'GET':
        return render_template('reembolso_colaborador.html', usuario=usuario, active_page="colaborador")

    elif request.method == 'POST':
        try:
            # Capturar os dados do formulário
            nome = request.form.get('nome')
            email = request.form.get('email')
            telefone = request.form.get('telefone')
            departamento = request.form.get('departamento')
            data_despesa = request.form.get('data-despesa')
            tipo_despesa = request.form.get('tipo-despesa')
            valor = request.form.get('valor')
            descricao = request.form.get('descricao')
            comprovante = request.files.get('comprovante')

            if not (nome and email and telefone and departamento and data_despesa and tipo_despesa and valor and descricao and comprovante):
                return jsonify({'Success': False, 'message': 'Todos os campos são obrigatórios.'}), 400

            # Verificar se o arquivo foi enviado e se é válido
            if not comprovante or comprovante.filename == '':
                logging.error("Nenhum arquivo de comprovante foi enviado.")
                return jsonify({'Success': False, 'message': 'Nenhum arquivo de comprovante foi enviado.'}), 400

            # Criar um arquivo temporário para salvar o comprovante
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                comprovante.save(temp_file.name)  # Salva o arquivo no arquivo temporário

                # Fazer o upload do comprovante para o S3
                link_anexo = upload_comprovante_to_s3(temp_file.name, comprovante.filename)

                if not link_anexo:
                    return jsonify({'Success': False, 'message': 'Erro ao fazer upload do comprovante.'}), 500

            # Chamada para a função que envia o e-mail
            response = send_reembolso_email(nome, departamento, email, telefone, data_despesa, tipo_despesa, valor, descricao, comprovante)
            if response[1] != 200:
                return jsonify({'Success': False, 'message': 'Erro ao enviar o e-mail.'}), 500

            # Define o status inicial para a solicitação de reembolso
            status = "Pendente"

            # Chamada para a função que insere os dados no banco de dados
            add_reembolsos(user_id, nome, departamento, email, telefone, data_despesa, tipo_despesa, valor, descricao, link_anexo, status)

            # Retornar uma resposta de sucesso
            return jsonify({'Success': True, 'message': 'Solicitação de reembolso processada com sucesso!'}), 200

        except Exception as e:
            logging.error(f"Erro ao processar solicitação de reembolso: {e}")
            return jsonify({'Success': False, 'message': 'Erro interno do servidor ao processar a solicitação.'}), 500

        finally:
            # Garantir que o arquivo temporário seja removido
            if os.path.exists(temp_file.name):
                os.remove(temp_file.name)


def upload_comprovante_to_s3(file_path, filename):
    s3_client = boto3.client('s3',
                             aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                             aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'))
    bucket_name = os.getenv("S3_BUCKET")

    try:
        logging.debug(f"Uploading {filename} to bucket {bucket_name}")
        s3_client.upload_file(file_path, bucket_name, filename)

        # Construir a URL pública do arquivo no S3
        url = f"https://{bucket_name}.s3.amazonaws.com/{filename}"
        logging.info(f"Upload successful: {url}")

        return url

    except NoCredentialsError:
        logging.error("Credentials not available")
        return None
    except ClientError as e:
        logging.error(f"Error uploading file: {str(e)}")
        return None


def send_reembolso_email(nome, departamento, user_email, telefone, data_despesa, tipo_despesa, valor, descricao, comprovante):
    remetente = os.getenv("EMAIL_REMETENTE")
    senha = os.getenv("EMAIL_SENHA")
    destinatario = "<EMAIL>"

    if not remetente or not senha:
        logging.error("As credenciais de e-mail do remetente não estão definidas corretamente.")
        return jsonify({'Success': False, 'message': 'Configuração de e-mail inválida.'}), 500

    corpo_email = (
        f"Nome: {nome}\n"
        f"Departamento: {departamento}\n"
        f"E-mail: {user_email}\n"
        f"Telefone: {telefone}\n"
        f"Data da Despesa: {data_despesa}\n"
        f"Tipo de Despesa: {tipo_despesa}\n"
        f"Valor: R$ {valor}\n"
        f"Descrição: {descricao}\n"
    )

    msg = EmailMessage()
    msg["From"] = remetente
    msg["To"] = destinatario
    msg["Subject"] = f"Solicitação de Reembolso - {nome}"
    msg.set_content(corpo_email)

    if comprovante:
        file_data = comprovante.read()
        file_name = comprovante.filename
        msg.add_attachment(file_data, maintype="application", subtype="octet-stream", filename=file_name)

    try:
        with smtplib.SMTP_SSL("smtps.uhserver.com", 465) as smtp:
            smtp.login(remetente, senha)
            smtp.send_message(msg)
        logging.info("E-mail de reembolso enviado com sucesso!")
        return jsonify({'Success': True, 'message': 'E-mail de reembolso enviado com sucesso!'}), 200
    except Exception as e:
        logging.error(f"Erro ao enviar e-mail: {e}")
        return jsonify({'Success': False, 'message': 'Erro ao enviar e-mail.'}), 500


@app.route('/chat_content')
def chat_content():
    return render_template('chat.html', active_page="colaborador")


@app.route('/fastmoney')
def fastmoney():
    if "user_id" in session:
        return render_template('fastmoney.html', active_page="colaborador")
    else:
        return redirect(url_for("index"))


@app.route('/process_fastmoney', methods=['POST'])
def process_fastmoney():
    if "user_id" not in session:
        return jsonify({'Success': False, 'message': 'Por favor, faça o login para continuar.'}), 401

    try:
        logging.info("Processando solicitação de adiantamento...")

        user_id = session["user_id"]

        tipo_adiantamento = request.form.get('tipo_adiantamento')
        codigo_proposta = request.form.get('codigo_proposta')
        corretor = request.form.get('corretor')
        supervisor = request.form.get('supervisor')
        modalidade = request.form.get('modalidade')
        operadora = request.form.get('operadora')
        subproduto = request.form.get('subproduto')
        segurado = request.form.get('segurado')
        valor_proposta = request.form.get('valor_proposta')
        acordo = request.form.get('acordo')
        tabela_padrao = request.form.get('tabela_padrao')
        comprovante_file = request.files.get('comprovante')
        boleto_file = request.files.get('boleto')
        status = "Pendente"

        # Criar um arquivo temporário para salvar o comprovante e boleto
        link_anexo_comprovante = ""
        link_anexo_boleto = ""

        if comprovante_file:
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                comprovante_file.save(temp_file.name)
                # Fazer o upload do comprovante para o S3 e obter o link
                link_anexo_comprovante = upload_comprovante_to_s3(temp_file.name, comprovante_file.filename)

                if not link_anexo_comprovante:
                    return jsonify({'Success': False, 'message': 'Erro ao fazer upload do comprovante.'}), 500

        if boleto_file:
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                boleto_file.save(temp_file.name)
                # Fazer o upload do boleto para o S3 e obter o link
                link_anexo_boleto = upload_comprovante_to_s3(temp_file.name, boleto_file.filename)

                if not link_anexo_boleto:
                    return jsonify({'Success': False, 'message': 'Erro ao fazer upload do boleto.'}), 500

        # Insere os dados no banco de dados, passando os links dos arquivos ao invés do objeto FileStorage
        add_fastmoney(
            tipo_adiantamento, codigo_proposta, corretor, supervisor, modalidade, operadora, subproduto,
            segurado, valor_proposta, acordo, tabela_padrao, link_anexo_comprovante, link_anexo_boleto, status, user_id
        )

        # Envia o e-mail
        response = send_fastmoney_email(
            tipo_adiantamento, codigo_proposta, valor_proposta,
            corretor, supervisor, modalidade, acordo,
            tabela_padrao, segurado, operadora, link_anexo_comprovante
        )
        return response

    except Exception as e:
        logging.error(f"Erro ao processar solicitação de adiantamento: {e}")
        return jsonify({'Success': False, 'message': 'Erro interno do servidor ao processar a solicitação.'}), 500


def send_fastmoney_email(tipo_adiantamento, codigo_proposta, valor_proposta, corretor, supervisor, modalidade, acordo, tabela_padrao, segurado, operadora, comprovante_link):
    remetente = os.getenv("EMAIL_REMETENTE")
    senha = os.getenv("EMAIL_SENHA")
    destinatario = "<EMAIL>"

    if not remetente or not senha:
        logging.error("As credenciais de e-mail do remetente não estão definidas corretamente.")
        return jsonify({'Success': False, 'message': 'Configuração de e-mail inválida.'}), 500

    corpo_email = (
        f"Tipo de Adiantamento: {tipo_adiantamento}\n"
        f"Código da Proposta: {codigo_proposta}\n"
        f"Valor da Proposta: R$ {valor_proposta}\n"
        f"Corretor: {corretor}\n"
        f"Supervisor: {supervisor}\n"
        f"Modalidade: {modalidade}\n"  # Incluindo a modalidade no corpo do e-mail
        f"Acordo: {acordo}\n"
        f"Tabela Padrão: {tabela_padrao}\n"
        f"Segurado: {segurado}\n"
        f"Operadora: {operadora}\n"
    )

    # Adicionar o link do comprovante ao corpo do e-mail, se disponível
    if comprovante_link:
        corpo_email += f"Comprovante: {comprovante_link}\n"

    msg = EmailMessage()
    msg["From"] = remetente
    msg["To"] = destinatario
    msg["Subject"] = f"Solicitação de Adiantamento - {codigo_proposta}"
    msg.set_content(corpo_email)

    try:
        with smtplib.SMTP_SSL("smtps.uhserver.com", 465) as smtp:
            smtp.login(remetente, senha)
            smtp.send_message(msg)
        logging.info("E-mail de adiantamento enviado com sucesso!")
        return jsonify({'Success': True, 'message': 'E-mail de adiantamento enviado com sucesso!'}), 200
    except Exception as e:
        logging.error(f"Erro ao enviar e-mail: {e}")
        return jsonify({'Success': False, 'message': 'Erro ao enviar e-mail.'}), 500


def fetch_dados_adiantamento(id):
    url_base = f"https://sisweb-api.azurewebsites.net/api/producoes/{id}"
    url_detalhes = f"https://sisweb-api.azurewebsites.net/api/producoes/{id}/detalhes"
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }

    try:
        response_base = requests.get(url_base, headers=headers)
        if response_base.status_code != 200:
            logging.error(f"Erro ao buscar dados da proposta: {response_base.status_code}")
            return None

        data_base = response_base.json()

        response_detalhes = requests.get(url_detalhes, headers=headers)
        if response_detalhes.status_code != 200:
            logging.error(f"Erro ao buscar detalhes da proposta: {response_detalhes.status_code}")
            return None

        data_detalhes = response_detalhes.json()

        detalhes = data_detalhes["result"][0] if data_detalhes["result"] else {}

        result = {
            "id": data_base["result"].get("id"),
            "vlBoleto": data_base["result"].get("vlBoleto"),
            "corretor": data_base["result"].get("corretor", {}).get("nome"),
            "segurado": data_base["result"].get("segurado"),
            "nomeOperadora": data_base["result"].get("nomeOperadora"),
            "supervisor": data_base["result"].get("assistenteOriginal", {}).get("nome"),
            "subProduto": data_base["result"].get("subProduto"),
            "comissionamentoGradeProducao": detalhes.get("comissionamentoGradeProducao"),
            "comissionamentoGradeCorretor": detalhes.get("comissionamentoGradeCorretor"),
            "modalidade": data_base["result"].get("modalidade")  # Novo campo "modalidade"
        }

        return result

    except Exception as e:
        logging.error(f"Erro interno ao buscar dados da proposta: {e}")
        return None


@app.route('/api/adiantamento/<int:id>', methods=['GET'])
def get_dados_adiantamento(id):
    dados = fetch_dados_adiantamento(id)

    if dados:
        return jsonify({'result': dados, 'Success': True}), 200
    else:
        return jsonify({'Success': False, 'message': 'Erro de conexão com a base de dados do Trindade'}), 500


@app.route('/gestao-fastmoney', methods=['GET'])
def gestao_fastmoney():
    if "user_id" in session:
        user_id = session["user_id"]
        dados_usuario = consulta_geral(user_id)
        tipo_usuario = dados_usuario[1]
        unidade_id = dados_usuario[8]

        fastmoney_permission = 3
        has_permission_fastmoney = consulta_permissao(user_id, fastmoney_permission)

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        tipo_consulta = ""
        if tipo_usuario in [1, 7] or has_permission_fastmoney:
            fastmoneys = consulta_fastmoney(start_date, end_date)
            tipo_consulta = "geral"
        elif unidade_id in [9, 10]:
            fastmoneys = consulta_fastmoney_unidade(unidade_id, start_date, end_date)
            tipo_consulta = "unidade"
        else:
            fastmoneys = consulta_fastmoney_usuario(user_id, start_date, end_date)
            tipo_consulta = "usuario"

        # Converter tuplas para dicionários para facilitar uso no template
        fastmoneys_formatados = []
        for fm in fastmoneys:
            if tipo_consulta == "geral":
                fm_dict = {
                    'id': fm[0],
                    'user_id': fm[1],
                    'nome_empresa': fm[2],
                    'segurado': fm[3],
                    'operadora': fm[4],
                    'valor_proposta': fm[5],
                    'modalidade': fm[6],
                    'tipo_adiantamento': fm[7] if len(fm) > 7 else '',
                    'acordo': fm[8] if len(fm) > 8 else '',
                    'tabela_padrao': fm[9] if len(fm) > 9 else '',
                    'comprovante_link': fm[10] if len(fm) > 10 else '',
                    'status': fm[11] if len(fm) > 11 else '',
                    'data_criacao': fm[12] if len(fm) > 12 else '',
                    'supervisor': fm[13] if len(fm) > 13 else '',
                    'cod_proposta': fm[14] if len(fm) > 14 else '',
                    'sub_produto': fm[15] if len(fm) > 15 else '',
                    'boleto_link': fm[16] if len(fm) > 16 else ''
                }
            elif tipo_consulta == "unidade":
                fm_dict = {
                    'id': fm[0],
                    'user_id': fm[1],
                    'nome_empresa': fm[2],
                    'segurado': fm[3],
                    'operadora': fm[4],
                    'valor_proposta': fm[5],
                    'modalidade': fm[6],
                    'tipo_adiantamento': fm[7] if len(fm) > 7 else '',
                    'acordo': fm[8] if len(fm) > 8 else '',
                    'tabela_padrao': fm[9] if len(fm) > 9 else '',
                    'comprovante_link': fm[10] if len(fm) > 10 else '',
                    'boleto_link': fm[11] if len(fm) > 11 else '',
                    'status': fm[12] if len(fm) > 12 else '',
                    'data_criacao': fm[13] if len(fm) > 13 else '',
                    'supervisor': fm[14] if len(fm) > 14 else '',
                    'cod_proposta': fm[15] if len(fm) > 15 else '',
                    'sub_produto': fm[16] if len(fm) > 16 else '',
                    'motivo_rejeicao': fm[17] if len(fm) > 17 else '',
                    'comentario_rejeicao': fm[18] if len(fm) > 18 else ''
                }
            else:  # tipo_consulta == "usuario"
                fm_dict = {
                    'id': fm[0],
                    'user_id': fm[1],
                    'nome_empresa': fm[2],
                    'segurado': fm[3],
                    'operadora': fm[4],
                    'valor_proposta': fm[5],
                    'modalidade': fm[6],
                    'tipo_adiantamento': fm[7] if len(fm) > 7 else '',
                    'acordo': fm[8] if len(fm) > 8 else '',
                    'tabela_padrao': fm[9] if len(fm) > 9 else '',
                    'comprovante_link': fm[10] if len(fm) > 10 else '',
                    'status': fm[11] if len(fm) > 11 else '',
                    'data_criacao': fm[12] if len(fm) > 12 else '',
                    'supervisor': fm[13] if len(fm) > 13 else '',
                    'cod_proposta': fm[14] if len(fm) > 14 else '',
                    'sub_produto': fm[15] if len(fm) > 15 else '',
                    'boleto_link': fm[16] if len(fm) > 16 else ''
                }
            fastmoneys_formatados.append(fm_dict)

        return render_template("gestao-fastmoney.html", fastmoney=fastmoneys_formatados)
    return redirect(url_for("login"))


@app.route('/detalhes-fastmoney/<int:fastmoney_id>', methods=['GET'])
def detalhes_fastmoney(fastmoney_id):
    logging.info(f"Buscando detalhes para o fastmoney_id: {fastmoney_id}")
    fastmoney = consulta_detalhes_fastmoney(fastmoney_id)
    if fastmoney:
        fastmoney_dict = {
            "id": fastmoney[0],
            "user_id": fastmoney[1],
            "corretor": fastmoney[2],
            "segurado": fastmoney[3],
            "operadora": fastmoney[4],
            "valor_proposta": fastmoney[5],
            "modalidade": fastmoney[6],
            "tipo_adiantamento": fastmoney[7],
            "acordo": fastmoney[8],
            "tabela_padrao": fastmoney[9],
            "comprovante_link": fastmoney[10],
            "boleto_link": fastmoney[11],
            "status": fastmoney[12],
            "supervisor": fastmoney[13],
            "cod_proposta": fastmoney[14],
            "sub_produto": fastmoney[15],
            "motivo_rejeicao": fastmoney[16],  # Adiciona o motivo de rejeição
            "comentario_rejeicao": fastmoney[17]  # Adiciona o comentário de rejeição
        }
        return jsonify(fastmoney_dict)
    else:
        logging.error(f"Fastmoney com id {fastmoney_id} não encontrado.")
        return jsonify({"error": "Fastmoney não encontrado"}), 404


@app.route('/update-status-fastmoney/<int:fastmoney_id>', methods=['POST'])
def update_status_fastmoney(fastmoney_id):
    novo_status = request.json.get('status')
    motivo_rejeicao = request.json.get('motivo')
    comentario_rejeicao = request.json.get('comentario')

    if novo_status not in ['Aprovado', 'Rejeitado', 'Pendente']:
        return jsonify({"error": "Status inválido"}), 400

    if novo_status == 'Rejeitado':
        sucesso = rejeicao_status_fastmoney(fastmoney_id, novo_status, motivo_rejeicao, comentario_rejeicao)
    else:
        sucesso = atualizar_status_fastmoney(fastmoney_id, novo_status)

    if sucesso:
        return jsonify({"success": True, "message": "Status atualizado com sucesso!"}), 200
    else:
        return jsonify({"error": "Erro ao atualizar status"}), 500


@app.route('/reanalisar-fastmoney/<int:fastmoney_id>', methods=['POST'])
def reanalisar_fastmoney_route(fastmoney_id):
    if "user_id" not in session:
        return jsonify({'success': False, 'message': 'Por favor, faça o login para continuar.'}), 401

    try:
        # Receber os dados da solicitação corrigida via POST
        data = request.get_json()
        codigo_proposta = data.get('codigo_proposta')
        segurado = data.get('segurado')

        # Chama a função no dependencies.py para realizar a reanálise
        sucesso = reanalisar_fastmoney(fastmoney_id, codigo_proposta, segurado)

        if sucesso:
            return jsonify({'success': True, 'message': 'Solicitação enviada para reanálise com sucesso.'}), 200
        else:
            return jsonify({'success': False, 'message': 'Erro ao reanalisar solicitação.'}), 500

    except Exception as e:
        logging.error(f"Erro ao enviar solicitação para reanálise: {e}")
        return jsonify({'success': False, 'message': 'Erro interno ao processar solicitação.'}), 500


@app.route('/gestao_corretores', methods=['GET'])
def gestao_corretores():
    # Captura os parâmetros de filtro da query string
    nome = request.args.get('nome', "")
    somente_ativos = request.args.get('somenteAtivos')
    incluir_pessoa_fisica = request.args.get('incluirPessoaFisica')
    incluir_pessoa_juridica = request.args.get('incluirPessoaJuridica')
    pagina = int(request.args.get('pagina', 1))
    registros_por_pagina = int(request.args.get('registrosPorPagina', 100))

    # Inicializa a lista de corretores como vazia
    corretores_data = []

    # Só faz a busca se ao menos um filtro for aplicado
    if nome or somente_ativos or incluir_pessoa_fisica or incluir_pessoa_juridica:
        corretores_data = fetch_corretores_paginated(
            nome=nome,
            somente_ativos=somente_ativos if somente_ativos is not None else "true",
            incluir_pessoa_fisica=incluir_pessoa_fisica if incluir_pessoa_fisica is not None else "true",
            incluir_pessoa_juridica=incluir_pessoa_juridica if incluir_pessoa_juridica is not None else "true",
            pagina=pagina,
            registros_por_pagina=registros_por_pagina
        )

    return render_template('gestao-corretores.html', corretores=corretores_data)


def formatar_data(data_str):
    if data_str:
        try:
            # Tentativa de formatar no formato padrão ISO
            data_obj = datetime.strptime(data_str, '%Y-%m-%dT%H:%M:%S.%f')
            return data_obj.strftime('%d/%m/%Y')
        except ValueError:
            try:
                # Tentativa de formatar no formato alternativo (caso o milissegundo não esteja presente)
                data_obj = datetime.strptime(data_str, '%Y-%m-%dT%H:%M:%S')
                return data_obj.strftime('%d/%m/%Y')
            except ValueError:
                return "Data inválida"
    return "Data não disponível"


# Mapeamento do idProducaoTipo para as siglas correspondentes
producao_tipo_map = {
    1: "S",    # SAÚDE - Produção Nos Últimos 4 Meses
    2: "SX",   # SAÚDE CANCELADO - Produção Entre 5 a 8 Meses
    3: "ZZ",   # SAÚDE CANCELADO - Produção Há 9 Meses ou Mais
    4: "ST",   # STANDY Produção acima de 12 meses
    5: "PP"    # POSSÍVEL PRODUTOR
}

def fetch_corretores_paginated(nome="", email="", StatusContrato="", somente_ativos="true", incluir_pessoa_fisica="true", incluir_pessoa_juridica="true", pagina=1, registros_por_pagina=10):
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",  # Substitua pela sua API Key real
    }

    # Fazendo a requisição para a API dos corretores com filtros
    url = "https://sisweb-api.azurewebsites.net/api/corretores"
    params = {
        "Pagina": pagina,
        "RegistrosPorPagina": registros_por_pagina,
        "Nome": nome,
        "Email": email,
    }

    all_data = []
    try:
        response = requests.get(url, headers=headers, params=params)
        if response.status_code == 200:
            corretores = response.json().get("result", {}).get("collection", [])
            for corretor in corretores:
                # Formatar datas
                corretor['dtInclusao'] = formatar_data(corretor.get('dtInclusao'))

                # Verificar e formatar a data do Termo LGPD e determinar a situação do contrato
                termo_lgpd_data = corretor.get('dtLGPD')
                if termo_lgpd_data:
                    corretor['termoLGPD'] = formatar_data(termo_lgpd_data)
                    corretor['statusContrato'] = 'Regular'
                else:
                    corretor['termoLGPD'] = "Sem data de termo LGPD"
                    corretor['statusContrato'] = 'Irregular'

                # Verificar e formatar a data da última produção
                ultima_producao_data = None
                detalhes = fetch_corretor_dados_completo(corretor.get('id')).get('detalhes', [])

                # Certificar que detalhes é uma lista
                if isinstance(detalhes, list):
                    for detalhe in detalhes:
                        if isinstance(detalhe, dict) and detalhe.get('dtUltimaProducao'):
                            ultima_producao_data = detalhe['dtUltimaProducao']
                            break

                if ultima_producao_data:
                    corretor['ultimaProducao'] = formatar_data(ultima_producao_data)
                else:
                    corretor['ultimaProducao'] = "Sem produção registrada"

                # Obter dados adicionais do corretor, como o assistente e comissão
                corretor['assistente'] = detalhes[0].get('assistente', 'Não informado') if detalhes else 'Não informado'
                corretor['comissao'] = detalhes[0].get('gradeEsforcoProprio', 'Sem informação de comissão') if detalhes else 'Sem informação de comissão'

                # Determinar tipo de pessoa
                corretor['pessoa'] = 'Jurídica' if corretor.get('pessoaFisica') is None else 'Física'

                # Mapear idProducaoTipo para a sigla correspondente
                id_producao_tipo = detalhes[0].get('idProducaoTipo', 0) if detalhes else 0
                corretor['producao'] = producao_tipo_map.get(id_producao_tipo, 'Não especificado')

                all_data.append(corretor)
        else:
            logging.error(f"Erro ao buscar corretores: {response.status_code}")
    except requests.exceptions.RequestException as e:
        logging.error(f"Erro interno ao buscar corretores: {e}")

    return all_data


@app.route('/gestao_corretores/<int:id>', methods=['GET'])
def gestao_corretores_id(id):
    corretor = fetch_corretor_dados_completo(id)  # Busca detalhes do corretor específico
    return jsonify(corretor)  # Retorna os detalhes como JSON para o JavaScript processar


def fetch_corretor_dados_completo(id):
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",  # Substitua pela sua API Key real
    }

    urls = {
        "detalhes": f"https://sisweb-api.azurewebsites.net/api/corretores/{id}/detalhes",
        "enderecos": f"https://sisweb-api.azurewebsites.net/api/corretores/{id}/enderecos",
        "telefones": f"https://sisweb-api.azurewebsites.net/api/corretores/{id}/telefones",
        "documentos": f"https://sisweb-api.azurewebsites.net/api/corretores/{id}/documentos",
        "contas_bancarias": f"https://sisweb-api.azurewebsites.net/api/corretores/{id}/contasbancarias",
    }

    try:
        # Fazer todas as requisições para os diferentes endpoints da API
        detalhes_resp = requests.get(urls['detalhes'], headers=headers)

        # Verificar se a resposta está vazia ou com erro
        if not detalhes_resp.text or detalhes_resp.status_code != 200:
            logging.error(f"Erro ao buscar detalhes do corretor {id}: Resposta vazia")
            return jsonify({"errorMessage": "Erro ao buscar detalhes do corretor: Resposta vazia"}), 500

        logging.info(f"Status da API de detalhes: {detalhes_resp.status_code}")
        logging.info(f"Resposta bruta da API de detalhes: {detalhes_resp.text}")
        detalhes = detalhes_resp.json()

        # Repetir a verificação para as outras chamadas da API
        enderecos_resp = requests.get(urls['enderecos'], headers=headers)
        if not enderecos_resp.text or enderecos_resp.status_code != 200:
            logging.error(f"Erro ao buscar endereços do corretor {id}: Resposta vazia")
            return jsonify({"errorMessage": "Erro ao buscar endereços do corretor: Resposta vazia"}), 500

        logging.info(f"Status da API de endereços: {enderecos_resp.status_code}")
        logging.info(f"Resposta bruta da API de endereços: {enderecos_resp.text}")
        enderecos = enderecos_resp.json()

        telefones_resp = requests.get(urls['telefones'], headers=headers)
        if not telefones_resp.text or telefones_resp.status_code != 200:
            logging.error(f"Erro ao buscar telefones do corretor {id}: Resposta vazia")
            return jsonify({"errorMessage": "Erro ao buscar telefones do corretor: Resposta vazia"}), 500

        logging.info(f"Status da API de telefones: {telefones_resp.status_code}")
        logging.info(f"Resposta bruta da API de telefones: {telefones_resp.text}")
        telefones = telefones_resp.json()

        documentos_resp = requests.get(urls['documentos'], headers=headers)
        if not documentos_resp.text or documentos_resp.status_code != 200:
            logging.error(f"Erro ao buscar documentos do corretor {id}: Resposta vazia")
            return jsonify({"errorMessage": "Erro ao buscar documentos do corretor: Resposta vazia"}), 500

        logging.info(f"Status da API de documentos: {documentos_resp.status_code}")
        logging.info(f"Resposta bruta da API de documentos: {documentos_resp.text}")
        documentos = documentos_resp.json()

        contas_resp = requests.get(urls['contas_bancarias'], headers=headers)
        if not contas_resp.text or contas_resp.status_code != 200:
            logging.error(f"Erro ao buscar contas bancárias do corretor {id}: Resposta vazia")
            return jsonify({"errorMessage": "Erro ao buscar contas bancárias do corretor: Resposta vazia"}), 500

        logging.info(f"Status da API de contas bancárias: {contas_resp.status_code}")
        logging.info(f"Resposta bruta da API de contas bancárias: {contas_resp.text}")
        contas_bancarias = contas_resp.json()

        return jsonify({
            "detalhes": detalhes,
            "enderecos": enderecos,
            "telefones": telefones,
            "documentos": documentos,
            "contasBancarias": contas_bancarias
        })
    except Exception as e:
        logging.error(f"Erro ao buscar detalhes do corretor {id}: {str(e)}")
        return jsonify({"errorMessage": "Erro ao buscar detalhes do corretor"}), 500


# Configuração da API
API_KEY = '72620008-f7e3-4c13-8772-9f6fb64d85b1'
BASE_URL = 'https://sisweb-api.azurewebsites.net/api/sumarios/producao/parcelas/aberto-fechado'


# Função para gerar intervalos de 90 dias
def generate_date_ranges(start_date, end_date):
    import datetime

    logging.info(f"Gerando intervalos de 90 dias entre {start_date} e {end_date}.")
    while start_date < end_date:
        next_date = start_date + datetime.timedelta(days=90)
        yield start_date, min(next_date, end_date)
        start_date = next_date + datetime.timedelta(days=1)


# Função para buscar dados da API com tentativas de nova requisição
def fetch_data_with_retry(url, headers, params, retries=3):
    logging.info(f"Iniciando busca de dados na API {url} com parâmetros {params}.")
    for attempt in range(retries):
        try:
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()  # Verifica se a resposta foi bem-sucedida
            logging.info(f"Requisição bem-sucedida na tentativa {attempt + 1}.")
            return response.json()
        except requests.HTTPError as http_err:
            logging.warning(f"Tentativa {attempt + 1} falhou: {http_err}. Tentando novamente...")
            if attempt == retries - 1:
                logging.error(f"Todas as tentativas falharam. Último erro: {http_err}")
                raise http_err


# Rota para `/gestao-comissoes`
@app.route('/gestao-comissoes')
def gestao_comissoes():
    return render_template('gestao-comissoes.html', active_page="gestao")


# Rota Flask para buscar os dados de comissões em intervalos de 90 dias
@app.route('/api/comissoes-anual', methods=['GET'])
def fetch_api_ranking_anual_():
    start_date = request.args.get('DataInicio', '2025-01-01')
    end_date = request.args.get('DataFinal', '2025-12-31')

    logging.info(f"Recebendo solicitação para buscar comissões anuais de {start_date} até {end_date}.")

    # Conversão das datas
    start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
    end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d')

    # Trava a data final para o dia atual, se for maior que hoje
    today = datetime.datetime.now()
    if end_date > today:
        logging.info(f"Truncando data final de {end_date} para {today}.")
        end_date = today

    headers = {
        'accept': 'application/json',
        'ApiKey': API_KEY
    }

    aggregated_data = {}

    # Busca e agrega dados para intervalos de 90 dias
    for start, end in generate_date_ranges(start_date, end_date):
        logging.info(f"Buscando dados para o intervalo de {start} até {end}.")
        params = {
            'DtInicio': start.strftime('%m/%d/%Y'),
            'DtFinal': end.strftime('%m/%d/%Y'),
            'Pagina': 1,
            'RegistrosPorPagina': 100
        }

        try:
            response = fetch_data_with_retry(BASE_URL, headers, params)
            data = response.get('result', {}).get('collection', [])
            if not data:
                logging.info(f"Nenhum dado retornado para o intervalo de {start} até {end}.")
                continue
            aggregate_data(data, aggregated_data)
        except requests.HTTPError as http_err:
            logging.error(f"Erro ao buscar dados para o intervalo {start} até {end}: {http_err}")
        except Exception as e:
            logging.error(f"Ocorreu um erro: {e}")

    # Converte os dados agregados em uma lista
    final_data = [
        {
            'assistente': item['assistente'],
            'vlTotal': item['vlTotal'],
            'vlContrato': item['vlContrato'],
            'vlVida': item['vlVida'],
            'vlReceber': item['vlReceber'],
            'vlPagar': item['vlPagar'],
            'corretorCount': item['corretorCount'],
            'contratoCount': item['contratoCount'],
            'vidaCount': item['vidaCount']
        }
        for item in aggregated_data.values()
    ]

    logging.info("Dados agregados com sucesso. Enviando resposta.")
    return jsonify(final_data)


# Rota padrão para buscar dados de comissões sem intervalos
@app.route('/api/comissoes', methods=['GET'])
def buscar_comissoes():
    data_tipo = request.args.get('DataTipo', 'Producao')  # Valor padrão: Producao
    visualizacao = request.args.get('Visualizacao', 'Todos')  # Valor padrão: Todos
    texto_procura = request.args.get('TextoDeProcura', '')  # Filtro de busca (opcional)
    data_inicio = request.args.get('DataInicio')  # Data obrigatória
    data_final = request.args.get('DataFinal')  # Data obrigatória
    pagina = request.args.get('Pagina', 1)  # Página padrão 1
    registros_por_pagina = request.args.get('RegistrosPorPagina', 100)  # Default 100

    logging.info(f"Recebendo solicitação para buscar comissões de {data_inicio} até {data_final}.")

    if not data_inicio or not data_final:
        logging.error("DataInicio e DataFinal são obrigatórios.")
        return jsonify({"error": "DataInicio e DataFinal são obrigatórios."}), 400

    params = {
        'DataTipo': data_tipo,
        'Visualizacao': visualizacao,
        'TextoDeProcura': texto_procura,
        'DataInicio': data_inicio,  # O formato precisa estar como MM/DD/YYYY
        'DataFinal': data_final,    # O formato precisa estar como MM/DD/YYYY
        'Pagina': pagina,
        'RegistrosPorPagina': registros_por_pagina
    }

    headers = {
        'accept': 'application/json',
        'ApiKey': API_KEY
    }

    try:
        response = requests.get(BASE_URL, params=params, headers=headers)
        response.raise_for_status()  # Verificar se a requisição foi bem-sucedida
        logging.info("Dados de comissões obtidos com sucesso.")

        # Logging para verificar o conteúdo da resposta
        comissoes_data = response.json()
        logging.info(f"Conteúdo da resposta da API: {comissoes_data}")

        return jsonify(comissoes_data)  # Retornar os dados da API em JSON
    except requests.exceptions.RequestException as e:
        logging.error(f"Erro na requisição à API: {e}")
        return jsonify({'error': str(e)}), 500


def render_tabela(filtros, grade_canal, has_permission):
    operadoras = sorted(list(set([filtro[1] for filtro in filtros])))
    modalidades = sorted(list(set([filtro[2] for filtro in filtros])))
    grades = [grade_canal]

    return render_template(
        "tabela_porGrade.html",
        active_page="gestao",
        has_permission=has_permission,
        grades=grades,
        operadoras=operadoras,
        modalidades=modalidades,
        tabelas=[],
    )


def format_tabelas(tabelas):
    return [
        {
            "operadora": tabela[0],
            "modalidade": tabela[1],
            "grade": tabela[15],
            "totais": float(tabela[2]),
            "parcela_1": float(tabela[3]) if tabela[3] is not None else 0,
            "parcela_2": float(tabela[4]) if tabela[4] is not None else 0,
            "parcela_3": float(tabela[5]) if tabela[5] is not None else 0,
            "parcela_4": float(tabela[6]) if tabela[6] is not None else 0,
            "parcela_5": float(tabela[7]) if tabela[7] is not None else 0,
            "parcela_6": float(tabela[8]) if tabela[8] is not None else 0,
            "parcela_7": float(tabela[9]) if tabela[9] is not None else 0,
            "parcela_8": float(tabela[10]) if tabela[10] is not None else 0,
            "parcela_9": float(tabela[11]) if tabela[11] is not None else 0,
            "parcela_10": float(tabela[12]) if tabela[12] is not None else 0,
            "parcela_11": float(tabela[13]) if tabela[13] is not None else 0,
            "parcela_12": float(tabela[14]) if tabela[14] is not None else 0,
        }
        for tabela in tabelas
    ]


def handle_post_request(data, grade_canal):
    grade = grade_canal
    operadora = data.get("operadora", "")
    modalidade = data.get("modalidade", "")

    if not grade and not operadora and not modalidade:
        tabelas = consulta_tabelas()
    elif not operadora and not modalidade:
        tabelas = consulta_tabela_grade(grade)
    elif not operadora and not grade:
        tabelas = consulta_tabela_mod(modalidade)
    elif not modalidade and not grade:
        tabelas = consulta_tabela_op(operadora)
    elif not operadora:
        tabelas = consulta_tabela_grade_mod(grade, modalidade)
    elif not modalidade:
        tabelas = consulta_tabela_op_grade(grade, operadora)
    else:
        tabelas = consulta_tabela_geral(grade, operadora, modalidade)

    return jsonify({"tabelas": format_tabelas(tabelas)})


def get_grade_canal(canal):
    canal_grade_map = {
        "assessoria": "ext-susep",
        "externo": "ext-susep",
        "coworking": "co-working",
        "franquia": "franquia-2021",
        "solution": "brh-solution",
    }
    return canal_grade_map.get(canal, None)


@app.route("/tabelas-comissoes/grade", methods=["GET", "POST"])
def tabela_comissoes_grade():
    if "user_id" in session:
        user_id = session.get("user_id")
        user_type = session.get("user_type")
        required_permission = 4
        has_permission = consulta_permissao(user_id, required_permission)

        try:
            # Permissão total para master ou dev (user_type in [1, 7]) ou por permissão
            if user_type in [1, 7] or has_permission:
                if request.method == "GET":
                    filtros = consulta_filtros_comissoes()

                    # Eliminar duplicatas e ordenar grades corretamente
                    grades = sorted(
                        list(
                            set(
                                [
                                    filtro[0]
                                    for filtro in filtros
                                    if filtro[0].lower() != "total"
                                ]
                            )
                        )
                    )
                    operadoras = sorted(list(set([filtro[1] for filtro in filtros])))
                    modalidades = sorted(list(set([filtro[2] for filtro in filtros])))

                    return render_template(
                        "tabela_porGrade.html",
                        active_page="gestao",
                        has_permission=has_permission,
                        grades=grades,
                        operadoras=operadoras,
                        modalidades=modalidades,
                        tabelas=[],
                    )

                elif request.method == "POST":
                    data = request.get_json()
                    grade = data.get("grade", "")
                    operadora = data.get("operadora", "")
                    modalidade = data.get("modalidade", "")

                    # Se nenhum filtro foi selecionado, executa a consulta padrão
                    if not grade and not operadora and not modalidade:
                        tabelas = consulta_tabelas()
                    elif not operadora and not modalidade:
                        tabelas = consulta_tabela_grade(grade)
                    elif not operadora and not grade:
                        tabelas = consulta_tabela_mod(modalidade)
                    elif not modalidade and not grade:
                        tabelas = consulta_tabela_op(operadora)
                    elif not operadora:
                        tabelas = consulta_tabela_grade_mod(grade, modalidade)
                    elif not modalidade:
                        tabelas = consulta_tabela_op_grade(grade, operadora)
                    else:
                        tabelas = consulta_tabela_geral(grade, operadora, modalidade)

                    return jsonify({"tabelas": format_tabelas(tabelas)})

            # Permissão limitada para supervisores e supervisor, trava a grade
            elif user_type in [3, 4]:
                consulta_canal = consulta_geral(user_id)
                canal_id = consulta_canal[-2]
                canal_name = consulta_canal_grade(canal_id)
                canal = canal_name[0][1]

                grade_canal = get_grade_canal(canal)
                if not grade_canal:
                    return jsonify({"error": "Canal inválido"}), 400

                if request.method == "GET":
                    filtros = consulta_filtros_comissoes()
                    return render_tabela(filtros, grade_canal, has_permission)

                elif request.method == "POST":
                    data = request.get_json()
                    return handle_post_request(data, grade_canal)

            return (
                jsonify({"error": "Usuário sem permissão para acessar a tabela"}),
                400,
            )

        except Exception as e:
            logging.error(f"Erro ao acessar a tabela: {e}")
            return (
                jsonify(
                    {
                        "error": "Ocorreu um erro ao processar a tabela. Tente novamente mais tarde."
                    }
                ),
                500,
            )

    return redirect(url_for("index"))


@app.route('/tabelas-comissoes/grade/filtros', methods=['POST'])
def filtros_por_grade():
    data = request.get_json()
    grade_selecionada = data.get("grade")

    filtros = consulta_filtros_por_grade(grade_selecionada)

    if filtros:
        operadoras = sorted(list(set([filtro[0] for filtro in filtros])))
        modalidades = sorted(list(set([filtro[1] for filtro in filtros])))

        return jsonify({
            "operadoras": operadoras,
            "modalidades": modalidades
        })
    else:
        return jsonify({"error": "Erro ao consultar filtros para a grade selecionada."}), 500


@app.route('/exportar-pdf', methods=['POST'])
def exportar_pdf():
    if 'user_id' not in session:
        return redirect(url_for('index'))  # Verifica se o usuário está logado

    # Caminho relativo ao diretório raiz do projeto
    script_path = os.path.join(os.getcwd(), 'static', 'js', 'gerar_pdf.js')

    data = request.get_json()  # Captura os filtros enviados do front-end
    grade = data.get('grade', '')
    operadora = data.get('operadora', '')
    modalidade = data.get('modalidade', '')

    # Consulta os dados com base nos filtros
    if not grade and not operadora and not modalidade:
        tabelas = consulta_tabelas()
    elif not operadora and not modalidade:
        tabelas = consulta_tabela_grade(grade)
    elif not operadora and not grade:
        tabelas = consulta_tabela_mod(modalidade)
    elif not modalidade and not grade:
        tabelas = consulta_tabela_op(operadora)
    elif not operadora:
        tabelas = consulta_tabela_grade_mod(grade, modalidade)
    elif not modalidade:
        tabelas = consulta_tabela_op_grade(grade, operadora)
    else:
        tabelas = consulta_tabela_geral(grade, operadora, modalidade)

    # Formata os dados para o PDF
    formatted_tabelas = format_tabelas(tabelas)

    try:
        # Cria um arquivo temporário para armazenar os dados JSON
        with tempfile.NamedTemporaryFile(delete=False, mode='w', suffix='.json') as temp_file:
            json.dump(formatted_tabelas, temp_file)
            temp_file_path = temp_file.name

        # Executa o script Node.js, passando o caminho do arquivo temporário
        result = subprocess.run(
            ['node', script_path, temp_file_path],
            check=True,
            capture_output=True,
            text=True
        )

        logging.info(f"Saída do Node.js: {result.stdout}")
    except subprocess.CalledProcessError as e:
        logging.error(f"Erro ao executar o Node.js: {e}")
        logging.error(f"Saída do erro: {e.stderr}")
        return "Erro ao gerar o PDF", 500
    finally:
        # Remove o arquivo temporário após o uso
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

    # Força o download do PDF gerado
    return send_file('tabela_filtrada.pdf', as_attachment=True)


@app.route("/gestao/novo-processo-seletivo", methods=["GET", "POST"])
def novo_processo_seletivo():
    if "user_id" not in session:
        return redirect(url_for("index"))

    if request.method == "GET":
        return render_template("criar_processo_seletivo.html", active_page="gestao")

    if request.method == "POST":
        try:

            logging.info(request.form)

            titulo = request.form["titulo"]
            descricao = request.form["descricao"]
            requisitos = request.form["requisitos"]
            data_inicio = request.form["data_inicio"]
            data_fim = request.form["data_fim"]

            user_type = session.get("user_type")
            user_id = session.get("user_id")

            required_permission = 0
            has_permission = consulta_permissao(user_id, required_permission)

            if user_type in [1, 7] or has_permission:
                add_processo_seletivo(titulo, descricao, requisitos, data_inicio, data_fim)
                return jsonify({"success": True, "message": "Processo seletivo adicionado com sucesso!"}), 200
            else:
                return jsonify({"success": False, "error": "Usuário sem permissão para acessar a tabela"}), 403
        except Exception as e:
            logging.error(f"Error: {e}")
            return jsonify({"success": False, "error": str(e)}), 500


@app.route('/rh/processos-seletivos-internos')
def processos_seletivos_interno():
    if "user_id" not in session:
        return redirect(url_for("index"))

    from datetime import datetime

    processos_seletivos = consulta_processo_seletivo()

    current_date = datetime.now().strftime("%Y-%m-%d")

    return render_template('processo_seletivo.html',
                           active_page="rh",
                           processos_seletivos=processos_seletivos,
                           current_date=current_date)


@app.route('/rh/processos-seletivos-internos/<int:seletivo_id>', methods=["GET", "POST"])
def processos_seletivos_detalhado(seletivo_id):
    if "user_id" not in session:
        return redirect(url_for("index"))

    if request.method == "GET":
        try:
            seletivo = consulta_processo_seletivo_detalhado(seletivo_id)

            if seletivo:
                return jsonify(seletivo), 200
            else:
                return jsonify({"error": "Processo seletivo não encontrado."}), 404
        except Exception as e:
            logging.error(f"Erro ao consultar detalhes do processo seletivo: {e}")
            return jsonify({"error": str(e)}), 500

    if request.method == "POST":
        try:
            logging.info(request.form)

            id_seletivo = request.form.get("id_seletivo", seletivo_id)
            id_user = session.get("user_id")
            nome = request.form["nome"]
            dp_atual = request.form["dp_atual"]
            cargo_atual = request.form["cargo_atual"]
            cnh = request.form["cnh"].lower() == "true"
            carro = request.form["carro"].lower() == "true"
            formacao = request.form["formacao"]
            formacao_detal = request.form["formacao-detal"]
            certificacao = request.form["certificacao"]
            motivo = request.form["motivo"]

            add_inscricao_seletivo(id_seletivo, id_user, nome, dp_atual, cargo_atual, cnh, carro, formacao, formacao_detal, certificacao, motivo)

            return redirect(url_for('processos_seletivos_interno'))
        except Exception as e:
            logging.error(f"Error: {e}")
            return jsonify({"success": False, "error": str(e)}), 500


@app.route('/gestao/processos_seletivos')
def processos_seletivos_abertos():
    processos = consulta_processos()
    return render_template('gestao_candidaturas.html', processos=processos, active_page="gestao")


@app.route('/consulta_candidaturas/<int:id_seletivo>', methods=["GET"])
def consulta_candidaturas_endpoint(id_seletivo):
    candidaturas = consulta_candidaturas(id_seletivo)
    return jsonify(candidaturas)


def ajuste_acompanhamento_amil(df):
    # Passo 1: Apagar as linhas 1 até 3 (index 0, 1 e 2)
    df = df.iloc[3:].reset_index(drop=True)

    # Passo 2: Renomear as colunas para o padrão do banco de dados
    colunas_mapeamento = {
        'Tipo PME': 'tipo_pme',
        'Porte': 'porte',
        'Operadora': 'operadora',
        'Filial': 'filial',
        'Unidade': 'unidade',
        'Local de Recebimento': 'local_recebimento',
        'Tipo Venda': 'tipo_venda',
        'Linha de Produto': 'linha_produto',
        'Grupo Proposta': 'grupo_proposta',
        'Contrato MEDICO': 'contrato_medico',
        'Contrato DENTAL': 'contrato_dental',
        'CNPJ Empresa': 'cnpj_empresa',
        'Nome Razão Social': 'nome_razao_social',
        'E-KIT': 'e_kit',
        'Nome Fantasia Contrato': 'nome_fantasia_contrato',
        'Natureza Jurídica': 'natureza_juridica',
        'Tipo do Produto': 'tipo_produto',
        'Proposta Comissionada Médica': 'proposta_comissionada_medica',
        'Proposta Comissionada Dental': 'proposta_comissionada_dental',
        'Tipo Comissionamento': 'tipo_comissionamento',
        'Motivo Não Comissionar': 'motivo_nao_comissionar',
        'Normativa de Venda': 'normativa_venda',
        'Número da Proposta': 'numero_proposta',
        'Número da Cotação': 'numero_cotacao',
        'Mês Referência': 'mes_referencia',
        'Vigência da Proposta': 'vigencia_proposta',
        'Município': 'municipio',
        'Bairro': 'bairro',
        'Estado': 'estado',
        'Qtd Portabilidade Dental': 'qtd_portabilidade_dental',
        'Qtd Portabilidade Medica': 'qtd_portabilidade_medica',
        'Qtde Total Benef. Médico': 'qtde_total_benef_medico',
        'Qtde Total Benef. Dental': 'qtde_total_benef_dental',
        'Valor Total Médica': 'valor_total_medica',
        'Valor Total Dental': 'valor_total_dental',
        'Valor Taxa Implantação': 'valor_taxa_implantacao',
        'Valor Aditivos': 'valor_aditivos',
        'Nome Plano Médico': 'nome_plano_medico',
        'Nome Plano Dental': 'nome_plano_dental',
        'Qtde Benef. por Plano': 'qtde_benef_por_plano',
        'Valor Benef. por Plano': 'valor_benef_por_plano',
        'CPF Corretor': 'cpf_corretor',
        'Nome Corretor': 'nome_corretor',
        'CPF Supervisor': 'cpf_supervisor',
        'Nome Supervisor': 'nome_supervisor',
        'Código Produtor': 'codigo_produtor',
        'CNPJ Produtor': 'cnpj_produtor',
        'Nome Produtor': 'nome_produtor',
        'Código Plataforma': 'codigo_plataforma',
        'Nome Plataforma': 'nome_plataforma',
        'CPF Gestor Produtor CNPJ': 'cpf_gestor_produtor_cnpj',
        'Nome Gestor Produtor CNPJ': 'nome_gestor_produtor_cnpj',
        'Situação': 'situacao',
        'Possui Imagem': 'possui_imagem',
        'Data Protocolo': 'data_protocolo',
        'Data da Digitalização': 'data_digitalizacao',
        'Data em Digitação': 'data_digitacao',
        'Data Pronta para Análise': 'data_pronta_para_analise',
        'Data em Análise': 'data_em_analise',
        'Qtd Devoluções': 'qtd_devolucoes',
        'Data Primeira Devolução': 'data_primeira_devolucao',
        'Motivo Primeira Devolução': 'motivo_primeira_devolucao',
        'Envio Retificadora Primeira Devolução': 'envio_retificadora_primeira_devolucao',
        'Obs Primeira Devolução': 'obs_primeira_devolucao',
        'Motivos de devoluções dos beneficiários': 'motivos_devolucoes_beneficiarios',
        'Data Devolução': 'data_devolucao',
        'Motivo Última Devolução': 'motivo_ultima_devolucao',
        'Envio Retificadora Última Devolução': 'envio_retificadora_ultima_devolucao',
        'Obs Última Devolução': 'obs_ultima_devolucao',
        'Data Reapresentação': 'data_reapresentacao',
        'Data Envio para Under': 'data_envio_para_under',
        'Data Análise Técnica': 'data_analise_tecnica',
        'Data Cancelamento': 'data_cancelamento',
        'Data Declínio': 'data_declinio',
        'Data Implantação': 'data_implantacao',
        'Venda Online': 'venda_online',
        'Venda e-corretora': 'venda_ecorretora',
        'Motivo de Cancelamento': 'motivo_cancelamento',
        'Observações de Cancelamento': 'obs_cancelamento',
        'E-faturamento': 'e_faturamento',
        'PTE': 'pte',
        'Forma de Pagto': 'forma_pagto',
        'Obs Analise': 'obs_analise',
        'Compulsória': 'compulsoria',
        'Vigência Contrato': 'vigencia_contrato',
        'Data Assinatura': 'data_assinatura',
        'Data Contrato Assinado': 'data_contrato_assinado',
        'Data Aceite Banco': 'data_aceite_banco',
        'Data Aguard. Confirm. Pagto': 'data_aguard_confirm_pagto',
        'Data Cancelada': 'data_cancelada',
        'Situação Prop. Cancelada': 'situacao_prop_cancelada',
        'Forma Pagto Selecionada': 'forma_pagto_selecionada',
        'Forma Pagto Efetuada': 'forma_pagto_efetuada',
        'Assistente SISWeb': 'assistente_sisweb',
        'Beneficiários Dental': 'beneficiarios_dental',
        'Beneficiários Médica': 'beneficiarios_medica',
        'Data Retorno após 1º Devolução': 'data_retorno_apos_1_devolucao',
        'Data Retorno após Ult.Devolução': 'data_retorno_apos_ult_devolucao',
        'Forma de Pagamento': 'forma_de_pagamento',
        'Idade Beneficiário': 'idade_beneficiario',
        'Local de Recebimento': 'local_de_recebimento',
        'Motivo Envio para Under': 'motivo_envio_para_under',
        'Nome Gestor do Produtor CNPJ': 'nome_gestor_do_produtor_cnpj',
        'Nome Titular': 'nome_titular',
        'Normativa de Venda': 'normativa_de_vendas',
        'Obs Primeira Devolução': 'observacao_primeira_devolucao',
        'Obs Última Devolução': 'observacao_ultima_devolucao',
        'Proposta Idoso': 'proposta_idoso',
        'Qtd Devoluções': 'qtde_de_devolucoes',
        'Situação Dentro da Análise Tecn.': 'situacao_dentro_da_analise_tecnica',
        'Tipo de Contrato': 'tipo_de_contrato',
        'Valor Desconto': 'valor_desconto',
        'Valor Liquido': 'valor_liquido'
    }

    df.rename(columns=colunas_mapeamento, inplace=True)

    # Passo 3: Substituir 'S' por 'Sim' e 'N' por 'Não'
    for coluna in df.columns:
        df[coluna] = df[coluna].apply(lambda x: x.strip() if isinstance(x, str) else x)  # Remove espaços ao redor
        df[coluna] = df[coluna].replace({
            'S': 'Sim',
            'N': 'Não'
        })

    # Passo 4: Substituir 'Sim' por True e 'Não' por False
    for coluna in df.columns:
        df[coluna] = df[coluna].replace({
            'Sim': True,
            'Não': False
        })

    # Passo 5: Tratar colunas de datas e substituir NaT por None (NULL)
    for coluna in df.select_dtypes(include=['datetime']).columns:
        df[coluna] = df[coluna].apply(lambda x: None if pd.isna(x) else x)

    return df


@app.route('/upload_acompanhamento', methods=['POST'])
def upload_acompanhamento():
    # Suprimir FutureWarnings do Pandas
    warnings.simplefilter(action='ignore', category=FutureWarning)

    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    if file and file.filename.endswith('.xlsx'):
        try:
            # Carregar o arquivo Excel, pulando as 3 primeiras linhas
            excel_file = pd.read_excel(BytesIO(file.read()), sheet_name=0, skiprows=3)

            # Aplicar as modificações usando a função ajuste_acompanhamento_amil
            df_ajustado = ajuste_acompanhamento_amil(excel_file)

            # Obter a modalidade selecionada
            modalidade = request.form.get('modalidade')
            logging.info(f"Modalidade selecionada: {modalidade}")

            # Converter o DataFrame ajustado para uma lista de dicionários
            data_with_modalidade = df_ajustado.to_dict(orient='records')
            for entry in data_with_modalidade:
                entry['modalidade'] = modalidade

            # Fazer o upload para o banco de dados usando a função add_acompanhamento
            add_acompanhamento(modalidade, data_with_modalidade)

            # Retornar uma resposta de sucesso
            return jsonify({"message": f"Dados para a modalidade {modalidade} inseridos com sucesso."}), 200

        except Exception as e:
            # Logar o erro no servidor, mas retornar uma mensagem genérica ao frontend
            logging.error(f"Erro durante o upload: {str(e)}")
            return jsonify({"error": "Ocorreu um erro ao processar o arquivo. Tente novamente."}), 500

    return jsonify({"error": "Invalid file type, please upload a .xlsx file"}), 400


@app.route('/gestao/upload-acompanhamento')
def gestao_acompanhamento_amil():
    if "user_id" in session:
        try:
            return render_template('upload-acompanhamento-amil.html', active_page="gestao")
        except Exception as e:
            logging.error(f"Error: {e}")
            return str(e)
    else:
        return redirect(url_for("index"))


@app.route('/gerenciais/acompanhamento/amil')
def acompanhamento_amil():
    if "user_id" in session:
        try:
            user_type = session.get("user_type")
            user_id = session.get("user_id")

            required_permission = 4
            has_permission = consulta_permissao(user_id, required_permission)

            list_acompanhamentos = []

            if user_type in [4]:
                dados_usuario = consulta_geral(user_id)
                assistente = dados_usuario[-3]
                list_acompanhamentos = consulta_acompanhamento_assistente(assistente)

            elif user_type in [1, 7] or has_permission:
                list_acompanhamentos = consulta_acompanhamento_geral()

            else:
                return jsonify({"error": "Usuário sem permissão para acessar a tabela"}), 400

            # Passa list_acompanhamentos para o template HTML
            return render_template('acompanhamento-amil.html',
                                   active_page="relatorio-gerenciais",
                                   list_acompanhamentos=list_acompanhamentos)
        except Exception as e:
            logging.error(f"Error: {e}")
            return str(e)
    else:
        return redirect(url_for("index"))


@app.route('/detalhes_acompanhamento', methods=['GET'])
def detalhes_acompanhamento():
    assistente = request.args.get('assistente')
    situacao = request.args.get('situacao')

    # Chama a função do dependencies.py para buscar os detalhes
    detalhes = consulta_acompanhamento_detalhada(assistente, situacao)

    if detalhes is not None:
        return jsonify(detalhes)
    else:
        return jsonify({'error': 'Erro ao buscar os detalhes'}), 500


# Rota para renderizar o template da página de boletos
@app.route('/boletos_corretores')
def boletos_corretores():
    return render_template('boletos_corretores.html')


# Função para buscar dados da API externa
def fetch_dados_adiantamento(id):
    url_base = f"https://sisweb-api.azurewebsites.net/api/producoes/{id}"
    url_detalhes = f"https://sisweb-api.azurewebsites.net/api/producoes/{id}/detalhes"
    headers = {
        "accept": "application/json",
        "ApiKey": "72620008-f7e3-4c13-8772-9f6fb64d85b1",
    }

    try:
        response_base = requests.get(url_base, headers=headers)
        if response_base.status_code != 200:
            return None

        data_base = response_base.json()

        response_detalhes = requests.get(url_detalhes, headers=headers)
        if response_detalhes.status_code != 200:
            return None

        data_detalhes = response_detalhes.json()
        detalhes = data_detalhes["result"][0] if data_detalhes["result"] else {}

        result = {
            "codigo_proposta": data_base["result"].get("id"),
            "vlBoleto": data_base["result"].get("vlBoleto"),
            "corretor": data_base["result"].get("corretor", {}).get("nome"),
            "segurado": data_base["result"].get("segurado"),
            "nomeOperadora": data_base["result"].get("nomeOperadora"),
            "supervisor": data_base["result"].get("assistente", {}).get("nome"),
            "subProduto": data_base["result"].get("subProduto"),
            "comissionamentoGradeProducao": detalhes.get("comissionamentoGradeProducao"),
            "comissionamentoGradeCorretor": detalhes.get("comissionamentoGradeCorretor"),
            "modalidade": data_base["result"].get("modalidade"),
            "status": data_base["result"].get("status")
        }

        return result

    except Exception as e:
        return None


# Rota para buscar a proposta específica via código
@app.route('/api/get_proposta/<codigo_proposta>', methods=['GET'])
def get_proposta(codigo_proposta):
    result = fetch_dados_adiantamento(codigo_proposta)
    if result:
        return jsonify(result), 200
    else:
        return jsonify({'error': 'Proposta não encontrada'}), 404


# Rota para upload do boleto e extração de código de barras
@app.route('/upload_boleto', methods=['POST'])
def upload_boleto():
    if 'file' not in request.files:
        logging.error("Nenhum arquivo foi enviado.")
        return jsonify({'error': 'Nenhum arquivo foi enviado'}), 400

    file = request.files['file']

    if file.filename == '':
        logging.error("Nenhum arquivo foi selecionado.")
        return jsonify({'error': 'Nenhum arquivo foi selecionado'}), 400

    senha = request.form.get('senha')
    codigo_barras_manual = request.form.get('codigo_barras_manual')

    try:
        resultado = processar_boleto(file, senha)
        if resultado.get('solicitar_senha'):
            return jsonify({'error': 'PDF protegido por senha', 'solicitar_senha': True}), 400
        if resultado['codigo_barras'] == 'Código não encontrado' and not codigo_barras_manual:
            return jsonify({'error': 'Código de barras não encontrado e não foi fornecido manualmente', 'solicitar_codigo_barras': True}), 200
        if codigo_barras_manual:
            resultado['codigo_barras'] = codigo_barras_manual
        return jsonify(resultado), 200

    except Exception as e:
        logging.error(f"Erro ao processar o arquivo: {str(e)}")
        return jsonify({'error': f'Erro ao processar o arquivo: {str(e)}'}), 500


def buscar_codigo_barras(texto):
    """
    Função principal para extrair o código de barras de um texto de boleto.
    Primeiro tenta o regex original; se falhar, tenta o regex mais flexível.
    """
    # Primeiro tenta o padrão original (boletos sem muitos pontos e espaços)
    pattern_original = r'(\d{5}\.\d{5}\s?\d{5}\.\d{6}\s?\d{5}\.\d{6}\s?\d\s?\d{14})'
    matches = re.findall(pattern_original, texto)

    if matches:
        for match in matches:
            codigo_barras = match.replace(" ", "").replace(".", "")
            if len(codigo_barras) in [47, 48]:
                return codigo_barras

    # Se o padrão original falhar, tenta o padrão mais flexível (com mais pontos e espaços)
    pattern_flexivel = r'(\d{3}\s?\d{5}\.?[\d\s\.]+\s?\d{14})'
    matches_flexivel = re.findall(pattern_flexivel, texto)

    if matches_flexivel:
        for match in matches_flexivel:
            codigo_barras = match.replace(" ", "").replace(".", "")
            if len(codigo_barras) in [47, 48]:
                return codigo_barras

    return None


def buscar_valor_boleto(texto):
    """
    Função auxiliar para buscar o valor do boleto no formato x.xxx,xx ou xxx,xx e retornar o valor em centavos.
    O valor é usado apenas como validação auxiliar.
    """
    pattern_valor = r'R?\$?\s?(\d{1,3}(\.\d{3})*,\d{2})'
    matches = re.findall(pattern_valor, texto)

    if matches:
        valores_encontrados = [float(match[0].replace('.', '').replace(',', '.')) * 100 for match in matches]
        valor_boleto = max(valores_encontrados)
        return int(valor_boleto)

    return None


# Configuração básica do logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def processar_boleto(file, senha=None):
    """
    Função que orquestra a extração do código de barras e usa o valor do boleto como uma validação auxiliar.
    """
    logging.info("Iniciando o processamento do boleto.")

    reader = PyPDF2.PdfReader(file)

    if reader.is_encrypted:
        logging.info("O PDF está protegido por senha.")
        if senha:
            try:
                reader.decrypt(senha)
                logging.info("Senha fornecida foi usada para descriptografar o PDF.")
            except Exception as e:
                logging.error(f"Erro ao descriptografar o PDF: {str(e)}")
                return {
                    'erro': 'Erro ao descriptografar o PDF',
                    'codigo_barras': None,
                    'valor_boleto': None
                }
        else:
            logging.warning("PDF protegido por senha e nenhuma senha foi fornecida.")
            return {
                'erro': 'PDF protegido por senha',
                'codigo_barras': None,
                'valor_boleto': None,
                'solicitar_senha': True
            }

    texto_extraido = ""
    for page_num, page in enumerate(reader.pages, 1):
        texto_extraido += page.extract_text()
        logging.debug(f"Texto extraído da página {page_num}: {texto_extraido[:100]}...")  # Loga os primeiros 100 caracteres do texto extraído

    # Primeiro, buscar o código de barras (foco principal)
    codigo_barras = buscar_codigo_barras(texto_extraido)
    logging.info(f"Código de barras encontrado: {codigo_barras}")

    # Se o código de barras foi encontrado, usar o valor como validação auxiliar
    valor_boleto = buscar_valor_boleto(texto_extraido)
    logging.info(f"Valor do boleto encontrado: {valor_boleto}")

    if codigo_barras and valor_boleto:
        valor_boleto_str = f"{valor_boleto:010d}"
        if codigo_barras[-10:] == valor_boleto_str:
            logging.info("Validação do valor do boleto com o código de barras foi bem-sucedida.")
        else:
            logging.warning("A validação do valor do boleto com o código de barras falhou.")

    resultado = {
        'codigo_barras': codigo_barras if codigo_barras else 'Código não encontrado',
        'valor_boleto': valor_boleto if valor_boleto else 'Valor não encontrado'
    }
    logging.info(f"Resultado do processamento: {resultado}")
    return resultado

@app.route('/enviar_solicitacao', methods=['POST'])
def enviar_solicitacao():
    try:
        # Coleta os dados do formulário enviados via POST
        codigo_proposta = request.form.get('codigo_proposta')
        vlBoleto = request.form.get('valor_proposta')
        corretor_nome = request.form.get('corretor')
        segurado = request.form.get('segurado')
        nome_operadora = request.form.get('operadora')
        supervisor_nome = request.form.get('supervisor')
        sub_produto = request.form.get('subproduto')
        comissionamento_grade_producao = request.form.get('acordo')
        comissionamento_grade_corretor = request.form.get('tabela_padrao')
        modalidade = request.form.get('modalidade')
        codigo_barras = request.form.get('codigo_barras')
        user_id = session.get("user_id")

        # Processa o arquivo de boleto enviado
        if 'boleto' not in request.files:
            logging.error("Erro: Arquivo de boleto não encontrado no request.")
            return jsonify({'error': 'Arquivo de boleto não encontrado'}), 400

        boleto_file = request.files['boleto']

        # Garantir que o nome do arquivo é seguro
        temp_filename = secure_filename(boleto_file.filename) or f"boleto_{uuid.uuid4().hex}.pdf"

        # Criar um arquivo temporário para salvar o boleto
        temp_file_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                boleto_file.save(temp_file.name)
                temp_file_path = temp_file.name
                logging.debug(f"Arquivo salvo temporariamente em: {temp_file_path}")

        except Exception as e:
            logging.error(f"Erro ao salvar o arquivo temporário: {str(e)}")
            return jsonify({'error': 'Erro ao salvar o arquivo temporário'}), 500

        # Verificar se o caminho do arquivo temporário é válido
        if not temp_file_path or not os.path.exists(temp_file_path):
            logging.error(f"Erro: O caminho do arquivo temporário '{temp_file_path}' não é válido.")
            return jsonify({'error': 'Erro ao salvar o arquivo temporário'}), 500

        # Fazer o upload para o S3
        s3_url = upload_arquivos_to_s3(temp_file_path, temp_filename)

        # Remover o arquivo temporário
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)
            logging.debug(f"Arquivo temporário {temp_file_path} removido.")

        if not s3_url:
            return jsonify({'error': 'Erro ao fazer upload do arquivo para o S3'}), 500

        # Chama a função de adicionar ao banco de dados
        add_boleto_corretor(codigo_proposta, vlBoleto, corretor_nome, segurado, nome_operadora, supervisor_nome,
                            sub_produto, comissionamento_grade_producao, comissionamento_grade_corretor,
                            modalidade, codigo_barras, user_id, s3_url)

        return jsonify({'message': 'Solicitação enviada com sucesso'}), 200

    except Exception as e:
        logging.error(f"Erro ao enviar solicitação: {str(e)}")
        return jsonify({'error': f'Erro ao enviar solicitação: {str(e)}'}), 500


@app.route('/gestao/boletos/corretores')
def gestao_boletos_corretores():
    if "user_id" not in session:
        return redirect(url_for("index"))

    user_id = session.get("user_id")
    user_type = session.get("user_type")

    # Permissão necessário para acessar gestão de boletos
    required_permissions = 5

    # Verifica se o usuário tem permissão
    has_permission = consulta_permissao(user_id, required_permissions)

    if has_permission or user_type in [1, 7]:
        boletos = fetch_boletos()
    else:
        boletos = fetch_boletos_usuario(user_id)

    return render_template('gestao_boletos_corretores.html', boletos=boletos, active_page="gestao")


@app.route('/upload_comprovante_gestao', methods=['POST'])
def upload_comprovante():
    try:
        # Coleta os dados do formulário enviados via POST
        codigo_proposta = request.form.get('codigo_proposta')

        # Processa o arquivo de comprovante enviado
        if 'file' not in request.files:
            logging.error("Erro: Arquivo de comprovante não encontrado no request.")
            return jsonify({'error': 'Arquivo de comprovante não encontrado'}), 400

        comprovante_file = request.files['file']

        # Garantir que o nome do arquivo é seguro
        temp_filename = secure_filename(comprovante_file.filename) or f"comprovante_{uuid.uuid4().hex}.pdf"

        # Criar um arquivo temporário para salvar o comprovante
        temp_file_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                comprovante_file.save(temp_file.name)
                temp_file_path = temp_file.name
                logging.debug(f"Arquivo salvo temporariamente em: {temp_file_path}")

        except Exception as e:
            logging.error(f"Erro ao salvar o arquivo temporário: {str(e)}")
            return jsonify({'error': 'Erro ao salvar o arquivo temporário'}), 500

        # Verificar se o caminho do arquivo temporário é válido
        if not temp_file_path or not os.path.exists(temp_file_path):
            logging.error(f"Erro: O caminho do arquivo temporário '{temp_file_path}' não é válido.")
            return jsonify({'error': 'Erro ao salvar o arquivo temporário'}), 500

        # Fazer o upload para o S3
        s3_url = upload_arquivos_to_s3(temp_file_path, temp_filename)

        # Remover o arquivo temporário
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)
            logging.debug(f"Arquivo temporário {temp_file_path} removido.")

        if not s3_url:
            return jsonify({'error': 'Erro ao fazer upload do arquivo para o S3'}), 500

        # Chama a função de atualizar o banco de dados com o comprovante
        atualizar_comprovante_upload(codigo_proposta, s3_url)

        return jsonify({'success': True, 'message': 'Comprovante enviado com sucesso'}), 200

    except Exception as e:
        logging.error(f"Erro ao enviar comprovante: {str(e)}")
        return jsonify({'error': f'Erro ao enviar comprovante: {str(e)}'}), 500


@app.route('/atualizar_status_boleto', methods=['POST'])
def atualizar_status_boleto():
    try:
        data = request.get_json()
        codigo_proposta = data.get('codigo_proposta')
        status = data.get('status')

        if not codigo_proposta or not status:
            return jsonify({'error': 'Dados inválidos'}), 400

        atualizar_status_boleto_db(codigo_proposta, status)

        return jsonify({'success': True}), 200

    except Exception as e:
        logging.error(f"Erro ao atualizar status no banco de dados: {e}")
        return jsonify({'error': f'Erro ao atualizar status: {str(e)}'}), 500


@app.route('/api/buscar_boleto_por_codigo', methods=['GET'])
@login_required
def buscar_boleto_por_codigo():
    try:
        codigo_barras = request.args.get('codigo_barras')

        if not codigo_barras:
            return jsonify({'success': False, 'error': 'Código de barras é obrigatório'}), 400

        # Busca boleto por código de barras na tabela boletos_corretor
        boleto = buscar_boleto_por_codigo_barras(codigo_barras)

        if boleto:
            return jsonify({
                'success': True,
                'boleto': boleto
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Nenhum boleto encontrado para este código de barras'
            }), 404

    except Exception as e:
        logging.error(f"Erro ao buscar boleto por código de barras: {e}")
        return jsonify({'success': False, 'error': f'Erro interno: {str(e)}'}), 500


def gerar_protocolo(id_chamado):
    data_atual = datetime.now().strftime('%Y%m%d')
    id_formatado = f"{id_chamado:06d}"
    logging.info(f"{data_atual}{id_formatado}")
    return f"{data_atual}{id_formatado}"


def upload_arquivos_to_s3(file_path, filename):
    s3_client = boto3.client('s3',
                             aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                             aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'))

    bucket_name = os.getenv("S3_BUCKET")

    try:
        logging.debug(f"Preparando para fazer upload do arquivo {filename} para o bucket {bucket_name}")
        logging.debug(f"Caminho do arquivo temporário: {file_path}")

        s3_client.upload_file(file_path, bucket_name, filename)

        # Construir a URL pública do arquivo no S3
        url = f"https://{bucket_name}.s3.amazonaws.com/{filename}"
        logging.info(f"Upload realizado com sucesso: {url}")

        return url

    except NoCredentialsError:
        logging.error("Credenciais não estão disponíveis")
        return None
    except ClientError as e:
        logging.error(f"Erro ao fazer upload do arquivo para o S3: {str(e)}")
        return None
    except Exception as e:
        logging.error(f"Erro inesperado ao tentar fazer upload do arquivo: {str(e)}")
        return None


@app.route('/abertura_ticket', methods=['GET', 'POST'])
def abertura_ticket():
    if "user_id" not in session:
        return redirect(url_for("index"))

    user_id = session["user_id"]

    if request.method == 'POST':
        try:
            # Valida se o Content-Type contém application/json
            if "application/json" not in request.headers.get('Content-Type', ''):
                return jsonify({
                    "error": "Content-Type inválido. Use 'application/json'."
                }), 415

            # Obtém o JSON enviado
            data = request.get_json()
            logging.info(f"JSON recebido: {data}")

            # Extrai os dados do JSON
            setor_id = data.get('setor_id')
            assunto = data.get('assunto')
            description = data.get('description')
            modalidade = data.get('modalidade')
            operadora = data.get('operadora')
            tipo_chamado = data.get('motivo')
            uploaded_files = data.get('uploaded_files', [])

            # Valida campos obrigatórios
            missing_fields = []
            if not setor_id:
                missing_fields.append("setor_id")
            if not assunto:
                missing_fields.append("assunto")
            if tipo_chamado != "Emissão" and not description:
                missing_fields.append("description")

            # Se houver campos obrigatórios faltantes, retorna erro
            if missing_fields:
                field_names = {
                    "setor_id": "Setor",
                    "assunto": "Assunto",
                    "description": "Descrição"
                }
                readable_fields = [field_names.get(field, field) for field in missing_fields]
                return jsonify({
                    "error": True,
                    "missing_fields": missing_fields,
                    "message": f"Preencha os campos obrigatórios: {', '.join(readable_fields)}"
                }), 400

            # Criação do ticket
            ticket_id = add_ticket(user_id, setor_id, assunto, description, modalidade, operadora, tipo_chamado)
            protocolo = gerar_protocolo(ticket_id)
            atualizar_protocolo(ticket_id, protocolo)

            # Associa os arquivos ao ticket, se enviados
            for file in uploaded_files:
                add_ticket_file(ticket_id, file['url'], file['title'])

            # Retorna sucesso com o protocolo gerado
            return jsonify({
                "message": "Chamado criado com sucesso!",
                "protocolo": protocolo
            }), 201

        except Exception as e:
            logging.error(f"Erro ao criar o chamado: {e}")
            return jsonify({
                "error": f"Erro ao criar o chamado: {e}"
            }), 500

    # Para requisições GET, renderiza o template do formulário
    setores = listar_setores()
    return render_template('create_ticket.html', active_page="colaborador", setores=setores)


@app.route('/emissao_ticket', methods=['POST'])
def emissao_ticket():
    if "user_id" not in session:
        return redirect(url_for("index"))

    logging.info(f"Payload recebido no backend: {request.get_json()}")

    user_id = session["user_id"]

    try:
        data = request.get_json()
        setor_id = data.get('setor_id')
        assunto = data.get('assunto')
        description = data.get('description', '')
        modalidade = data.get('modalidade')
        operadora = data.get('operadora')
        tipo_chamado = data.get('motivo')

        # Receber links e títulos dos arquivos diretamente
        uploaded_files = data.get('uploaded_files', [])

        ticket_id = add_ticket(user_id, setor_id, assunto, description, modalidade, operadora, tipo_chamado)
        protocolo = gerar_protocolo(ticket_id)
        atualizar_protocolo(ticket_id, protocolo)

        # Inserção do formulário específico
        if modalidade in ["Adesão", "PF", "PF - Odontológico"]:
            form_id = add_formulario_pfadesao(
                ticket_id,
                data.get("multinotas_pf") == "true",
                data.get("login_multinotas", ""),
                data.get("senha_multinotas", ""),
                int(data.get("cod_corretor", "0")) if data.get("cod_corretor") else 0,
                modalidade,
                operadora,
                data.get("administradora", ""),
                data.get("entidade", ""),
                data.get("vigencia") or None,
                data.get("copart") == "true",
                data.get("aprov_carencia") == "true",
                data.get("odonto") == "true",
                data.get("contato_responsavel", ""),
                data.get("email", ""),
                formatar_telefone(data.get("telefone", "")),
                limpar_valor(data.get("valor_cotacao", "0.0")),
                data.get("info_complementares", ""),
                data.get("tipo_copart", ""),
            )
        elif modalidade in ["PME", "PME - Odontológico"]:
            form_id = add_formulario_porte1(
                ticket_id,
                data.get("multinotas_pme") == "true",
                data.get("login_multinotas", ""),
                data.get("senha_multinotas", ""),
                int(data.get("cod_corretor", "0")) if data.get("cod_corretor") else 0,
                modalidade,
                operadora,
                data.get("nome_empresa", ""),
                data.get("cnpj", ""),
                data.get("copart") == "true",
                data.get("compulsorio") == "true",
                data.get("aprov_carencia") == "true",
                data.get("vigencia") or None,
                data.get("vencimento") or None,
                data.get("odonto") == "true",
                data.get("tabela_regiao", ""),
                data.get("contato_responsavel", ""),
                data.get("email", ""),
                formatar_telefone(data.get("telefone", "")),
                limpar_valor(data.get("valor_cotacao", "0.0")),
                data.get("info_complementares", ""),
                data.get("tipo_copart", "")
            )

        # Inserção dos beneficiários
        for beneficiario in data.get("beneficiarios", []):
            add_beneficiario(
                beneficiario.get("tipo_beneficiario"),
                beneficiario.get("nome"),
                beneficiario.get("plano"),
                beneficiario.get("acomodacao"),
                beneficiario.get("faixa_etaria"),
                beneficiario.get("grau_parentesco"),
                beneficiario.get("civil", ""),
                beneficiario.get("dt_nasci") or None,
                int(beneficiario.get("idade", "0")),
                formatar_telefone(data.get("telefone", "")),
                beneficiario.get("email"),
                form_id if modalidade in ["Adesão", "PF", "PF - Odontológico"] else None,
                form_id if modalidade in ["PME", "PME - Odontológico"] else None
            )

        # Associar os links dos arquivos ao ticket
        for file in uploaded_files:
            # Confere se ambos 'url' e 'title' existem antes de inserir no banco de dados
            if 'url' in file and 'title' in file:
                add_ticket_file(ticket_id, file['url'], file['title'])
            else:
                logging.warning(f"Dados incompletos para o arquivo: {file}")

        return jsonify({"ticket_id": ticket_id, "protocolo": protocolo, "message": "Emissão criada com sucesso."}), 201

    except Exception as e:
        logging.error(f"Erro ao processar a emissão: {e}")
        return jsonify({"error": f"Erro ao processar a emissão: {e}"}), 500


@app.route('/upload_document', methods=['POST'])
def upload_document():
    if "user_id" not in session:
        return jsonify({"error": "Não autorizado"}), 401

    file = request.files.get('file')
    title = request.form.get('title')

    if not file or not title:
        return jsonify({"error": "Arquivo e título são obrigatórios"}), 400

    try:
        # Gera um UUID para garantir que o nome do arquivo seja único
        unique_id = uuid.uuid4()
        extension = file.filename.split('.')[-1]
        filename = f"{title}_{unique_id}.{extension}"

        # Salva o arquivo temporariamente
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            file.save(temp_file.name)

        # Faz o upload do arquivo para o S3 com o nome único
        file_url = upload_arquivos_to_s3(temp_file.name, filename)

        if file_url:
            return jsonify({"url": file_url}), 200
        else:
            return jsonify({"error": "Falha no upload para o S3"}), 500

    except Exception as e:
        logging.error(f"Erro ao fazer upload do arquivo: {e}")
        return jsonify({"error": f"Erro ao fazer upload do arquivo: {e}"}), 500


# Rota para deletar arquivo
@app.route('/delete_file', methods=['POST'])
def delete_file():
    data = request.get_json()
    file_url = data.get('url')
    logging.info(f"Tentando deletar o arquivo com a URL: {file_url}")

    if not file_url:
        logging.error("URL do arquivo não fornecida")
        return jsonify({"error": "URL do arquivo não fornecida"}), 400

    try:
        bucket_name = "intranet-picture-profile"
        delete_image_from_s3(file_url, bucket_name)
        logging.info(f"Arquivo removido com sucesso: {file_url}")
        return jsonify({"message": "Arquivo removido com sucesso"}), 200
    except Exception as e:
        logging.error(f"Erro ao remover o arquivo: {e}")
        return jsonify({"error": "Erro ao remover o arquivo"}), 500


@app.route('/novo_documento_ticket', methods=['POST'])
def novo_documento_ticket():
    if "user_id" not in session:
        return jsonify({"error": "Não autorizado"}), 401

    # Captura os dados do formulário
    ticket_id = request.form.get('ticket_id')
    file = request.files.get('file')
    title = request.form.get('title')

    # Verifica se os campos obrigatórios foram preenchidos
    if not ticket_id or not file or not title:
        return jsonify({"error": "ID do ticket, arquivo e título são obrigatórios!"}), 400

    try:
        # Gera um UUID para garantir que o nome do arquivo seja único
        unique_id = uuid.uuid4()
        extension = file.filename.split('.')[-1]
        filename = f"{title}_{unique_id}.{extension}"

        # Salva o arquivo temporariamente
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            file.save(temp_file.name)

        # Faz o upload do arquivo para o S3 com o nome único
        file_url = upload_arquivos_to_s3(temp_file.name, filename)

        if file_url:
            # Associa o arquivo ao ticket no banco de dados
            add_ticket_file(ticket_id, file_url, title)

            return jsonify({"message": "Documento adicionado com sucesso!", "url": file_url}), 201
        else:
            return jsonify({"error": "Falha no upload para o S3"}), 500

    except Exception as e:
        logging.error(f"Erro ao fazer upload do documento: {e}")
        return jsonify({"error": f"Erro ao fazer upload do documento: {e}"}), 500


@app.route('/gestao/chamados', methods=['GET'])
def gestao_chamados():
    if "user_id" not in session:
        return redirect(url_for("index"))

    setor_id = session.get("setor_id")
    tipo_usuario = session.get("user_type")
    user_id = session["user_id"]

    if tipo_usuario in [1, 7]:
        chamados = listar_todos_chamados()
    elif user_id == 188 or setor_id == 10:
        chamados = listar_chamados_brcall()
    elif setor_id == 12 or user_id in [218, 220]:
        chamados = listar_chamados_rj()
    elif user_id in [110, 149] or setor_id == 13:
        chamados = listar_chamados_luanca()
    elif setor_id == 1:
        chamados = listar_chamados_usina()
    else:
        chamados = listar_chamados_por_setor(setor_id)

    return render_template('gestao_chamados.html', chamados=chamados, active_page="gestao")


@app.route('/api/assistentes_luanca', methods=['GET'])
def get_assistentes_luanca():
    try:
        result = consulta_assistentes_luanca()
        if result:
            return jsonify({
                'status': 'success',
                'data': result
            }), 200
        return jsonify({
            'status': 'error',
            'message': 'Nenhuma execução encontrada'
        }), 404
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/atribuir_assistente/<chamado_id>', methods=['POST'])
def atribuir_assistente(chamado_id):
    try:
        data = request.get_json()
        assistente_id = data.get('assistente_id')

        if not assistente_id:
            return jsonify({'success': False, 'message': 'ID do assistente não fornecido'})

        if atualizar_assistente_chamado(chamado_id, assistente_id):
            return jsonify({'success': True, 'message': 'Assistente atribuído com sucesso'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao atribuir assistente'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Erro: {str(e)}'})


@app.route('/gestao/chamados/detalhes_chamado/<int:chamado_id>', methods=['GET', 'POST'])
def detalhes_chamado(chamado_id):
    # Verificar se o usuário está logado
    if "user_id" not in session:
        return redirect(url_for("index"))

    # Consultar detalhes do chamado
    chamado = consulta_detalhes_chamado(chamado_id)
    tipo_usuario = session.get("user_type")
    user_id = session["user_id"]
    solicitante = chamado.get('user_id')

    # Verificar e definir a data limite
    if chamado:
        if chamado['ultima_interacao']:
            chamado['data_limite'] = chamado['ultima_interacao'] + timedelta(hours=72)
        else:
            chamado['data_limite'] = chamado['created_at'] + timedelta(hours=72)

        # Verificar e atualizar o status e responsável se o chamado está 'Aberto'
        if chamado['status'] == 'Aberto' and solicitante != user_id and tipo_usuario not in [1, 7] and user_id not in [122, 188, 110, 149]: #inserir o ID dos gerentes do Rio
            user_id = session.get("user_id")
            atualizou_responsavel = atualizar_responsavel_chamado(chamado_id, user_id)

            if atualizou_responsavel:
                # Atualizar status para 'Em Tratativa'
                atualizou_status = atualizar_status_chamado(chamado_id, 'Em Tratativa')

                if atualizou_status:
                    flash('Responsável atualizado e status alterado para "Em Tratativa".', 'success')
                else:
                    flash('Erro ao alterar o status do chamado.', 'error')
            else:
                flash('Erro ao atualizar o responsável do chamado.', 'error')

        # Carregar mensagens do chat e arquivos
        mensagens = listar_mensagens_chamado(chamado_id)
        ticket_files = consulta_ticket_file(chamado_id)

        # Acessar o formulário e beneficiários relacionados ao chamado
        formulario = chamado.get('formulario')
        beneficiarios = chamado.get('beneficiarios')

        # Processar a resposta enviada pelo formulário de chat
        if request.method == 'POST':
            nova_resposta = request.form.get('resposta')
            user_id = session.get("user_id")

            try:
                chat_ticket(chamado_id, user_id, nova_resposta)
                flash('Chamado atualizado com sucesso!', 'success')
                return redirect(url_for('detalhes_chamado', chamado_id=chamado_id))
            except Exception as e:
                flash('Erro ao atualizar o chamado.', 'error')
                logging.error(f"Erro ao atualizar o chamado {chamado_id}: {e}")
                return redirect(url_for('detalhes_chamado', chamado_id=chamado_id))

        # Renderizar o template com todas as informações
        return render_template(
            'detalhes_chamado.html',
            chamado=chamado,
            mensagens=mensagens,
            ticket_files=ticket_files,
            formulario=formulario,
            beneficiarios=beneficiarios,
            active_page="gestao",
            ticket_id=chamado_id
        )
    else:
        flash('Chamado não encontrado.', 'error')
        return redirect(url_for('gestao_chamados'))


@app.route('/delete_ticket_file/<int:file_id>', methods=['POST'])
def delete_ticket_file_route(file_id):
    try:
        delete_ticket_file(file_id)
        return jsonify({"message": "Arquivo Deletado com Sucesso!"}), 200
    except Exception as e:
        logging.error(f"Erro ao deletar o arquivo do banco de dados: {e}")
        return jsonify({"error": "Erro ao deletar o arquivo do banco de dados"}), 500


@app.route('/download_all_files/<int:ticket_id>', methods=['GET'])
def download_all_files(ticket_id):
    # Verificar se já existe um arquivo ZIP pronto
    zip_filename = f"ticket_{ticket_id}_files.zip"
    zip_path = os.path.join(tempfile.gettempdir(), zip_filename)

    if os.path.exists(zip_path):
        return send_file(zip_path, as_attachment=True, download_name=zip_filename, mimetype='application/zip')

    # Iniciar processo em background
    thread = threading.Thread(target=create_zip_in_background, args=(ticket_id, zip_path))
    thread.daemon = True
    thread.start()

    # Retornar resposta imediata para o usuário
    return jsonify({
        "status": "processing",
        "message": "Os arquivos estão sendo preparados. Por favor, tente baixar novamente em alguns segundos."
    })


def create_zip_in_background(ticket_id, zip_path):
    try:
        # Obtém a lista de arquivos para o ticket
        files = consulta_ticket_file(ticket_id)

        # Cria arquivo ZIP no disco temporário
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in files:
                file_url = file['url_arquivo']  # URL do S3
                file_name = file['nome_arquivo']

                _, file_extension = os.path.splitext(file_url)
                temp_file_name = f"{file_name}{file_extension}"

                try:
                    response = requests.get(file_url, stream=True)
                    if response.status_code == 200:
                        zipf.writestr(temp_file_name, response.content)
                    else:
                        logging.warning(f"Não foi possível baixar o arquivo {temp_file_name} do S3.")
                except Exception as e:
                    logging.error(f"Erro ao baixar o arquivo {temp_file_name} do S3: {e}")

        logging.info(f"Arquivo ZIP para ticket {ticket_id} criado com sucesso.")
    except Exception as e:
        logging.error(f"Erro ao criar o arquivo ZIP para o chamado {ticket_id}: {e}")


active_timers = {}
def monitorar_inatividade():
    while True:
        time.sleep(120)  # Intervalo de verificação
        for chamado_id, data in list(active_timers.items()):
            # Verifica se não houve atividade
            if not data.get("active"):
                sid = data.get("sid")
                if sid:
                    logging.info(f"Desconectando por inatividade: chamado_id={chamado_id}, sid={sid}")
                    socketio.emit("disconnect_due_to_inactivity", {"message": "Desconectado por inatividade"}, to=sid)
                    del active_timers[chamado_id]
            else:
                # Reseta o estado de atividade para False
                active_timers[chamado_id]["active"] = False

# Iniciar a thread do monitor no início do app
threading.Thread(target=monitorar_inatividade, daemon=True).start()

@socketio.on("message")
def messagereceived(data):
    from datetime import datetime
    import uuid

    chamado_id = data.get("chamado_id")
    user_id = data.get("user_id")
    mensagem = data.get("mensagem")
    message_id = data.get("message_id", str(uuid.uuid4()))
    sid = request.sid  # Captura o `sid` do cliente conectado

    # Validação de chamado_id e user_id
    if chamado_id is None or user_id is None:
        logging.error(f"chamado_id ou user_id está faltando. Chamado: {chamado_id}, User: {user_id}")
        emit("error", {"error": "chamado_id ou user_id está faltando."})
        return

    # Atualizar ou criar timer para o chamado
    active_timers[chamado_id] = {"sid": sid, "active": True}

    # Salvar a mensagem no banco ou realizar a lógica necessária
    try:
        chat_ticket(int(chamado_id), int(user_id), mensagem)

        # Enviar mensagem de confirmação de recebimento apenas para o remetente
        emit("message_received", {
            "message_id": message_id,
            "status": "received"
        }, to=sid)

        # Enviar mensagem para todos na sala do chamado
        response_data = {
            'chamado_id': chamado_id,
            'user_id': user_id,
            'mensagem': mensagem,
            'usuario_nome': session.get('user_name'),
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'profile_image_url': session.get('profile_image_url') or 'default-profile.png',
            'mensagem_id': message_id
        }

        # Envia para a sala específica deste chamado, não para todos
        emit("message", response_data, to=f"chamado_{chamado_id}")

    except Exception as e:
        logging.error(f"Erro ao salvar mensagem para o chamado {chamado_id}: {e}")
        emit("error", {"error": str(e)})


@socketio.on("join_room")
def handle_join_room(data):
    chamado_id = data.get("chamado_id")
    if not chamado_id:
        logging.error("Tentativa de entrar em sala sem chamado_id")
        emit("error", {"error": "chamado_id não informado"})
        return

    room = f"chamado_{chamado_id}"
    join_room(room)
    logging.info(f"Cliente {request.sid} entrou na sala {room}")
    emit("room_joined", {"room": room, "status": "success"})


@socketio.on("disconnect")
def handle_disconnect():
    sid = request.sid  # Captura o `sid` da conexão desconectada
    logging.info(f"Usuário desconectado: sid={sid}")

    # Procurar e remover timers e `sid` associados ao chamado
    for chamado_id, data in list(active_timers.items()):
        if data["sid"] == sid:
            del active_timers[chamado_id]
            logging.info(f"Timer removido para chamado_id={chamado_id}, sid={sid}")


@socketio.on("ping")
def handle_ping():
    sid = request.sid
    logging.info(f"Ping recebido de sid={sid}")

    # Redefinir estado de inatividade
    for chamado_id, data in active_timers.items():
        if data["sid"] == sid:
            active_timers[chamado_id]["active"] = True
            logging.info(f"Inatividade resetada via ping para chamado_id={chamado_id}, sid={sid}")

    emit("pong", {"status": "ok"})


@socketio.on("mark_as_read")
def marcar_mensagem_como_visualizada(data):
    chamado_id = data.get("chamado_id")
    user_id = session.get("user_id")

    # Validação de chamado_id e user_id
    if chamado_id is None or user_id is None:
        logging.warning(f"Falha ao marcar como visualizado: chamado_id ou user_id ausente. Chamado: {chamado_id}, User: {user_id}")
        emit("error", {"error": "chamado_id ou user_id está ausente."})
        return

    try:
        mensagens_visualizadas = atualizar_visualizacao_mensagens(int(chamado_id), int(user_id))

        emit("mensagens_lidas", {"chamado_id": chamado_id, "user_id": user_id}, broadcast=True)

    except Exception as e:
        emit("error", {"error": str(e)})


def enviar_email_status_chamado(destinatario, assunto, protocolo):
    remetente = "<EMAIL>"
    saudacao = gerar_saudacao()
    corpo_email = (
        f"Olá, {saudacao}!\n\n"
        f"Seu chamado de protocolo {protocolo} foi atualizado. 🚀\n"
        "Acesse a intranet para conferir os detalhes e acompanhar as próximas etapas!\n\n"
        "Atenciosamente,\n"
        "Equipe BrazilHealth"
    )

    # Criando o cliente SES
    ses_client = boto3.client(
        "ses",
        region_name=SES_REGION,
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    )

    try:
        # Enviando o e-mail usando Amazon SES
        response = ses_client.send_email(
            Source=remetente,
            Destination={
                "ToAddresses": [destinatario],  # Lista de destinatários
            },
            Message={
                "Subject": {
                    "Data": f"Novidades no Seu Chamado: {assunto}"
                },
                "Body": {
                    "Text": {
                        "Data": corpo_email
                    }
                },
            }
        )
        logging.info(f"E-mail enviado para {destinatario}. Response: {response}")
        return True
    except ClientError as e:
        logging.error(f"Erro ao enviar e-mail para {destinatario}: {e.response['Error']['Message']}")
        return False


@app.route('/mudar_status_chamado/<int:chamado_id>', methods=['POST'])
def mudar_status_chamado(chamado_id):
    if "user_id" not in session:
        return jsonify({"error": "Usuário não autenticado!"}), 403

    send_chamado = consulta_detalhes_chamado(chamado_id)

    novo_status = request.form.get('status')
    assunto = send_chamado.get('assunto')
    protocolo = send_chamado.get('protocolo')
    user_id = request.form.get('user_id')

    # Log dos dados recebidos para depuração
    logging.info(f"Dados recebidos: status={novo_status}, assunto={assunto}, protocolo={protocolo}, user_id={user_id}")

    if not novo_status or not assunto or not protocolo or not user_id:
        return jsonify({"error": "Dados insuficientes para atualizar o chamado."}), 400

    try:
        # Atualiza o status do chamado
        atualizar_status_chamado(chamado_id, novo_status)
        record_log(user_id, f"Atualizou o status do chamado {chamado_id} para {novo_status}")

        # Consulta os detalhes do usuário
        user_data = consulta_geral(user_id)
        logging.info(f"Dados do usuário ({user_id}): {user_data}")

        # Acessando o email diretamente pela posição na tupla
        email_usuario = user_data[5] if user_data else None
        if email_usuario:
            enviar_email_status_chamado(email_usuario, assunto, protocolo)
        else:
            logging.warning(f"Usuário {user_id} não tem e-mail cadastrado.")

        return jsonify({'success': True, 'message': "Status do chamado atualizado com sucesso!"}), 200
    except Exception as e:
        logging.error(f"Erro ao atualizar o status do chamado {chamado_id}: {e}")
        return jsonify({'success': False, 'message': "Erro ao atualizar o status do chamado."}), 500


@app.route('/mudar_sub_status_chamado/<int:chamado_id>', methods=['POST'])
def mudar_sub_status_chamado(chamado_id):
    if "user_id" not in session:
        return jsonify({"error": "Usuário não autenticado!"}), 403

    novo_status = request.form.get('status')
    novo_sub_status = request.form.get('sub_status')
    user_id = session.get("user_id")

    if not novo_status or not novo_sub_status:
        return jsonify({"error": "Status e sub-status são obrigatórios!"}), 400

    try:
        sucesso = atualizar_substatus_chamado(novo_status, novo_sub_status, user_id, chamado_id)

        record_log(user_id, f"Atualizou o substatus do chamado {chamado_id} para {novo_sub_status}")

        if sucesso:
            return jsonify({"success": True, "message": "Substatus atualizado com sucesso!"}), 200
        else:
            return jsonify({"success": False, "message": "Nenhuma alteração foi feita."}), 200

    except Exception as e:
        logging.error(f"Erro ao atualizar o substatus do chamado {chamado_id}: {e}")
        return jsonify({"success": False, "message": "Erro ao atualizar o substatus do chamado."}), 500


@app.route('/mudar_propC_chamado/<int:chamado_id>', methods=['POST'])
def mudar_propC_chamado(chamado_id):
    if "user_id" not in session:
        return jsonify({"error": "Usuário não autenticado!"}), 403

    novo_proposta = request.form.get('status')

    try:
        novo_proposta = bool(int(novo_proposta))
        atualizar_propC_chamado(chamado_id, novo_proposta)
        return jsonify({'success': True, 'message': "Status do chamado atualizado com sucesso!"}), 200
    except Exception as e:
        logging.error(f"Erro ao atualizar o status do chamado {chamado_id}: {e}")
        return jsonify({'success': False, 'message': "Erro ao atualizar o status do chamado."}), 500


@app.route('/mudar_cod_proposta/<int:chamado_id>', methods=['POST'])
def mudar_cod_proposta(chamado_id):
    if "user_id" not in session:
        return jsonify({"error": "Usuário não autenticado!"}), 403

    cod_proposta = request.form.get('cod_proposta')
    chamado = consulta_detalhes_chamado(chamado_id)
    modalidade = chamado.get('modalidade')

    if not cod_proposta:
        return jsonify({"success": False, "message": "Código da proposta é obrigatório."}), 400

    try:
        if modalidade in ["PME", "PME - Odontológico"]:
            atualizar_formulario_pme(cod_proposta, chamado_id)
        else:
            atualizar_formulario_pf(cod_proposta, chamado_id)

        return jsonify({'success': True, 'message': "Proposta atualizada com sucesso!"}), 200
    except Exception as e:
        logging.error(f"Erro ao atualizar o código da proposta {chamado_id}: {e}")
        return jsonify({'success': False, 'message': "Erro ao atualizar o código da proposta."}), 500


@app.route('/colaborador/chamados', methods=['GET'])
def chamados_colaborador():
    if "user_id" not in session:
        return redirect(url_for("index"))

    user_id = session.get("user_id")

    if user_id in [67, 55]:
        chamados = listar_chamados_franquia(user_id)
    elif user_id in [207, 218, 220, 193]:
        chamados = listar_chamados_rj_colaborador(user_id)
    else:
        chamados = listar_chamados_por_user(user_id)

    return render_template('chamados_colaborador.html', chamados=chamados, active_page="colaborador")


@app.route('/colaborador/chamados/detalhes/<int:chamado_id>', methods=['GET', 'POST'])
def detalhes_chamado_usuario(chamado_id):
    # Verificar se o usuário está logado
    if "user_id" not in session:
        return redirect(url_for("index"))

    # Consultar detalhes do chamado
    chamado = consulta_detalhes_chamado(chamado_id)

    # Verificar e definir a data limite
    if chamado:
        if chamado['ultima_interacao']:
            chamado['data_limite'] = chamado['ultima_interacao'] + timedelta(hours=72)
        else:
            chamado['data_limite'] = chamado['created_at'] + timedelta(hours=72)

        # Carregar mensagens do chat e arquivos
        mensagens = listar_mensagens_chamado(chamado_id)
        ticket_files = consulta_ticket_file(chamado_id)

        # Acessar o formulário e beneficiários relacionados ao chamado
        formulario = chamado.get('formulario')
        beneficiarios = chamado.get('beneficiarios')

        # Processar a resposta enviada pelo formulário de chat
        if request.method == 'POST':
            nova_resposta = request.form.get('resposta')
            user_id = session.get("user_id")

            try:
                chat_ticket(chamado_id, user_id, nova_resposta)
                flash('Chamado atualizado com sucesso!', 'success')
                return redirect(url_for('detalhes_chamado', chamado_id=chamado_id))
            except Exception as e:
                flash('Erro ao atualizar o chamado.', 'error')
                logging.error(f"Erro ao atualizar o chamado {chamado_id}: {e}")
                return redirect(url_for('detalhes_chamado', chamado_id=chamado_id))

        # Renderizar o template com todas as informações
        return render_template(
            'detalhes_chamado_usuario.html',
            chamado=chamado,
            mensagens=mensagens,
            ticket_files=ticket_files,
            formulario=formulario,
            beneficiarios=beneficiarios,
            active_page="colaborador",
            ticket_id=chamado_id
        )
    else:
        flash('Chamado não encontrado.', 'error')
        return redirect(url_for('gestao_chamados'))


# Rota para a página da AccountTech
@app.route('/lp/condicao-exclusiva')
def lp_accounttech():
    return render_template('lpaccounttech.html')


# Rota para processar o formulário de contato da AccountTech
@app.route('/submit-form', methods=['POST'])
def submit_accounttech_form():
    try:
        data = request.get_json()
        logging.info(f"Formulário recebido: {data}")

        nome = data.get('fullName')
        email = data.get('email')
        telefone = data.get('phone')
        hascnpj = data.get('hasCnpj')
        plano_atual = data.get('currentPlan')
        cidade = data.get('city')
        periodo_contato = data.get('contactPreference')

        upload_formlpaccount(nome, email, telefone, hascnpj, plano_atual, cidade, periodo_contato)

        return jsonify({
            'status': 'success',
            'message': 'Obrigado! Entraremos em contato em breve.'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': 'Erro ao processar o formulário. Por favor, tente novamente.'
        }), 500


@app.route('/contas_home')
@login_required
def contas_home():
    return render_template('contas_home.html')


@app.route('/contas_fornecedores', methods=['GET'])
@login_required
def contas_fornecedores():
    try:
        fornecedores = get_all_fornecedores()
        return render_template('contas_fornecedores.html', fornecedores=fornecedores)
    except Exception as e:
        logging.error(f"Erro ao buscar fornecedores: {e}")
        flash('Erro ao carregar fornecedores', 'danger')
        return render_template('contas_fornecedores.html', fornecedores=[])


@app.route('/submit_fornecedor', methods=['POST'])
def submit_fornecedor():
    try:
        # Captura os dados do formulário
        nome = request.form.get('nome')
        cnpj_cpf = request.form.get('cnpj_cpf')
        endereco = request.form.get('endereco')
        telefone = request.form.get('telefone')
        email = request.form.get('email')
        categoria = request.form.get('categoria')
        site = request.form.get('site')
        observacoes = request.form.get('observacoes')

        # Adiciona o fornecedor no banco
        add_fornecedor(nome, cnpj_cpf, endereco, telefone, email, categoria, site, observacoes)

        user_id = session.get("user_id")

        record_log(user_id, "Cadastrou um fornecedor")

        # Retorna mensagem de sucesso como JSON
        return jsonify({'status': 'success', 'message': f'Fornecedor {nome} cadastrado com sucesso!'})
    except ValueError as e:
        return jsonify({'status': 'error', 'message': str(e)})
    except Exception as e:
        logging.error(f"Erro ao cadastrar fornecedor: {e}")
        return jsonify({'status': 'error', 'message': 'Erro ao cadastrar fornecedor.'})


@app.route('/get_fornecedores')
def get_fornecedores():
    fornecedores = get_all_fornecedores()
    logging.debug(f"Fornecedores retornados: {fornecedores}")
    return jsonify(fornecedores)


@app.route('/get_fornecedores/<int:fornecedor_id>', methods=['GET'])
@login_required
def get_fornecedoresId(fornecedor_id):
    fornecedores = get_fornecedor_id(fornecedor_id)
    return jsonify(fornecedores)


@app.route('/update_fornecedor/<int:fornecedor_id>', methods=['PATCH'])
def update_fornecedor(fornecedor_id):
    try:
        dados = request.json
        if not dados:
            return jsonify({"error": "Nenhum dado fornecido para atualização"}), 400

        nome = dados.get("nome")
        cnpj_cpf = dados.get("cnpj_cpf")
        endereco = dados.get("endereco")
        telefone = dados.get("telefone")
        email = dados.get("email")
        categoria = dados.get("categoria")
        site = dados.get("site")
        observacoes = dados.get("observacoes")

        atualizar_fornecedor(fornecedor_id, nome, cnpj_cpf, endereco, telefone, email, categoria, site, observacoes)

        user_id = session.get("user_id")

        record_log(user_id, "Atualizou um fornecedor")

        return jsonify({"message": "Conta atualizada com sucesso!"}), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 404
    except Exception as e:
        logging.error(f"Erro ao atualizar a conta: {e}")
        return jsonify({"error": "Erro ao atualizar a conta"}), 500


@app.route('/contas_funcionarios', methods=['GET'])
@login_required
def contas_funcionarios():

    return render_template('contas_funcionarios.html')


@app.route('/get_funcionarios')
@login_required
def get_funcionarios():
    try:
        funcionarios = get_all_funcionario()
        return jsonify(funcionarios), 200
    except Exception as e:
        logging.error(f"Erro ao buscar o funcionario: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/submit_funcionarios', methods=['POST'])
def submit_funcionarios():
    try:
        nome = request.form.get('nome')
        cpf = request.form.get('cpf')
        telefone = request.form.get('telefone')
        email = request.form.get('email')

        add_funcionario(nome, cpf, telefone, email)

        user_id = session.get("user_id")

        record_log(user_id, "Cadastrou um funcionário")

        return jsonify({'status': 'success', 'message': 'Funcionário cadastrado com sucesso!'})

    except ValueError as e:
        return jsonify({'status': 'error', 'message': str(e)})
    except Exception as e:
        logging.error(f"Erro ao cadastrar funcionarios: {e}")
        return jsonify({'status': 'error', 'message': 'Erro ao cadastrar funcionarios.'})


@app.route('/get_funcionarios/<int:funcionario_id>', methods=['GET'])
@login_required
def get_funcionariosId(funcionario_id):
    funcionarios = get_funcionario_id(funcionario_id)
    return jsonify(funcionarios)


@app.route('/update_funcionarios/<int:funcionario_id>', methods=['PUT'])
def update_funcionarios(funcionario_id):
    try:
        dados = request.json
        if not dados:
            return jsonify({"error": "Nenhum dado fornecido para atualização"}), 400

        nome = dados.get("nome")
        cpf = dados.get("cpf")
        telefone = dados.get("telefone")
        email = dados.get("email")

        atualizar_funcionario(funcionario_id, nome, cpf, telefone, email)

        user_id = session.get("user_id")

        record_log(user_id, "Atualizou um funcionário")

        return jsonify({"message": "Funcionário atualizado com sucesso!"}), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 404
    except Exception as e:
        logging.error(f"Erro ao atualizar o funcionário: {e}")
        return jsonify({"error": "Erro ao atualizar o funcionário"}), 500


@app.route('/contas_dadosbancarios')
@login_required
def contas_dadosbancarios():
    return render_template('contas_dadosbancarios.html')


@app.route('/submit_dadosbancarios', methods=['POST'])
def submit_dadosbancarios():
    try:
        nome_banco = request.form.get('nome_banco')
        numero_banco = request.form.get('numero_banco')
        agencia = request.form.get('agencia_banco')
        digito_agencia = request.form.get('digito_agencia')
        tipo_conta = request.form.get('tipo_conta')
        tipo_benef = request.form.get("tipo_beneficiario")
        fornecedor_id = request.form.get('fornecedor')
        funcionario_id = request.form.get("funcionario_id")
        digito_conta = request.form.get('digito_conta')
        conta_corrente = request.form.get('numero_conta') if tipo_conta == 'corrente' else None
        conta_poupanca = request.form.get('numero_conta') if tipo_conta == 'poupanca' else None
        conta_salario = request.form.get('numero_conta') if tipo_conta == 'salario' else None

        # Validação básica
        if not all([nome_banco, numero_banco, agencia, digito_agencia, tipo_conta]):
            return jsonify({'status': 'error', 'message': 'Todos os campos são obrigatórios.'})

        if tipo_benef == 'fornecedor':
            add_dados_bancarios(
                nome_banco, numero_banco, agencia, digito_agencia,
                tipo_conta, conta_corrente, conta_poupanca, conta_salario, digito_conta, fornecedor_id
            )
        elif tipo_benef == 'funcionario':
            add_dados_funcionario(
                nome_banco, numero_banco, agencia, digito_agencia,
                tipo_conta, conta_corrente, conta_poupanca, conta_salario, digito_conta, funcionario_id
            )

        user_id = session.get("user_id")

        record_log(user_id, "Cadastrou dados bancários")

        return jsonify({'status': 'success', 'message': 'Dados bancários cadastrados com sucesso!'})
    except Exception as e:
        logging.error(f"Erro ao cadastrar dados bancários: {e}")
        return jsonify({'status': 'error', 'message': 'Erro inesperado ao cadastrar dados bancários.'})


@app.route('/get_dadosbancarios')
def get_dadosbancarios():
    dados_bancarios = get_all_dados_bancarios()
    logging.debug(f"Dados bancários retornados: {dados_bancarios}")
    return jsonify(dados_bancarios)


@app.route('/get_dadosbancarios_id/<int:dados_bancarios_id>', methods=['GET'])
@login_required
def get_dadosbancarios_id(dados_bancarios_id):
    dados_bancarios = get_dados_bancarios_id(dados_bancarios_id)
    return jsonify(dados_bancarios)


@app.route('/update_dadosbancarios/<int:dados_bancarios_id>', methods=['PUT'])
@login_required
def update_dadosbancarios(dados_bancarios_id):
    try:
        dados = request.json
        if not dados:
            return jsonify({"error": "Nenhum dado fornecido para atualização"}), 400

        nome_banco = dados.get("nome_banco")
        numero_banco = dados.get("numero_banco")
        agencia = dados.get("agencia")
        digito_agencia = dados.get("digito_agencia")
        conta = dados.get("conta")
        digito_conta = dados.get("digito_conta")
        tipo_conta = dados.get("tipo_conta")

        atualizar_dados_bancarios(
            dados_bancarios_id, nome_banco, numero_banco, agencia, digito_agencia,
            conta, digito_conta, tipo_conta)

        user_id = session.get("user_id")

        record_log(user_id, "Atualizou dados bancários")

        return jsonify({"message": "Dados bancários atualizados com sucesso!"}), 200
    except Exception as e:
        logging.error(f"Erro ao atualizar dados bancários: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/delete_dadosbancarios/<int:dados_bancarios_id>', methods=['DELETE'])
def delete_dadosbancarios(dados_bancarios_id):
    try:
        delete_dados_bancarios(dados_bancarios_id)

        user_id = session.get("user_id")
        record_log(user_id, "Deletou dados bancários")

        return jsonify({"message": "Dados bancários deletados com sucesso!"}), 200
    except Exception as e:
        logging.error(f"Erro ao deletar dados bancários: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/contas_vencimentos')
@login_required
def contas_vencimentos():
    return render_template('contas_vencimentos.html')


@app.route('/get_vencimentos')
def get_vencimentos():
    from datetime import date

    user_id = session.get("user_id")

    try:
        if user_id in [110]:
            contas = get_all_contas_calendario_luanca()
        else:
            contas = get_all_contas_calendario()

        eventos = []
        for c in contas:
            # Se a data_vencimento for datetime ou date, converta para string 'YYYY-MM-DD'
            data_str = None
            if isinstance(c["data_vencimento"], (datetime, date)):
                data_str = c["data_vencimento"].strftime('%Y-%m-%d')
            elif isinstance(c["data_vencimento"], str):
                # Converte a data de 'DD/MM/YYYY' para 'YYYY-MM-DD'
                data_venc = datetime.strptime(c["data_vencimento"], '%d/%m/%Y')
                data_str = data_venc.strftime('%Y-%m-%d')

            # Título no calendário pode ser o nome da conta, ou algo que vc quiser
            evento = {
                "title": c["nome_conta"],  # ou f"{c['empresa_pagadora']} - {c['nome_conta']}"
                "start": data_str,
                # Você pode incluir outras chaves, ex. "id": c["id"]
            }
            eventos.append(evento)

        # Retorna o array de eventos em JSON
        return jsonify(eventos)
    except Exception as e:
        logging.error(f"Erro ao buscar vencimentos: {e}")
        # Pode retornar um array vazio ou alguma msg de erro
        return jsonify([])


@app.route('/contas_empresas', methods=['GET'])
@login_required
def contas_empresas():
    empresas = get_all_empresas()  # Busca todas as empresas
    return render_template('contas_empresas.html', empresas=empresas)


@app.route('/submit_empresa', methods=['POST'])
def submit_empresa():
    data = request.form
    try:
        add_empresa(
            nome_empresa=data.get('empresa'),
            cnpj=data.get('cnpjEmpresa'),
            endereco=data.get('enderecoEmpresa'),
            telefone=data.get('telefoneEmpresa'),
            email=data.get('emailEmpresa'),
            observacoes=data.get('observacoes')
        )

        user_id = session.get("user_id")
        record_log(user_id, "Cadastrou uma empresa do grupo")

        return jsonify({"status": "success", "message": "Empresa cadastrada com sucesso."})
    except Exception as e:
        return jsonify({"status": "error", "message": f"Erro ao cadastrar empresa: {e}"})


@app.route('/get_empresas', methods=['GET'])
@login_required
def get_empresas():
    try:
        empresas = get_all_empresas()  # Lista de dicionários
        return jsonify(empresas)  # Retorna o JSON correto
    except Exception as e:
        return jsonify({"status": "error", "message": f"Erro ao buscar empresas: {e}"})


@app.route('/get_empresas/<int:empresa_id>', methods=['GET'])
@login_required
def get_empresas_id(empresa_id):
    empresa = get_empresa_by_id(empresa_id)
    return jsonify(empresa)


@app.route('/update_empresa/<int:empresa_id>', methods=['PUT'])
@login_required
def update_empresa(empresa_id):
    try:
        data = request.json
        if not data:
            return jsonify({"error": "Nenhum dado fornecido para atualização"}), 400

        nome_empresa = data.get("nome_empresa")
        cnpj = data.get("cnpj")
        endereco = data.get("endereco")
        telefone = data.get("telefone")
        email = data.get("email")
        observacoes = data.get("observacoes")

        atualizar_empresa(empresa_id, nome_empresa, cnpj, endereco, telefone, email, observacoes)

        user_id = session.get("user_id")
        record_log(user_id, "Atualizou uma empresa do grupo")

        return jsonify({"message": "Empresa atualizada com sucesso!"}), 200
    except Exception as e:
        logging.error(f"Erro ao atualizar empresa: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/contas_cadastro', methods=['GET'])
@login_required
def contas_cadastro():
    return render_template('contas_cadastro.html')


@app.route('/get_contas_por_mes', methods=['GET'])
@login_required
def get_contas_por_mes_route():
    start = request.args.get('start')
    end = request.args.get('end')
    try:
        user_id = session.get("user_id")

        if user_id in [110]:
            contas = get_luanca_contas(start, end)
        else:
            contas = get_contas_por_mes(start, end)
        return jsonify(contas), 200
    except Exception as e:
        logging.error(f"Erro ao buscar contas por período: {e}")
        return jsonify({'error': str(e)}), 500


def extrair_codigo_boleto(pdf_path, usar_ocr=False):
    try:
        with pdfplumber.open(pdf_path) as pdf:
            texto = ""
            for page in pdf.pages:
                page_text = page.extract_text() or ""
                texto += page_text + "\n"

        # 1.1) Regex para boletos bancários (47 dígitos)
        pattern_47 = re.compile(
            r'(\d{5}[\.\s-]?\d{5}[\.\s-]?\d{5}[\.\s-]?\d{6}[\.\s-]?\d{5}[\.\s-]?\d{6}[\.\s-]?\d[\.\s-]?\d{14})'
        )

        # 1.2) Regex para concessionárias (48 dígitos).
        pattern_48 = re.compile(
            r'(?:\d{11}[\.\s-]?\d){4}'
        )

        # 1.3) Tenta primeiro o padrão de 47
        match_47 = pattern_47.search(texto)
        if match_47:
            cod_sujo = match_47.group()
            somente_digitos = re.sub(r'\D', '', cod_sujo)
            if len(somente_digitos) == 47:
                return somente_digitos

        # 1.4) Tenta o padrão de 48
        match_48 = pattern_48.search(texto)
        if match_48:
            cod_sujo = match_48.group()
            somente_digitos = re.sub(r'\D', '', cod_sujo)
            if len(somente_digitos) == 48:
                return somente_digitos

        # 1.5) Se não achou nos padrões específicos, usa um regex "fallback" amplo
        fallback = re.search(r'(?:\D*\d\D*){47,48}', texto)
        if fallback:
            cod_sujo = fallback.group()
            somente_digitos = re.sub(r'\D', '', cod_sujo)
            if len(somente_digitos) in [47, 48]:
                return somente_digitos

    except Exception as e:
        print("Erro ao extrair texto do PDF:", e)

    # ----------------------------------------------------
    # 2) Se não achou no texto, converte PDF -> Imagens e tenta ler barcodes
    # ----------------------------------------------------
    try:
        pages = convert_from_path(pdf_path, dpi=300)
        for page in pages:
            resultados = zbar_decode(page)
            for r in resultados:
                valor = r.data.decode('utf-8')
                somente_digitos = re.sub(r'\D', '', valor)
                if len(somente_digitos) in [47, 48]:
                    return somente_digitos
    except Exception as e:
        print("Erro ao converter PDF em imagens ou ler barcode:", e)

    # ----------------------------------------------------
    # 3) (Opcional) Se usar_ocr=True, tenta extrair via OCR
    # ----------------------------------------------------
    if usar_ocr:
        try:
            import pytesseract
            # Reaproveitar as regex definidas acima
            pages_ocr = convert_from_path(pdf_path, dpi=300)
            for page_img in pages_ocr:
                ocr_text = pytesseract.image_to_string(page_img, lang='por')
                if not ocr_text:
                    continue

                # Tenta primeiro 47
                match_47_ocr = pattern_47.search(ocr_text)
                if match_47_ocr:
                    somente_digitos = re.sub(r'\D', '', match_47_ocr.group())
                    if len(somente_digitos) == 47:
                        return somente_digitos

                # Tenta 48
                match_48_ocr = pattern_48.search(ocr_text)
                if match_48_ocr:
                    somente_digitos = re.sub(r'\D', '', match_48_ocr.group())
                    if len(somente_digitos) == 48:
                        return somente_digitos

                # Fallback regex amplo no OCR
                fallback_ocr = re.search(r'(?:\D*\d\D*){47,48}', ocr_text)
                if fallback_ocr:
                    somente_digitos = re.sub(r'\D', '', fallback_ocr.group())
                    if len(somente_digitos) in [47, 48]:
                        return somente_digitos

        except Exception as e:
            print("Erro ao fazer OCR:", e)

    # Se não achou em nenhum método:
    return None


@app.route('/api/extrair-codigo-barras', methods=['POST'])
def extrair_codigo_barras():
    if 'arquivo_boleto' not in request.files:
        return jsonify({'codigo_barras': None, 'error': 'Nenhum arquivo enviado.'}), 400

    file = request.files['arquivo_boleto']

    # 🔹 Criando caminho temporário seguro
    temp_dir = tempfile.gettempdir()
    safe_filename = file.filename.replace(" ", "_")
    temp_path = os.path.join(temp_dir, safe_filename)

    # Salva o arquivo no caminho temporário
    file.save(temp_path)

    try:
        codigo = extrair_codigo_boleto(temp_path)
        # Remove o arquivo temporário após o uso
        os.remove(temp_path)

        return jsonify({'codigo_barras': codigo}), 200
    except Exception as e:
        # Em caso de erro, tente remover o arquivo temporário (se ainda existir)
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return jsonify({'codigo_barras': None, 'error': str(e)}), 500


@app.route('/submit_conta', methods=['POST'])
def submit_conta():
    try:
        # 1) Recebe dados do form
        nome_conta = request.form.get('nome_conta')
        empresa_pagadora_id = request.form.get('empresa_pagadora')
        fornecedor_id = request.form.get('fornecedor_id')
        funcionario_id = request.form.get("funcionario_id")
        tipo_benef = request.form.get("tipo_beneficiario")
        valor_str = request.form.get('valor', '0')
        data_venc_str = request.form.get('data_vencimento')
        tipo_conta = request.form.get('tipo_conta')
        codigo_barras = request.form.get('codigo_barras') or None
        recorrencia_str = request.form.get('recorrencia') or ''
        quantidade_recorrencia_str = request.form.get(
            'quantidade_recorrencia', '1')
        if not quantidade_recorrencia_str.strip():
            quantidade_recorrencia_str = '1'
        quantidade_recorrencia = int(quantidade_recorrencia_str)
        observacoes = request.form.get('observacoes') or None
        file = request.files.get('upload_boleto')
        boleto_filename = None
        user_id = session.get("user_id")

        if file:
            # 🔹 Criando caminho temporário seguro
            temp_dir = tempfile.gettempdir()
            # Remove espaços do nome do arquivo
            safe_filename = file.filename.replace(" ", "_")
            temp_path = os.path.join(temp_dir, safe_filename)

            # 🔹 Salvando o arquivo temporariamente
            file.save(temp_path)
            logging.info(f"Arquivo salvo temporariamente: {temp_path}")

            # 🔹 Upload para o S3
            upload_response = upload_pagamentos_s3(temp_path, file.filename)

            if upload_response["success"]:
                boleto_filename = upload_response["url"]

            # 🔹 Removendo arquivo temporário
            os.remove(temp_path)

        # 2) Normaliza a string de recorrência (remove espaços e converte para lower)
        recorrencia_val = recorrencia_str.strip()

        # 3) Converte valor de "R$ 290,00" ou "R$ 290.00" para float
        valor_str = valor_str.strip().replace('R$', '').replace(' ', '')
        # Trocamos vírgula por ponto. Não removemos todos os pontos!
        valor_str = valor_str.replace(',', '.')
        valor_final = float(valor_str) if valor_str else 0

        # 4) Converte 'quantidade_recorrencia' para int
        quantidade_recorrencia_str = request.form.get(
            'quantidade_recorrencia', '1')
        if not quantidade_recorrencia_str.strip():
            quantidade_recorrencia_str = '1'
        quantidade_recorrencia = int(quantidade_recorrencia_str)

        # 5) Converte data para datetime (suporta "DD/MM/YYYY" e "YYYY-MM-DD")
        if data_venc_str:
            if '-' in data_venc_str:
                data_venc_datetime = datetime.strptime(
                    data_venc_str, '%Y-%m-%d')
            else:
                data_venc_datetime = datetime.strptime(
                    data_venc_str, '%d/%m/%Y')
        else:
            return jsonify(status='error', message="Data de vencimento não informada."), 400

        # 6) Chama a função que cria a conta e as parcelas
        if tipo_benef == 'fornecedor':
            add_conta(
                nome_conta=nome_conta,
                empresa_pagadora_id=empresa_pagadora_id,
                fornecedor_id=fornecedor_id,
                valor=valor_final,
                data_vencimento=data_venc_datetime,
                codigo_barras=codigo_barras,
                recorrencia=recorrencia_val,
                observacoes=observacoes,
                boleto_filename=boleto_filename,
                quantidade_recorrencia=quantidade_recorrencia,
                tipo_conta=tipo_conta
            )

            record_log(user_id, "Cadastrou uma nova conta para pagamento")

        elif tipo_benef == 'funcionario':
            add_conta_funcionario(
                nome_conta=nome_conta,
                empresa_pagadora_id=empresa_pagadora_id,
                funcionario_id=funcionario_id,
                valor=valor_final,
                data_vencimento=data_venc_datetime,
                codigo_barras=codigo_barras,
                recorrencia=recorrencia_val,
                observacoes=observacoes,
                boleto_filename=boleto_filename,
                quantidade_recorrencia=quantidade_recorrencia,
                tipo_conta=tipo_conta
            )

            record_log(user_id, "Cadastrou uma nova conta para pagamento")

        return jsonify(status='success', message="Conta(s) cadastrada(s) com sucesso!"), 200

    except Exception as e:
        logging.error(f"Erro ao cadastrar conta: {e}")
        return jsonify(status='error', message="Ocorreu um erro ao cadastrar conta."), 500


@app.route('/get_conta/<int:conta_id>/<int:recorrencia_id>', methods=['GET'])
def get_conta(conta_id, recorrencia_id):
    try:
        conta = get_conta_by_id(conta_id, recorrencia_id)
        if conta:
            return jsonify(conta)
        else:
            return jsonify({"error": "Conta ou recorrência não encontrada"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/delete_conta/<int:conta_id>', methods=['DELETE'])
def delete_conta_route(conta_id):
    try:
        delete_conta(conta_id)

        user_id = session.get("user_id")
        record_log(user_id, "Deletou uma conta para pagamento")

        return jsonify({"message": "Conta Deletada com Sucesso!"}), 200
    except Exception as e:
        logging.error(f"Erro ao deletar a conta do banco de dados: {e}")
        return jsonify({"error": "Erro ao deletar a conta do banco de dados"}), 500


@app.route('/update_conta/<int:conta_id>', methods=['PATCH'])
def update_conta_route(conta_id):
    try:
        valor_str = request.form.get('valor')
        data_vencimento = request.form.get('data_vencimento')
        codigo_barras = request.form.get('codigo_barras')
        observacoes = request.form.get('observacoes')
        tipo_conta = request.form.get('tipo_conta')
        recorrencia_id = request.form.get('recorrencia_id')
        file = request.files.get('arquivo_boleto')
        boleto_filename = None

        # Processar o valor
        if valor_str:
            valor_str = valor_str.replace("R$", "").replace(".", "").replace(",", ".")
            valor = float(valor_str)
        else:
            valor = None

        # Converter data de vencimento se necessário
        if data_vencimento and "/" in data_vencimento:
            data_parts = data_vencimento.split("/")
            data_vencimento = f"{data_parts[2]}-{data_parts[1]}-{data_parts[0]}"

        # Upload do boleto (igual submit_conta)
        if file:
            import tempfile, os
            temp_dir = tempfile.gettempdir()
            safe_filename = file.filename.replace(" ", "_")
            temp_path = os.path.join(temp_dir, safe_filename)
            file.save(temp_path)
            upload_response = upload_pagamentos_s3(temp_path, file.filename)
            if upload_response and upload_response.get("success"):
                boleto_filename = upload_response["url"]
            os.remove(temp_path)

        # Atualizar a conta
        update_contas(
            conta_id=conta_id,
            valor=valor,
            data_vencimento=data_vencimento,
            codigo_barras=codigo_barras,
            observacoes=observacoes,
            tipo_conta=tipo_conta,
            recorrencia_id=recorrencia_id,
            boleto_filename=boleto_filename
        )

        user_id = session.get("user_id")
        record_log(user_id, "Atualizou uma conta para pagamento")

        return jsonify({'message': 'Conta atualizada com sucesso!'})
    except Exception as e:
        import logging
        logging.error(f"Erro ao atualizar conta: {e}")
        return jsonify({'error': 'Ocorreu um erro ao atualizar a conta.'}), 500


@app.route('/contas/recorrentes', methods=['GET'])
@login_required
def contas_recorrentes():
    user_id = session.get("user_id")

    if user_id in [110]:
        recorrencias = get_contas_recorrentes_luanca()
    else:
        recorrencias = consulta_contas_recorrentes()

    return render_template('contas_recorrencia.html', recorrencias=recorrencias)


@app.route('/recorrencia_detalhada/<int:recorrente_id>', methods=['GET'])
def recorrencia_detalhada(recorrente_id):
    try:
        dados = consulta_contas_recorrentes_id(recorrente_id)
        if not dados["conta"]:
            return jsonify({"erro": "Conta não encontrada"}), 404
        return jsonify(dados), 200
    except Exception as e:
        return jsonify({"erro": str(e)}), 500


@app.route('/contas/pagamentos', methods=['GET'])
@login_required
def contas_pagamentos():
    return render_template('contas_pagamentos.html')


@app.route('/get_pagamentos_por_mes', methods=['GET'])
@login_required
def get_pagamentos_por_mes_route():
    start = request.args.get('start')
    end = request.args.get('end')
    try:
        user_id = session.get("user_id")
        if user_id in [110]:
            contas = get_pagamentos_luanca(start, end)
        else:
            contas = get_pagamentos_por_mes(start, end)
        return jsonify(contas), 200
    except Exception as e:
        logging.error(f"Erro ao buscar contas por período: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/get_pagamento/<int:conta_id>/<int:recorrencia_id>', methods=['GET'])
def get_pagamento(conta_id, recorrencia_id):
    try:
        conta = get_conta_by_id(conta_id, recorrencia_id)
        if conta:
            return jsonify(conta)
        else:
            return jsonify({"error": "Conta ou recorrência não encontrada"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500


def upload_pagamentos_s3(file_path, original_filename):
    try:
        # 🔹 Gera um nome único para o arquivo
        file_extension = os.path.splitext(original_filename)[1]
        unique_filename = f"{uuid.uuid4().hex}{file_extension}"

        S3_BUCKET = "intranet-pagamentos"

        s3_client = boto3.client('s3',
                         aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                         aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'))

        logging.debug(f"Renomeando arquivo para: {unique_filename}")

        # Determina o Content-Type
        content_type, _ = mimetypes.guess_type(file_path)
        if not content_type:
            content_type = "application/octet-stream"

        # Upload para o S3 sem ACL
        s3_client.upload_file(
            file_path, S3_BUCKET, unique_filename,
            ExtraArgs={'ContentType': content_type}  # 🔹 Removido 'ACL': 'public-read'
        )

        # URL do arquivo no S3
        url = f"https://{S3_BUCKET}.s3.amazonaws.com/{unique_filename}"
        logging.info(f"Upload realizado com sucesso: {url}")

        return {"success": True, "url": url}

    except NoCredentialsError:
        logging.error("Credenciais AWS não disponíveis")
        return {"success": False, "error": "Credenciais não disponíveis"}
    except ClientError as e:
        logging.error(f"Erro ao fazer upload do arquivo para o S3: {str(e)}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logging.error(f"Erro inesperado ao fazer upload: {str(e)}")
        return {"success": False, "error": str(e)}


@app.route('/update_pagamento/<int:recorrencia_id>', methods=['PATCH'])
def update_pagamento(recorrencia_id):
    try:
        data_pagamento = request.form.get('data_pagamentos')
        file = request.files.get('file')
        comprovante_filename = None

        if file:
            # 🔹 Criando caminho temporário seguro
            temp_dir = tempfile.gettempdir()
            safe_filename = file.filename.replace(" ", "_")  # Remove espaços do nome do arquivo
            temp_path = os.path.join(temp_dir, safe_filename)

            # 🔹 Salvando o arquivo temporariamente
            file.save(temp_path)
            logging.info(f"Arquivo salvo temporariamente: {temp_path}")

            # 🔹 Upload para o S3
            upload_response = upload_pagamentos_s3(temp_path, file.filename)

            if upload_response["success"]:
                comprovante_filename = upload_response["url"]

            # 🔹 Removendo arquivo temporário
            os.remove(temp_path)

        # 🔹 Chama a função que atualiza o pagamento no banco de dados
        atualizar_pagamento(recorrencia_id, data_pagamento, comprovante_filename)

        user_id = session.get("user_id")
        record_log(user_id, "Pagou a conta e adicionou o comprovante")

        logging.info(f"Pagamento atualizado com sucesso para recorrencia_id {recorrencia_id}")

        return jsonify({
            "success": True,
            "message": "Pagamento atualizado com sucesso!",
            "data": {
                "recorrencia_id": recorrencia_id,
                "data_pagamentos": data_pagamento,
                "comprovante_filename": comprovante_filename
            }
        })

    except Exception as e:
        logging.error(f"Erro ao atualizar pagamento: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/parametros', methods=['GET'])
@login_required
def parametros():
    return render_template('parametros.html')


@app.route('/api/get_metas', methods=['GET'])
@login_required
def get_metas():
    try:
        metas = get_metas_gerais()
        return jsonify(metas), 200
    except Exception as e:
        logging.error(f"Erro ao buscar metas: {e}")
        return jsonify([]), 500


@app.route('/api/update_meta/<int:meta_id>', methods=['PATCH'])
@login_required
def update_meta_api(meta_id):
    try:
        dados = request.json
        if not dados:
            return jsonify({"error": "Nenhum dado fornecido para atualização"}), 400

        valor = dados.get("valor")

        update_meta(meta_id, valor)
        return jsonify({"message": "Meta atualizada com sucesso!"}), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 404
    except Exception as e:
        logging.error(f"Erro ao atualizar a meta: {e}")
        return jsonify({"error": "Erro ao atualizar a meta"}), 500


@app.route('/api/get_datas_globais', methods=['GET'])
def rota_get_datas_globais():
    try:
        data = get_datas_globais()
        return jsonify(data)
    except Exception as e:
        return jsonify({"erro": str(e)}), 500


@app.route('/api/update_datas_globais/<int:data_id>', methods=['PATCH'])
@login_required
def update_data_globais(data_id):
    try:
        dados = request.json
        if not dados:
            return jsonify({"error": "Nenhum dado fornecido para atualização"}), 400

        data_inicio = dados.get("data_inicio")
        data_fim = dados.get("data_fim")

        update_datas_globais(data_id, data_inicio, data_fim)
        return jsonify({"message": "Datas globais atualizadas com sucesso!"}), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 404
    except Exception as e:
        logging.error(f"Erro ao atualizar as datas globais: {e}")
        return jsonify({"error": "Erro ao atualizar as datas globais"}), 500


@app.route('/api/get_metasporvida_operadora', methods=['GET'])
@login_required
def get_metasporvida_operadora_api():
    try:
        metas = get_metasporvida_operadora_metas()
        return jsonify(metas), 200
    except Exception as e:
        logging.error(f"Erro ao buscar metas por vida operadora: {e}")
        return jsonify([]), 500


@app.route('/api/update_metas_vidas_operadora/<operadora>', methods=['PATCH'])
@login_required
def update_metas_vidas_operadora_api(operadora):
    try:
        dados = request.json
        if not dados:
            return jsonify({"error": "Nenhum dado fornecido para atualização"}), 400

        valor = dados.get("valor")
        update_metasporvida_operadora_metas(operadora, valor)
        return jsonify({"message": "Meta por vida operadora atualizada com sucesso!"}), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 404
    except Exception as e:
        logging.error(f"Erro ao atualizar meta por vida operadora: {e}")
        return jsonify({"error": "Erro ao atualizar meta por vida operadora"}), 500


@app.route('/api/get_metas_vidas_assistentes', methods=['GET'])
@login_required
def get_metas_vidas_assistentes_api():
    try:
        metas = get_metas_vidas_assistentes_metas()
        return jsonify(metas), 200
    except Exception as e:
        logging.error(f"Erro ao buscar metas por vida assistente: {e}")
        return jsonify([]), 500


@app.route('/api/update_metas_vidas_assistente/<assistente>/<operadora>', methods=['PATCH'])
@login_required
def update_metas_vidas_assistente_api(assistente, operadora):
    try:
        dados = request.json
        if not dados:
            return jsonify({"error": "Nenhum dado fornecido para atualização"}), 400

        valor = dados.get("valor")
        update_metas_vidas_assistentes_metas(assistente, operadora, valor)
        return jsonify({"message": "Meta por vida assistente atualizada com sucesso!"}), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 404
    except Exception as e:
        logging.error(f"Erro ao atualizar meta por vida assistente: {e}")
        return jsonify({"error": "Erro ao atualizar meta por vida assistente"}), 500


@app.route('/')
@app.route('/projects_dashboard')
@login_required
def projects_dashboard():
    active_page = 'dashboard'
    try:
        user_id = session.get("user_id")
        user_type = session.get("user_type")

        if user_type in [1, 7]:
            projetos = get_all_projetos()
        else:
            projetos = get_projetos_usuario(user_id)
    except Exception as e:
        logging.error("Erro ao listar projetos: %s", e)
        flash("Ocorreu um erro ao carregar a lista de projetos.", "danger")
        projetos = []
    return render_template('projects_dashboard.html', active_page=active_page, projetos=projetos)


@app.route('/project_add', methods=['GET', 'POST'])
@login_required
def project_add():
    active_page = 'project_add'
    if request.method == 'POST':
        try:
            if "user_id" not in session:
                flash("Você precisa estar logado para criar um projeto.", "danger")
                return redirect(url_for("index"))
            responsavel = session.get("user_id")
            nome_projeto = request.form.get('projectName')
            descricao = request.form.get('projectDescription')
            justificativa = request.form.get('projectJustification') or None
            prioridade = request.form.get('projectPriority') or None
            complexidade = request.form.get('projectComplexity') or None
            data_inicio = request.form.get('projectStartDate') or None
            data_entrega = request.form.get('projectDeadline') or None
            status = request.form.get('projectStatus') or 'backlog/pendencias'
            add_projeto(nome_projeto, descricao, justificativa, responsavel,
                        prioridade, complexidade, data_inicio, data_entrega, status)
            flash("Projeto cadastrado com sucesso!", "success")
            return redirect(url_for('projects_dashboard'))
        except Exception as e:
            logging.error("Erro ao cadastrar projeto: %s", e)
            flash("Ocorreu um erro ao cadastrar o projeto.", "danger")
            return redirect(url_for('project_add'))
    else:
        return render_template('project_add.html', active_page=active_page)


@app.route('/project_edit')
@login_required
def project_edit():
    active_page = 'project_edit'
    return render_template('project_edit.html', active_page=active_page)


@app.route('/project_details')
@login_required
def project_details_all():
    active_page = 'project_details'
    try:
        user_id = session.get("user_id")
        projetos = get_projetos_usuario(user_id)
        equipe_dict = {}
        tarefas_dict = {}
        for proj in projetos:
            project_id = proj[0]
            equipe_dict[project_id] = get_team_by_project_id_dict(project_id)
            tarefas = get_tarefas_by_projeto(project_id)
            grouped = {'a_fazer': [], 'em_andamento': [], 'concluido': []}
            for t in tarefas:
                tracking = True if t[10] in [True, 'true', 'True'] else False
                st = t[7].lower() if t[7] else 'a_fazer'
                if tracking:
                    st = 'em_andamento'
                grouped[st].append({
                    "id": t[0],
                    "id_projeto": t[1],
                    "titulo": t[3],
                    "descricao": t[4],
                    "data_inicio": t[5],
                    "data_fim": t[6],
                    "status": st,
                    "esta_em_tempo_tracking": tracking,
                    "tempo_gasto": t[12],
                    "prioridade": t[13],
                    "complexidade": t[14],
                    "responsavel_nome": t[15]
                })
            tarefas_dict[project_id] = grouped

        usuarios = consulta_todos_usuarios()
        users = [{"id": u[0], "name": u[1]} for u in usuarios] if usuarios else []
    except Exception as e:
        logging.error("Erro ao carregar detalhes dos projetos: %s", e)
        flash("Erro ao carregar detalhes dos projetos.", "danger")
        return redirect(url_for("projects_dashboard"))
    return render_template("project_details_all.html",
                           active_page=active_page,
                           projetos=projetos,
                           equipe_dict=equipe_dict,
                           users=users,
                           tarefas_dict=tarefas_dict)


@app.route('/project/<int:project_id>/add_team_members', methods=['POST'])
@login_required
def add_team_members(project_id):
    selected_team_members = request.form.getlist('team_members')
    try:
        for member_id in selected_team_members:
            add_team_member_to_project(projeto_id=project_id, usuario_id=member_id)
            logging.debug("Membro %s adicionado ao projeto %s", member_id, project_id)
        flash("Membros da equipe adicionados com sucesso!", "success")
    except Exception as e:
        logging.error("Erro ao adicionar membros da equipe: %s", e)
        flash("Ocorreu um erro ao adicionar os membros da equipe.", "danger")
    return redirect(url_for('project_details_all'))


@app.route('/tarefas_add', methods=['POST'])
def tarefas_add():
    if "user_id" not in session:
        flash("Você precisa estar logado para adicionar tarefas.", "danger")
        return redirect(url_for("projects_dashboard"))
    try:
        projeto_id = request.form.get("projeto_id")  # Se for tarefa vinculada a projeto, esse campo virá preenchido.
        titulo = request.form.get("titulo")
        descricao = request.form.get("descricao")
        data_inicio = request.form.get("data_inicio") or None
        data_fim = request.form.get("data_fim") or None
        # Captura os novos campos enviados pelo formulário:
        prioridade = request.form.get("prioridade") or None
        complexidade = request.form.get("complexidade") or None
        id_criador = session.get("user_id")
        tarefa_id = add_tarefa(projeto_id, titulo, descricao, id_criador, "a_fazer",
                                data_inicio, data_fim, prioridade, complexidade)
        flash("Tarefa adicionada com sucesso!", "success")
    except Exception as e:
        logging.error("Erro ao adicionar tarefa: %s", e)
        flash("Erro ao adicionar tarefa.", "danger")
    return redirect(url_for("project_details_all"))


@app.route('/tarefas/<int:tarefa_id>/iniciar', methods=['POST'])
def iniciar_tarefa(tarefa_id):
    if "user_id" not in session:
        flash("Você precisa estar logado para acessar essa página.", "danger")
        return redirect(url_for("index"))
    try:
        update_tarefa_tracking(tarefa_id, True)
        flash("Time tracking iniciado para a tarefa.", "success")
    except Exception as e:
        logging.error("Erro ao iniciar time tracking: %s", e)
        flash("Erro ao iniciar time tracking.", "danger")
    return redirect(url_for("project_details_all"))


@app.route('/tarefas/<int:tarefa_id>/finalizar', methods=['POST'])
def finalizar_tarefa(tarefa_id):
    if "user_id" not in session:
        flash("Você precisa estar logado para acessar essa página.", "danger")
        return redirect(url_for("index"))
    try:
        update_tarefa_tracking(tarefa_id, False)
        flash("Time tracking finalizado para a tarefa.", "success")
    except Exception as e:
        logging.error("Erro ao finalizar time tracking: %s", e)
        flash("Erro ao finalizar time tracking.", "danger")
    return redirect(url_for("project_details_all"))


@app.route('/project_delete/<int:project_id>', methods=['POST'])
def project_delete(project_id):
    if "user_id" not in session:
        flash("Você precisa estar logado para acessar essa página.", "danger")
        return redirect(url_for("index"))
    return render_template('project_delete.html', project_id=project_id)


@app.route('/editar_resumo/<int:projeto_id>', methods=['POST'])
def editar_resumo(projeto_id):
    if "user_id" not in session:
        flash("Você precisa estar logado para acessar essa página.", "danger")
        return redirect(url_for("index"))
    try:
        titulo = request.form.get('titulo')
        descricao = request.form.get('descricao')
        data_entrega = request.form.get('data_entrega')
        status = request.form.get('status')
        update_projeto(projeto_id, titulo, descricao, data_entrega, status)
        flash("Resumo atualizado com sucesso!", "success")
    except Exception as e:
        logging.error("Erro ao atualizar resumo do projeto: %s", e)
        flash("Erro ao atualizar o resumo do projeto.", "danger")
    return redirect(url_for('project_details_all'))


@app.route('/editar_tarefa/<int:tarefa_id>', methods=['POST'])
def editar_tarefa(tarefa_id):
    if "user_id" not in session:
        flash("Você precisa estar logado para acessar essa página.", "danger")
        return redirect(url_for("index"))
    try:
        titulo = request.form.get('titulo')
        descricao = request.form.get('descricao')
        data_fim = request.form.get('data_fim')
        prioridade = request.form.get('prioridade')
        complexidade = request.form.get('complexidade')
        update_tarefa(tarefa_id, titulo, descricao, data_fim, prioridade, complexidade)
        flash("Tarefa atualizada com sucesso!", "success")
    except Exception as e:
        logging.error("Erro ao atualizar tarefa: %s", e)
        flash("Erro ao atualizar a tarefa.", "danger")
    return redirect(url_for('project_details_all'))


@app.route('/adicionar_membro_tarefa/<int:tarefa_id>', methods=['POST'])
def adicionar_membro_tarefa(tarefa_id):
    if "user_id" not in session:
        flash("Você precisa estar logado para acessar essa página.", "danger")
        return redirect(url_for("index"))
    try:
        membro_id = request.form.get('membro_tarefa')
        if not membro_id:
            flash("Nenhum membro selecionado.", "warning")
            return redirect(url_for('project_details_all'))
        add_membro_a_tarefa(tarefa_id, membro_id)
        flash("Membro adicionado à tarefa com sucesso!", "success")
    except Exception as e:
        logging.error("Erro ao adicionar membro à tarefa: %s", e)
        flash("Erro ao adicionar membro à tarefa.", "danger")
    return redirect(url_for('project_details_all'))


@app.route('/project_tasks')
def project_tasks():
    active_page = 'project_tasks'
    user_id = session.get("user_id")
    if not user_id:
        flash("Você precisa estar logado para acessar suas tarefas.", "danger")
        return redirect(url_for('index'))
    try:
        tasks = get_user_tasks(user_id)
        projetos = get_all_projetos()
        usuarios = consulta_todos_usuarios()
        users = [{"id": u[0], "name": u[1]} for u in usuarios] if usuarios else []
    except Exception as e:
        logging.error("Erro ao buscar dados: %s", e)
        flash("Ocorreu um erro ao carregar suas tarefas/projetos.", "danger")
        tasks = []
        projetos = []
        users = []
    return render_template(
        "project_tasks.html",
        active_page=active_page,
        tasks=tasks,
        projetos=projetos,
        users=users
    )


@app.route('/project_calendar')
def project_calendar():
    active_page = 'project_calendar'
    return render_template('project_calendar.html', active_page=active_page)


@app.route('/api/calendar_events')
def calendar_events():
    if "user_id" not in session:
        return jsonify([])
    user_id = session.get("user_id")
    tasks = get_user_tasks(user_id)
    events = []
    for t in tasks:
        start = convert_date_to_iso(t[5])
        end   = convert_date_to_iso(t[6])
        if not start:
            continue
        event = {
            "id": t[0],
            "title": t[3],
            "start": start,
            "end": end if end else start,
            "extendedProps": {
                "descricao": t[4],
                "status": t[7],
                "responsaveis": t[13]
            }
        }
        events.append(event)
    return jsonify(events)


@app.route('/independent_task', methods=['POST'])
def independent_task():
    if "user_id" not in session:
        flash("Você precisa estar logado para acessar essa página.", "danger")
        return redirect(url_for("index"))
    try:
        titulo = request.form.get('titulo')
        descricao = request.form.get('descricao')
        data_inicio = request.form.get('data_inicio') or None
        data_fim = request.form.get('data_fim') or None
        prioridade = request.form.get('prioridade') or "Não definido"
        complexidade = request.form.get('complexidade') or "Não definido"
        id_criador = session.get("user_id")
        tarefa_id = add_tarefa(None, titulo, descricao, id_criador, "a_fazer", data_inicio, data_fim, prioridade, complexidade)
        flash("Tarefa independente adicionada com sucesso!", "success")
    except Exception as e:
        logging.error("Erro ao adicionar tarefa independente: %s", e)
        flash("Erro ao adicionar tarefa independente.", "danger")
    return redirect(url_for("project_tasks"))


@app.route("/projetos_v2")
@login_required
def projetos_v2():
    """Rota para renderizar a página de projetos v2."""
    if "user_id" not in session:
        return redirect(url_for("index"))
    return render_template('projeto_v2.html')


@app.route('/analytics_projects')
def analytics_projects():
    # 1) Buscamos todos os projetos para calculos rápidos
    projetos = get_all_projetos()

    # 2) KPIs de projetos (já existentes ou usamos get_projetos_kpis())
    total_projetos_abertos = sum(1 for p in projetos if p[10] == 'aberto')
    total_projetos_progresso = sum(1 for p in projetos if p[10] == 'em_progresso')
    total_projetos_concluidos = sum(1 for p in projetos if p[10] == 'concluido')
    total_projetos_atrasados = sum(1 for p in projetos if p[10] == 'atrasado')

    # 3) KPIs de tarefas
    tarefas_kpis = get_tarefas_kpis()
    total_tarefas = tarefas_kpis["total_tarefas"]
    total_tarefas_concluidas = tarefas_kpis["total_concluidas"]
    total_tarefas_atrasadas = tarefas_kpis["total_atrasadas"]
    tarefas_em_andamento = tarefas_kpis["em_andamento"]
    tempo_medio_conclusao = tarefas_kpis["tempo_medio_conclusao"]
    tempo_medio_em_andamento = tarefas_kpis["tempo_medio_em_andamento"]

    # 4) Ranking de usuários
    ranking_usuarios = get_ranking_usuarios()

    # 5) KPIs estratégicos (já existiam)
    kpis = get_kpis_estrategicos()
    percentual_no_prazo = round(kpis["percentual_no_prazo"], 2)
    velocidade_media = kpis["velocidade_media"]

    # 6) Dados para gráfico de tendência (se quiser continuar usando)
    try:
        tendencias = get_tendencias_mensais()  # se já implementado
    except:
        tendencias = []
    meses = [t['mes'] for t in tendencias]
    projetos_concluidos_mensal = [t['projetos_concluidos'] for t in tendencias]
    tarefas_concluidas_mensal = [t['tarefas_concluidas'] for t in tendencias]

    # 7) Gráfico de status (projetos)
    labels_status = ["Abertos", "Em Progresso", "Concluídos", "Atrasados"]
    data_status = [
        total_projetos_abertos,
        total_projetos_progresso,
        total_projetos_concluidos,
        total_projetos_atrasados
    ]

    try:
        prioridades_rows = get_tarefas_por_prioridade()
    except:
        prioridades_rows = []
    labels_prioridades = []
    data_prioridades = []
    for (prio, qtd) in prioridades_rows:
        labels_prioridades.append(prio.capitalize() if prio else "Não Definida")
        data_prioridades.append(qtd)

    return render_template(
        "analytics_projects.html",
        active_page='analytics_projects',

        # Projetos
        total_projetos_abertos=total_projetos_abertos,
        total_projetos_progresso=total_projetos_progresso,
        total_projetos_concluidos=total_projetos_concluidos,
        total_projetos_atrasados=total_projetos_atrasados,

        # Tarefas
        total_tarefas=total_tarefas,
        total_tarefas_concluidas=total_tarefas_concluidas,
        total_tarefas_atrasadas=total_tarefas_atrasadas,
        tarefas_em_andamento=tarefas_em_andamento,
        tempo_medio_conclusao=tempo_medio_conclusao,
        tempo_medio_em_andamento=tempo_medio_em_andamento,

        # Extra
        percentual_no_prazo=percentual_no_prazo,
        velocidade_media=velocidade_media,
        previsao_atrasos_futuros=25,  # ou crie lógica real
        ranking_usuarios=ranking_usuarios,

        # Gráficos
        labels_status=labels_status,
        data_status=data_status,
        labels_prioridades=labels_prioridades,
        data_prioridades=data_prioridades,
        meses=meses,
        projetos_concluidos_mensal=projetos_concluidos_mensal,
        tarefas_concluidas_mensal=tarefas_concluidas_mensal
    )


@app.route("/api/projetos_v2", methods=['GET'])
@login_required
def listar_projetos_v2():
    """Rota para listar projetos v2."""
    return jsonify({'message': 'Funcionalidade em desenvolvimento'}), 501


@app.route('/metas-vidas')
def metas_vidas():
    return render_template('metas-vidas.html', active_page="relatorio-gerenciais")


@app.route('/metas-vidas/overview')
def metas_vidas_overview():
    return render_template('overview-vidas.html', active_page="relatorio-gerenciais")


@app.route('/metas-vidas/assistentes')
def metas_vidas_assistentes():
    return render_template('metas-vidas-assistentes.html', active_page="relatorio-gerenciais")


@app.route('/metas-vidas/operadoras')
def metas_vidas_operadoras():
    return render_template('metas-vidas-operadoras.html', active_page="relatorio-gerenciais")


@app.route("/api/metas-vidas-operadora", methods=["GET"])
@login_required
def get_metas_vidas_operadora():
    try:
        resultado = get_metasporvida_operadora()
        return jsonify({
            "status": "success",
            "data": resultado
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Erro ao buscar metas por vida das operadoras: {str(e)}"
        }), 500


def fetch_api_metas_vidas_assistentes():
    global dt_inicio_global, dt_final_global
    dt_inicio = dt_inicio_global
    dt_final = dt_final_global

    if not dt_inicio or not dt_final:
        logging.error("Datas globais não configuradas")
        return []

    url = "https://sisweb-api.azurewebsites.net/api/sumarios/producao"
    params = {
        "DtInicio": dt_inicio,
        "DtFinal": dt_final,
        "ProducaoDataTipo": "Producao",
        "ProducaoMultinota": "Todas",
        "SumarioGroupBy": ["Assistente", "Operadora"],
        "SumarioOptions": [
            "Producao",
            "Empresarial",
        ],
        "SumarioStatus": [
            "PreCadastro",
            "Implantada",
            "AguardandoImplantacao",
            "AcompanharImplantacao",
            "AguardandoRegularizacao",
            "Inadimplente",
            "AguardandoOperadora",
        ],
        "Pagina": 1,
        "RegistrosPorPagina": 100,
    }
    headers = {
        "accept": "application/json",
        "ApiKey": os.getenv("API_KEY")
    }

    # Lista de operadoras que queremos filtrar
    operadoras_alvo = [
        "Alice",
        "Amil",
        "Bradesco Seguros",
        "Medsênior",
        "Notre Dame Intermédica",
        "Qualicorp",
        "Sulamérica",
        "Supermed",
        "Porto Seguro",
        "Seguros Unimed",
        "Omint"
    ]

    # Para mapear possíveis variações de nomes das operadoras
    operadoras_map = {
        "GNDI": "Notre Dame Intermédica",
        "SUL AMÉRICA": "Sulamérica",
        "SULAMERICA": "Sulamérica",
        "MEDSENIOR": "Medsênior",
        "PORTO": "Porto Seguro"
    }

    all_data = []

    try:
        # Primeira requisição para obter o número total de páginas
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        total_paginas = data.get("result", {}).get("paginaInfo", {}).get("totalPaginas", 1)

        logging.info(f"Buscando dados de metas por vida dos assistentes. Total de páginas: {total_paginas}")

        # Data formatada para o banco de dados
        mes_ano = f"{dt_inicio.split('/')[2]}-{dt_inicio.split('/')[0]}-01"

        # Agora fazemos requisições para todas as páginas
        for pagina in range(1, total_paginas + 1):
            params["Pagina"] = pagina
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()

            page_data = response.json()
            items = page_data.get("result", {}).get("collection", [])

            # Filtramos apenas os dados que precisamos e que são das operadoras alvo
            for item in items:
                operadora_nome = item.get("operadoraNome", "")

                # Normalizar nome da operadora
                operadora_normalizada = operadoras_map.get(operadora_nome.upper(), operadora_nome)

                if operadora_normalizada in operadoras_alvo:
                    all_data.append({
                        "assistente": item.get("assistente", ""),
                        "operadora": operadora_normalizada,
                        "metas_vidas": item.get("vidaCount", 0),
                        "mesAno": mes_ano
                    })

            logging.info(f"Página {pagina}/{total_paginas} processada com sucesso.")

            # Pausa para não sobrecarregar a API
            time.sleep(1)

        # Inserir ou atualizar os dados no banco de dados
        if all_data:
            if update_or_insert_metas_vidas_assistente(all_data):
                logging.info(f"Dados de metas por vida dos assistentes atualizados com sucesso: {len(all_data)} registros")
            else:
                logging.error("Erro ao atualizar dados no banco de dados")

        return all_data

    except Exception as e:
        logging.error(f"Erro ao buscar dados de metas por vida dos assistentes: {e}")
        return []


@app.route('/api/mesesanos-meta-vidas-assistente-api', methods=['GET'])
@login_required
def get_mesesanos_meta_vidas_assistente_api():
    try:
        meses = consulta_mesesanos_meta_vidas_assistente_api()
        logging.info(f"Meses: {meses}")
        return jsonify({
            'status': 'success',
            'data': meses
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route("/api/metas-vidas-assistentes", methods=["GET"])
@login_required
def get_metas_vidas_assistentes():
    try:
        # Pega a data do query parameter
        mesano = request.args.get('mesano')

        if not mesano:
            return jsonify({
                "status": "error",
                "message": "Parâmetro mesano é obrigatório"
            }), 400

        resultado = consulta_metas_vidas_comparativo(mesano)
        return jsonify({
            "status": "success",
            "data": resultado
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Erro ao buscar metas por vida dos assistentes: {str(e)}"
        }), 500

@app.route('/api/last-execution', methods=['GET'])
def get_last_execution_route():
    try:
        result = get_last_execution()
        if result:
            return jsonify({
                'status': 'success',
                'data': result
            }), 200
        return jsonify({
            'status': 'error',
            'message': 'Nenhuma execução encontrada'
        }), 404
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


if __name__ == "__main__":
    # fetch_and_update_all_data() # Modelo de como testar uma def da API
    socketio.run(app, debug=True)
# Teste
