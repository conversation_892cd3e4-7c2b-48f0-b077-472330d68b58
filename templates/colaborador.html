{% extends "base.html" %}

{% block title %}Área do Colaborador{% endblock %}

{% block body_class %}colaborador{% endblock %}

{% block content %}

<div id="alertBox" class="alert d-none" role="alert">
    <!-- Mensagem de alerta será inserida aqui -->
</div>
<div class="container-fluid">
    <div class="row">
        <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky">
                <ul class="nav flex-column">
                    {% if session.get('user_type') in [1, 5, 7] and session.get('setor_id') not in [9, 10] %}
                    <li class="nav-item">
                        <a class="nav-link active" href="#" id="acessosLink">
                            <i class="fa-solid fa-link"></i>
                            Acesso - Portais
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] and session.get('setor_id') not in [9, 10]
                    %}
                    <a class="nav-link" href="#" id="reembolsoLink">
                        <i class="fas fa-money-check-alt"></i>
                        Reembolso
                    </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [1, 5, 7] %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="beneficiosLink">
                            <i class="fa-solid fa-clipboard-check"></i>
                            Meus Benefícios
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [7] %}
                    <li class="nav-item">
                        <a class="nav-link" href="/breve" id="chatButton">
                            <i class="fa-regular fa-comments"></i>
                            Chat
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="ramaisLink">
                            <i class="fa-regular fa-address-book"></i>
                            Ramais
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [7] %}
                    <li class="nav-item">
                        <a class="nav-link" href="/breve" id="hierarquia">
                            <i class="fa-solid fa-sitemap"></i>
                            Organograma
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [7] %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="sugestoescolaborador">
                            <i class="fa-solid fa-envelope-open-text"></i>
                            Sugestões
                        </a>
                    </li>
                    {% endif %}

                    {% if has_permission_fastmoney or session.get('user_type') in [1, 2, 3, 4, 7] %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="fastmoneyLink">
                            <i class="fa-solid fa-hand-holding-dollar"></i>
                            Fast e Adiantamento
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] and session.get('setor_id') not in [9, 10] %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="boletos_corretores">
                            <i class="fa-solid fa-file-invoice-dollar"></i>
                            Boletos 1ª Parcela
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="ticketLink">
                            <i class="fa-solid fa-envelope-circle-check"></i>
                            Abrir Chamados
                        </a>
                    </li>
                    {% endif %}

                    {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="seuChamadoLink">
                            <i class="fa-solid fa-person-circle-exclamation"></i>
                            Acompanhe seu chamado
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </nav>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div
                class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2" style="
                    color: black;
                ">Área do Colaborador</h1>
            </div>
            <div id="mainContent">
                <!-- Conteúdo da página "Acompanhe seu chamado" será carregado aqui -->
            </div>
        </main>
    </div>
</div>

<script src="{{ url_for('static', filename='js/reembolso_colaborador.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/acessos_colaborador.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/beneficios_colaborador.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/ramais_colaborador.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/fastmoney.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/ticket_colaborador.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/boletos_corretores.js') }}" defer></script>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const seuChamadoLink = document.getElementById('seuChamadoLink');
        const ticketLink = document.getElementById('ticketLink');
        const mainContent = document.getElementById('mainContent');

        seuChamadoLink.addEventListener('click', function (event) {
            event.preventDefault();

            // Verifica se o iframe já existe para evitar recriação
            let chamadoIframe = document.getElementById('chamadoIframe');

            if (!chamadoIframe) {
                chamadoIframe = document.createElement('iframe');
                chamadoIframe.id = 'chamadoIframe';
                chamadoIframe.style.width = "100%";
                chamadoIframe.style.height = "80vh";
                chamadoIframe.style.border = "none";

                mainContent.innerHTML = "";
                mainContent.appendChild(chamadoIframe);
            }

            // Define a URL da rota e exibe o iframe
            chamadoIframe.src = "/colaborador/chamados";
            chamadoIframe.style.display = "block";
        });

        ticketLink.addEventListener('click', function (event) {
            event.preventDefault();

            // Verifica se o iframe já existe para evitar recriação
            let ticketIframe = document.getElementById('ticketIframe');

            if (!ticketIframe) {
                ticketIframe = document.createElement('iframe');
                ticketIframe.id = 'ticketIframe';
                ticketIframe.style.width = "90%";
                ticketIframe.style.height = "80vh";
                ticketIframe.style.border = "none";

                mainContent.innerHTML = "";
                mainContent.appendChild(ticketIframe);
            }

            // Define a URL da rota e exibe o iframe
            ticketIframe.src = "/abertura_ticket";
            ticketIframe.style.display = "block";
        });
    });
</script>
<script>
    const permissions = {
        hasPermissionReembolso: JSON.parse('{{ has_permission_reembolso | tojson }}'),
        hasPermissionFastmoney: JSON.parse('{{ has_permission_fastmoney | tojson }}'),
        hasPermissionComissao: JSON.parse('{{ has_permission_comissao | tojson }}'),
        userType: JSON.parse('{{ user_type | tojson }}')
    };

    console.log("Permissões carregadas:", permissions);
</script>

{% endblock %}