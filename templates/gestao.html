{% extends "base.html" %}

{% block title %}Intranet | BrazilHealth{% endblock %}

{% block body_class %}gestao{% endblock %}

{% block content %}
<div class="gestao">
    <div class="header-gestao">
        <h1>Painel de Gestão</h1>
    </div>
    <div class="gestao-container">
        {% if has_permission_reembolso or session.get('user_id') in [110, 149, 218, 220] or session.get('user_type') in
        [1,
        5, 7]
        and session.get('setor_id') != 9%}
        <div class="cardboard" data-content="gestao-reembolso" onclick="window.location.href='/gestao/chamados'">
            <div class="cardboard-header">
                <i class="fa-solid fa-person-circle-exclamation"></i>
                <h3>Gestão de Chamados</h3>
            </div>
            <div class="cardboard-content">
                <p>Gerencie os chamados do seu setor de forma eficiente e segura.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if has_permission_comissao or session.get('user_type') in [1, 7]%}
        <div class="cardboard" data-content="dashboard-resumo" onclick="loadContent('dashboard-resumo')">
            <div class="cardboard-header">
                <i class="fas fa-coins"></i>

                <h3>Gestão de Comissões</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Acesse e gerencie as comissões da sua equipe de forma eficiente e detalhada.
                </p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if has_permission_reembolso or session.get('user_type') in [1, 3, 4, 5, 7] and session.get('setor_id') not in
        [9, 10, 12]%}
        <div class="cardboard" data-content="gestao-reembolso" onclick="window.location.href='/gestao-de-reembolso'">
            <div class="cardboard-header">
                <i class="fas fa-money-check-alt"></i>
                <h3>Gestão de Reembolso</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Gerencie os processos de reembolso de forma eficiente e segura.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 2, 3, 4, 7] %}
        <div class="cardboard" data-content="tabela-comissoes" onclick="openModal('tabela-comissoes')">
            <div class="cardboard-header">
                <i class="fas fa-table"></i>
                <h3>Gestão de Tabelas de Comissão</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Consulte a alterações de comissões, tabelas e premiações.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 7] %}
        <div class="cardboard" data-content="gestao-politicas"
            onclick="window.location.href='/politicas-beneficios-rh'">
            <div class="cardboard-header">
                <i class="fas fa-file-alt"></i>
                <h3>Políticas de Benefícios BRH</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Entenda todas as políticas de benefícios oferecidas pela empresa.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if has_permission_fastmoney or session.get('user_type') in [1, 2, 3, 4, 7] %}
        <div class="cardboard" onclick="window.location.href='/gestao-fastmoney'">
            <div class="cardboard-header">
                <i class="fas fa-coins"></i>
                <h3>Gestão de Fast-Money</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Gerencie as solicitações de adiantamento de comissão (Fast-Money).</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 7] %}
        <div class="cardboard" data-content="gestao-corretores" onclick="window.location.href='/gestao_corretores'">
            <div class="cardboard-header">
                <i class="fas fa-users"></i>
                <h3>Gestão de Corretores</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Gerencie e acompanhe os dados dos corretores.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 7] %}
        <div class="cardboard" data-content="comunicacao-equipe-ramais" onclick="window.location.href='/gestao_ramais'">
            <div class="cardboard-header">
                <i class="fas fa-users-cog"></i>
                <h3>Comunicação com Equipe</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Comunique-se com as áreas, equipes, verifique ramais e departamentos do grupo
                    BRH</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 7] %}
        <div class="cardboard" data-content="compliance-regulamentacao"
            onclick="loadContent('compliance-regulamentacao')">
            <div class="cardboard-header">
                <i class="fas fa-gavel"></i>
                <h3>Compliance e Regulamentação</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Monitore e assegure conformidade com as regulamentações vigentes.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 7] %}
        <div class="cardboard">
            <div class="cardboard-header">
                <i class="fas fa-folder-open"></i>
                <h3>Portal de Documentos</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Acesse documentos importantes e compartilhe com a equipe.</p>
            </div>
            <div class="cardboard-footer">
                <a href="{{ url_for('portal_documentos') }}" class="gestao-btn-acessar">Acessar</a>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 7] %}
        <div class="cardboard" data-content="integracoes" onclick="window.location.href='/gestao_integracao'">
            <div class="cardboard-header">
                <i class="fas fa-plug"></i>
                <h3>Integrações</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Conecte-se com outros sistemas e plataformas.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 7] %}
        <div class="cardboard" data-content="suporte-treinamento" onclick="loadContent('suporte-treinamento')">
            <div class="cardboard-header">
                <i class="fas fa-life-ring"></i>
                <h3>Suporte e Treinamento</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Obtenha suporte técnico e acesse recursos de treinamento.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if session.get('user_type') in [1, 2, 3, 4, 5, 7] and session.get('setor_id') not in
        [9, 10]%}
        <div class="cardboard" data-content="acessos" onclick="openModal('acessos')">
            <div class="cardboard-header">
                <i class="fa-solid fa-key"></i>
                <h3>Login e Senha</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Gerencie suas senhas de acesso de forma prática e segura por aqui.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if has_permission_comissao or session.get('user_type') in [1, 7]%}
        <div class="cardboard" data-content="upload-acompanhamento"
            onclick="window.location.href='/gestao/upload-acompanhamento'">
            <div class="cardboard-header">
                <i class="fa-solid fa-file-arrow-up"></i>
                <h3>Upload - Acompanhamento Amil</h3>
            </div>
            <div class="cardboard-content">
                <p>Faça o upload do arquivo de acompanhamento de vendas Amil.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if has_permission_boleto or session.get('user_type') in [1, 2, 3, 4, 7] %}
        <div class="cardboard" data-content="gestao-boletos"
            onclick="window.location.href='/gestao/boletos/corretores'">
            <div class="cardboard-header">
                <i class="fas fa-receipt"></i>
                <h3>Gestão de Boletos de Corretores</h3>
            </div>
            <div class="cardboard-content">
                <p class="paragrafogestao">Gerencie todos os boletos submetidos pelos corretores, incluindo pagamentos e
                    uploads de comprovantes.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if has_permission_comissao or session.get('user_type') in [1, 7]%}
        <div class="cardboard" data-content="processo-seletivo"
            onclick="window.location.href='/gestao/novo-processo-seletivo'">
            <div class="cardboard-header">
                <i class="fa-brands fa-wpforms"></i>
                <h3>Criar Processo Seletivo</h3>
            </div>
            <div class="cardboard-content">
                <p>Crie um novo processo seletivo interno.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

        {% if has_permission_comissao or session.get('user_type') in [1, 7]%}
        <div class="cardboard" data-content="processo-seletivo"
            onclick="window.location.href='/gestao/processos_seletivos'">
            <div class="cardboard-header">
                <i class="fa-solid fa-users-rays"></i>
                <h3>Gestão de Candidatos</h3>
            </div>
            <div class="cardboard-content">
                <p>Gerêncie as canditaduras dos processos seletivos.</p>
            </div>
            <div class="cardboard-footer">
                <button class="gestao-btn-acessar">Acessar</button>
            </div>
        </div>
        {% endif %}

    </div>
    <div id="content-container" class="content-container">
        <!-- O conteúdo será carregado aqui -->
    </div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modal-body">
                <!-- Conteúdo inicial da modal -->
                <div class="cardboard" onclick="loadForm('portal')">
                    <div class="cardboard-header">
                        <i class="fa-solid fa-desktop"></i>
                        <h3>Cadastrar Portal</h3>
                    </div>
                    <div class="cardboard-content">
                        <p class="paragrafogestao">Cadastre um novo portal para acesso.</p>
                    </div>
                    <div class="cardboard-footer">
                        <button class="gestao-btn-acessar">Cadastrar</button>
                    </div>
                </div>
                <div class="cardboard" onclick="loadForm('acesso')">
                    <div class="cardboard-header">
                        <i class="fas fa-key"></i>
                        <h3>Cadastrar Acesso</h3>
                    </div>
                    <div class="cardboard-content">
                        <p class="paragrafogestao">Cadastre novos acessos para os portais.</p>
                    </div>
                    <div class="cardboard-footer">
                        <button class="gestao-btn-acessar">Cadastrar</button>
                    </div>
                </div>
                <div class="cardboard">
                    <div class="cardboard-header">
                        <i class="fas fa-key"></i>
                        <h3>Login e Senhas</h3>
                    </div>
                    <div class="cardboard-content">
                        <p class="paragrafogestao">Acesse seus logins e senhas.</p>
                    </div>
                    <div class="cardboard-footer">
                        <button class="gestao-btn-acessar">Acessar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modal-body">
                <!-- Conteúdo inicial da modal -->
                <div class="cardboard" id="tabelas">
                    <div class="cardboard-header">
                        <i class="fa-solid fa-file-arrow-up"></i>
                        <h3>Gerencie as Tabelas de Comissão</h3>
                    </div>
                    <div class="cardboard-content">
                        <p class="paragrafogestao">Faça o upload ou update da tabela de comissão.</p>
                    </div>
                    <div class="cardboard-footer">
                        <button class="gestao-btn-acessar">Acessar</button>
                    </div>
                </div>

                <div class="cardboard" id="tabelas">
                    <div class="cardboard-header">
                        <i class="fa-solid fa-file-invoice-dollar"></i>
                        <h3>Tabela de Comissão</h3>
                    </div>
                    <div class="cardboard-content">
                        <p class="paragrafogestao">Acesse a tabela de comissão.</p>
                    </div>
                    <div class="cardboard-footer">
                        <button class="gestao-btn-acessar">Acessar</button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div id="loadingScreen" class="loading-screen" style="display: none;">
        <div class="spinner"></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    const permissions = {
        hasPermissionReembolso: JSON.parse('{{ has_permission_reembolso | tojson }}'),
        hasPermissionFastmoney: JSON.parse('{{ has_permission_fastmoney | tojson }}'),
        hasPermissionComissao: JSON.parse('{{ has_permission_comissao | tojson }}'),
        userType: JSON.parse('{{ user_type | tojson }}')
    };

    console.log("Permissões carregadas:", permissions);
</script>

<script src="{{ url_for('static', filename='js/gestao.js') }}"></script>
{% endblock %}